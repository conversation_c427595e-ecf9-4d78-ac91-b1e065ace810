let level0obstacles = [];

function handleObstacles() {
    for (let i = 135; i < 142; i++) {
        level0obstacles.push(i);
    }
    for (let i = 96; i < 108; i++) {
        level0obstacles.push(i);
    }
    for (let i = 276; i < 280; i++) {
        level0obstacles.push(i);
    }
    for (let i = 252; i < 256; i++) {
        level0obstacles.push(i);
    }
    for (let i = 226; i < 232; i++) {
        level0obstacles.push(i);
    }
}
handleObstacles();

let level1obstacles = [];

function handleObstacles1() {
    for (let i = 87; i < 114; i++) {
        level1obstacles.push(i);
    }
    for (let i = 171; i < 578; i += 29) {
        level1obstacles.push(i);
    }
    for (let i = 148; i < 526; i += 29) {
        level1obstacles.push(i);
    }
    for (let i = 120; i < 527; i += 29) {
        level1obstacles.push(i);
    }
}
handleObstacles1()

var controlLayer = [
    Level0 = {
        "標題": "第零幕 獨行",
        "標題圖": "./Public/Title/0.png",
        "BGM": "./Music/2.mp3",
        "地圖背景": "./Public/Map/0.png",
        "戰鬥背景": "./Battle/0.png",
        "width": "181.5%",//165
        "height": "192.5%",//175
        size: {
            cols: 24,
            rows: 16
        },
        Players: [
            { id: "player1", Position: 262, OldPosition: 262, lastdirect: "left" },
            { id: "player0", Position: 238, OldPosition: 238, lastdirect: "left" }
        ],
        Enemys: [

            { ...Enemy0, Position: 260, lastdirect: "right" },
            //{ ...Enemy0, Position: 259,lastdirect: "right"  },
            //{ ...Enemy0, Position: 153,lastdirect: "right"  },
            //{ ...Enemy1, Position: 258 ,lastdirect: "right" }
        ],
        Treasures: [{ "寶物": Fittings["妖磷石"], "位置": 235 }],
        Obstacles: [...level0obstacles, 109, 110, 202, 203, 204, 166, 167, 190, 191, 287, 309, 310, 311, 332, 333, 353, 354, 355, 376, 377],
        "勝利訊息": "擊敗所有敵人(60分)",
        "失敗訊息": "殷劍平陣亡,",
        "成就訊息": "拿到2個燧石(+40分)，拿到1個燧石(+20分)，拿到0個燧石(+0分)",
        // 第零幕的多波增援示例
        AdditionEnemys: [],
        startmovie: "./Movie/0.mp4",
        endhasmovie: "./Movie/1.mp4",
        onVictoryRedirect: 'hostel'
    },
    Level1 = {
        "標題": "第壹幕 因緣",
        "標題圖": "./Public/Title/1.png",
        "BGM": "./Music/1.mp3",
        "地圖背景": "./Public/Map/1.png",
        "戰鬥背景": "./Battle/1.png",
        "width": "235%",
        "height": "240%",
        size: {
            cols: 29,
            rows: 20
        },
        Players: [
            { id: "player1", Position: 392, OldPosition: 392, lastdirect: "up" },
            { id: "player2", Position: 362, OldPosition: 362, lastdirect: "up" }
        ],
        Enemys: [
            { ...Enemy1, Position: 131, lastdirect: "down" },
            { ...Enemy1, Position: 156, lastdirect: "down" },
            { ...Enemy1, Position: 245, lastdirect: "down" },
            { ...Enemy1, Position: 163, lastdirect: "down" }
        ],
        Treasures: [{ "寶物": Fittings["妖磷石"], "位置": 397 }, { "寶物": Fittings["妖磷石"], "位置": 437 }],
        Obstacles: [...level1obstacles, 208, 238, 268, 297, 326, 355, 384, 383, 382, 408, 436, 464, 241, 242, 243, 270, 271, 272, 299, 300, 301, 247, 248, 249, 276, 277, 278, 305, 306, 307, 417, 418, 419, , 446, 447, 448, 475, 476, 477, 253, 254, 255, 282, 283, 284, 311, 312, 313, 424, 425, 426, 453, 454, 455, 482, 483, 484, 136, 137, 138, 124, 125, 126, 127],
        "勝利訊息": "擊敗所有敵人(60分)",
        "失敗訊息": "殷劍平或封寒月陣亡,",
        "成就訊息": "拿到紫蘊精玉(+20分)，受到敵人兩次法術攻擊(+20分)，殷劍平與封寒月達到位階8以上(20分)",
        AdditionEnemys: [
            {
                turn: 2,
                enemys: [
                    { ...Enemy1, Position: 131 },
                    { ...Enemy1, Position: 156 }
                ],
                msg: [
                    {
                        context: "第一波敵方增援已到達！"
                    }
                ]
            },
            {
                turn: 4,
                enemys: [
                    { ...Enemy1, Position: 245 },
                    { ...Enemy1, Position: 163 }
                ],
                msg: [
                    {
                        context: "第二波增援來勢洶洶！"
                    },
                    {
                        context: "戰況變得更加激烈了..."
                    }
                ]
            },
            {
                turn: 6,
                enemys: [
                    { ...Enemy0, Position: 200 },
                    { ...Enemy0, Position: 201 },
                    { ...Enemy1, Position: 202 }
                ],
                msg: [
                    {
                        context: "最後一波增援！"
                    },
                    {
                        context: "敵人傾巢而出，小心應對！"
                    }
                ]
            }
        ],
        startmovie: null,
        endhasmovie: null,
        // 勝利後導向設定：'camp' = 營地場景, 'hostel' = 客棧場景
        onVictoryRedirect: "camp"
    },
    Level1 = {
        "標題": "第壹幕 測試客棧",
        "標題圖": "./Public/Title/1.png",
        "BGM": "./Music/1.mp3",
        "地圖背景": "./Public/Map/1.png",
        "戰鬥背景": "./Battle/1.png",
        "width": "181.5%",
        "height": "192.5%",
        size: {
            cols: 24,
            rows: 16
        },
        Players: [
            { id: "player1", Position: 100, OldPosition: 100 },
            { id: "player0", Position: 76, OldPosition: 76 }
        ],
        Enemys: [
            { ...Enemy0, Position: 120 }
        ],
        Treasures: [
            {
                "位置": 125,
                "寶物": {
                    name: "測試寶物",
                    type: "道具",
                    description: "測試用寶物"
                }
            }
        ],
        "勝利訊息": "測試關卡通關！",
        "失敗訊息": "測試失敗，請重新挑戰！",
        "成就訊息": "獲得測試成就！",
        reinforcements: [],
        startmovie: null,
        endhasmovie: null,
        // 勝利後導向設定：這個關卡勝利後會導向客棧
        onVictoryRedirect: "hostel"
    },

];

console.log(controlLayer[0]);

// ===== 增援管理工具函數 =====

/**
 * 手動觸發增援（調試用）
 * @param {number} levelIndex - 關卡索引
 * @param {number} reinforcementIndex - 增援索引（用於多波增援）
 */
function triggerReinforcement(levelIndex = currentLevel, reinforcementIndex = 0) {
    const levelData = controlLayer[levelIndex];

    if (levelData.AdditionEnemys) {
        // 檢查是否為陣列格式（多波增援）
        if (Array.isArray(levelData.AdditionEnemys)) {
            if (levelData.AdditionEnemys[reinforcementIndex]) {
                console.log(`手動觸發關卡 ${levelIndex} 的第 ${reinforcementIndex + 1} 波增援`);
                operates.spawnEnemyReinforcements(levelData.AdditionEnemys[reinforcementIndex]);
                return;
            } else {
                console.warn(`關卡 ${levelIndex} 沒有第 ${reinforcementIndex + 1} 波增援`);
                return;
            }
        } else {
            // 單次增援格式
            console.log(`手動觸發關卡 ${levelIndex} 的增援`);
            operates.spawnEnemyReinforcements(levelData.AdditionEnemys);
            return;
        }
    }

    if (levelData.MultipleReinforcements && levelData.MultipleReinforcements[reinforcementIndex]) {
        console.log(`手動觸發關卡 ${levelIndex} 的第 ${reinforcementIndex + 1} 波增援（MultipleReinforcements）`);
        operates.spawnEnemyReinforcements(levelData.MultipleReinforcements[reinforcementIndex]);
        return;
    }

    console.warn(`關卡 ${levelIndex} 沒有可觸發的增援`);
}

/**
 * 獲取關卡增援信息
 * @param {number} levelIndex - 關卡索引
 * @returns {object} - 增援信息
 */
function getReinforcementInfo(levelIndex = currentLevel) {
    const levelData = controlLayer[levelIndex];
    const info = {
        levelIndex: levelIndex,
        levelTitle: levelData["標題"],
        hasReinforcements: false,
        reinforcements: []
    };

    // 檢查 AdditionEnemys（支持單次和多波增援）
    if (levelData.AdditionEnemys) {
        info.hasReinforcements = true;

        // 檢查是否為陣列格式（多波增援）
        if (Array.isArray(levelData.AdditionEnemys)) {
            levelData.AdditionEnemys.forEach((reinforcement, index) => {
                info.reinforcements.push({
                    type: 'addition_multiple',
                    wave: index + 1,
                    turn: reinforcement.turn,
                    enemyCount: reinforcement.enemys ? reinforcement.enemys.length : 0,
                    messages: reinforcement.msg ? reinforcement.msg.length : 0
                });
            });
        } else {
            // 單次增援格式
            info.reinforcements.push({
                type: 'addition_single',
                turn: levelData.AdditionEnemys.turn,
                enemyCount: levelData.AdditionEnemys.enemys ? levelData.AdditionEnemys.enemys.length : 0,
                messages: levelData.AdditionEnemys.msg ? levelData.AdditionEnemys.msg.length : 0
            });
        }
    }

    // 檢查多波增援
    if (levelData.MultipleReinforcements) {
        info.hasReinforcements = true;
        levelData.MultipleReinforcements.forEach((reinforcement, index) => {
            info.reinforcements.push({
                type: 'multiple',
                wave: index + 1,
                turn: reinforcement.turn,
                enemyCount: reinforcement.enemys ? reinforcement.enemys.length : 0,
                messages: reinforcement.msg ? reinforcement.msg.length : 0
            });
        });
    }

    // 檢查條件增援
    if (levelData.ConditionalReinforcements) {
        info.hasReinforcements = true;
        levelData.ConditionalReinforcements.forEach((reinforcement) => {
            info.reinforcements.push({
                type: 'conditional',
                condition: reinforcement.condition.type,
                conditionValue: reinforcement.condition.value,
                enemyCount: reinforcement.enemys ? reinforcement.enemys.length : 0,
                messages: reinforcement.msg ? reinforcement.msg.length : 0,
                triggered: reinforcement.triggered || false
            });
        });
    }

    return info;
}

/**
 * 列出所有關卡的增援信息
 */
function listAllReinforcements() {
    console.log("=== 所有關卡增援信息 ===");
    controlLayer.forEach((_, index) => {
        const info = getReinforcementInfo(index);
        if (info.hasReinforcements) {
            console.log(`關卡 ${index}: ${info.levelTitle}`);
            info.reinforcements.forEach(reinforcement => {
                console.log(`  - ${reinforcement.type} 增援: ${reinforcement.enemyCount} 個敵人`);
            });
        }
    });
}

// 將工具函數添加到全局作用域，方便調試
window.triggerReinforcement = triggerReinforcement;
window.getReinforcementInfo = getReinforcementInfo;
window.listAllReinforcements = listAllReinforcements;

// ===== 關卡管理函數 =====

/**
 * 進入下一關的函數
 * @param {number} currentLevelIndex - 當前關卡索引
 * @returns {boolean} - 是否成功進入下一關
 */
function goToNextLevel(currentLevelIndex = currentLevel) {
    console.log(`嘗試進入下一關，當前關卡: ${currentLevelIndex}`);

    const nextLevelIndex = currentLevelIndex + 1;

    // 檢查下一關是否存在
    if (nextLevelIndex >= controlLayer.length) {
        console.warn("已經是最後一關，無法進入下一關");
        alert("恭喜！你已經完成了所有關卡！");
        return false;
    }

    // 檢查場景管理器是否可用
    if (typeof sceneManager === 'undefined') {
        console.error("場景管理器不可用，無法切換關卡");
        return false;
    }

    // 使用場景管理器切換到下一關
    try {
        console.log(`準備進入關卡 ${nextLevelIndex}: ${controlLayer[nextLevelIndex]["標題"]}`);
        sceneManager.switchToLevel(nextLevelIndex);
        return true;
    } catch (error) {
        console.error("切換關卡時發生錯誤:", error);
        return false;
    }
}

/**
 * 進入指定關卡的函數
 * @param {number} levelIndex - 目標關卡索引
 * @returns {boolean} - 是否成功進入關卡
 */
function goToLevel(levelIndex) {
    console.log(`嘗試進入指定關卡: ${levelIndex}`);

    // 檢查關卡是否存在
    if (levelIndex < 0 || levelIndex >= controlLayer.length) {
        console.warn(`關卡索引 ${levelIndex} 超出範圍 (0-${controlLayer.length - 1})`);
        return false;
    }

    // 檢查場景管理器是否可用
    if (typeof sceneManager === 'undefined') {
        console.error("場景管理器不可用，無法切換關卡");
        return false;
    }

    // 使用場景管理器切換到指定關卡
    try {
        console.log(`準備進入關卡 ${levelIndex}: ${controlLayer[levelIndex]["標題"]}`);
        sceneManager.switchToLevel(levelIndex);
        return true;
    } catch (error) {
        console.error("切換關卡時發生錯誤:", error);
        return false;
    }
}

/**
 * 回到營地的函數
 * @param {number} campIndex - 營地索引，默認為當前關卡對應的營地
 * @returns {boolean} - 是否成功回到營地
 */
function goToCamp(campIndex = currentLevel) {
    console.log(`嘗試回到營地，營地索引: ${campIndex}`);

    // 檢查場景管理器是否可用
    if (typeof sceneManager === 'undefined') {
        console.error("場景管理器不可用，無法切換到營地");
        return false;
    }

    // 使用場景管理器切換到營地
    try {
        console.log(`準備進入營地 ${campIndex}`);
        sceneManager.switchToCamp(campIndex);
        return true;
    } catch (error) {
        console.error("切換到營地時發生錯誤:", error);
        return false;
    }
}

/**
 * 獲取關卡信息的函數
 * @param {number} levelIndex - 關卡索引
 * @returns {object|null} - 關卡數據或null
 */
function getLevelInfo(levelIndex) {
    if (levelIndex < 0 || levelIndex >= controlLayer.length) {
        console.warn(`關卡索引 ${levelIndex} 超出範圍`);
        return null;
    }

    return {
        index: levelIndex,
        title: controlLayer[levelIndex]["標題"],
        titleImage: controlLayer[levelIndex]["標題圖"],
        bgm: controlLayer[levelIndex]["BGM"],
        mapBackground: controlLayer[levelIndex]["地圖背景"],
        battleBackground: controlLayer[levelIndex]["戰鬥背景"],
        size: controlLayer[levelIndex].size,
        playersCount: controlLayer[levelIndex].Players.length,
        enemysCount: controlLayer[levelIndex].Enemys.length,
        treasuresCount: controlLayer[levelIndex].Treasures.length,
        victoryMessage: controlLayer[levelIndex]["勝利訊息"],
        failureMessage: controlLayer[levelIndex]["失敗訊息"],
        achievementMessage: controlLayer[levelIndex]["成就訊息"]
    };
}

/**
 * 獲取所有關卡列表的函數
 * @returns {array} - 所有關卡的基本信息
 */
function getAllLevels() {
    return controlLayer.map((level, index) => ({
        index: index,
        title: level["標題"],
        titleImage: level["標題圖"]
    }));
}

/**
 * 檢查是否可以進入下一關的函數
 * @param {number} currentLevelIndex - 當前關卡索引
 * @returns {boolean} - 是否可以進入下一關
 */
function canGoToNextLevel(currentLevelIndex = currentLevel) {
    const nextLevelIndex = currentLevelIndex + 1;
    return nextLevelIndex < controlLayer.length;
}

// ===== 便捷函數 =====

/**
 * 快速進入下一關（全局函數）
 */
window.nextLevel = function () {
    return goToNextLevel();
};

/**
 * 快速進入指定關卡（全局函數）
 */
window.gotoLevel = function (levelIndex) {
    return goToLevel(levelIndex);
};

/**
 * 快速回到營地（全局函數）
 */
window.gotoCamp = function (campIndex) {
    return goToCamp(campIndex);
};

/**
 * 進入客棧的函數
 * @param {number} hostelIndex - 客棧索引，默認為當前關卡對應的客棧
 * @param {string} previousScene - 來源場景，用於決定角色位置和選單選項
 * @returns {boolean} - 是否成功進入客棧
 */
function goToHostel(hostelIndex = currentLevel, previousScene = null) {
    console.log(`嘗試進入客棧，客棧索引: ${hostelIndex}，來源場景: ${previousScene}`);

    // 檢查場景管理器是否可用
    if (typeof sceneManager === 'undefined') {
        console.error("場景管理器不可用，無法切換到客棧");
        return false;
    }

    // 使用場景管理器切換到客棧
    try {
        console.log(`準備進入客棧 ${hostelIndex}`);
        sceneManager.switchToHostel(hostelIndex, previousScene);
        return true;
    } catch (error) {
        console.error("切換到客棧時發生錯誤:", error);
        return false;
    }
}

/**
 * 快速進入客棧（全局函數）
 */
window.gotoHostel = function (hostelIndex, previousScene) {
    return goToHostel(hostelIndex, previousScene);
};

// 導出函數供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        goToNextLevel,
        goToLevel,
        goToCamp,
        getLevelInfo,
        getAllLevels,
        canGoToNextLevel,
        controlLayer
    };
}