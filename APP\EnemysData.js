const Enemy0 = {
    name: "夜魑",
    "是否電腦操作": true,
    lastdirect: "down",
    hitframe: 0.1,
    level: 1,
    HP: 120,
    CurHP: 120,
    MP: 0,
    CurMP: 0,
    EXP: 0,
    CurEXP: 0,
    "是否蓄力": false,
    "是否釋放蓄力": false,
    "已增加移動力": false,
    ATK: 65,//45
    ATKRange: 1,
    AvoidRate: 20, //20
    HitRate: 110,
    CriticalHitRate: 45,
    Move: 4,
    DEF: 8,
    Position: null,
    "五內": {
        "迅": 0,
        "烈": 0,
        "神": 0,
        "魔": 0,
        "魂": 0
    },
    "法抗": {
        "火": -25,
        "雷": -15,
        "冰": -5,
        "幻": -15,
        "華": -25,
        "冥": -15
    },
    "法術": [],
    Equipment: {
        Weapon: null,
        Armor: null,
        Fitting: null
    },
    // 背包
    Inventory: [Fittings["燧石"]],
    RewardItems: [Fittings["燧石"]],
    "圖片": "./Public/Enemys/0/man.gif",
    width: "132",
    height: "122",
    Stand: {
        up: ["./Public/Enemys/0/Stand/up/0.png", "./Public/Enemys/0/Stand/up/1.png", "./Public/Enemys/0/Stand/up/2.png", "./Public/Enemys/0/Stand/up/3.png"],
        down: ["./Public/Enemys/0/Stand/down/0.png", "./Public/Enemys/0/Stand/down/1.png", "./Public/Enemys/0/Stand/down/2.png", "./Public/Enemys/0/Stand/down/3.png"],
        left: ["./Public/Enemys/0/Stand/left/0.png", "./Public/Enemys/0/Stand/left/1.png", "./Public/Enemys/0/Stand/left/2.png", "./Public/Enemys/0/Stand/left/3.png"],
        right: ["./Public/Enemys/0/Stand/right/0.png", "./Public/Enemys/0/Stand/right/1.png", "./Public/Enemys/0/Stand/right/2.png", "./Public/Enemys/0/Stand/right/3.png"],
    },
    MoveRes: {
        up: ["./Public/Enemys/0/Move/up/0.png", "./Public/Enemys/0/Move/up/1.png", "./Public/Enemys/0/Move/up/2.png", "./Public/Enemys/0/Move/up/3.png"],
        down: ["./Public/Enemys/0/Move/down/0.png", "./Public/Enemys/0/Move/down/1.png", "./Public/Enemys/0/Move/down/2.png", "./Public/Enemys/0/Move/down/3.png"],
        left: ["./Public/Enemys/0/Move/left/0.png", "./Public/Enemys/0/Move/left/1.png", "./Public/Enemys/0/Move/left/2.png", "./Public/Enemys/0/Move/left/3.png"],
        right: ["./Public/Enemys/0/Move/right/0.png", "./Public/Enemys/0/Move/right/1.png", "./Public/Enemys/0/Move/right/2.png", "./Public/Enemys/0/Move/right/3.png"],
    },
    AtkRes: {
        animates: ["./Public/Enemys/0/ATK/0.png", "./Public/Enemys/0/ATK/1.png", "./Public/Enemys/0/ATK/2.png", "./Public/Enemys/0/ATK/3.png", "./Public/Enemys/0/ATK/4.png", "./Public/Enemys/0/ATK/5.png", "./Public/Enemys/0/ATK/6.png", "./Public/Enemys/0/ATK/7.png", "./Public/Enemys/0/ATK/8.png", "./Public/Enemys/0/ATK/9.png", "./Public/Enemys/0/ATK/10.png", "./Public/Enemys/0/ATK/11.png", "./Public/Enemys/0/ATK/12.png", "./Public/Enemys/0/ATK/13.png", "./Public/Enemys/0/ATK/14.png", "./Public/Enemys/0/ATK/15.png", "./Public/Enemys/0/ATK/16.png", "./Public/Enemys/0/ATK/17.png"],
        sound: "./Public/Enemys/0/ATK/ATK.mp3",
        x: 0,
        y: 0,
    },
    "遭受傷害": {
        animates: ["./Public/Enemys/0/MISC/1.png"],
        x: 100,
        y: 30,
    },
    AtkMiss: {
        animates: ["./Public/Enemys/0/ATKB/0.png", "./Public/Enemys/0/ATKB/1.png", "./Public/Enemys/0/ATKB/2.png", "./Public/Enemys/0/ATKB/3.png", "./Public/Enemys/0/ATKB/4.png", "./Public/Enemys/0/ATKB/5.png", "./Public/Enemys/0/ATKB/6.png", "./Public/Enemys/0/ATKB/7.png", "./Public/Enemys/0/ATKB/8.png", "./Public/Enemys/0/ATKB/9.png", "./Public/Enemys/0/ATKB/10.png", "./Public/Enemys/0/ATKB/11.png", "./Public/Enemys/0/ATKB/12.png", "./Public/Enemys/0/ATKB/13.png", "./Public/Enemys/0/ATKB/14.png", "./Public/Enemys/0/ATKB/15.png", "./Public/Enemys/0/ATKB/16.png", "./Public/Enemys/0/ATKB/17.png"],
        sound: "./Public/Enemys/0/ATKB/ATKB.mp3",
        x: 0,
        y: 0,
    },
    BattleIdleRes: {
        animates: ["./Public/Enemys/0/NORMAL/0.png", "./Public/Enemys/0/NORMAL/1.png", "./Public/Enemys/0/NORMAL/2.png", "./Public/Enemys/0/NORMAL/3.png"],
        x: 0,
        y: 0,
    },
    bloodfun: function (enemydamage, player) {
        return new Promise((resolve) => {
            let tempdamge = enemydamage / 3
            let originalHP = player.CurHP;  // 保存原始HP值

            let playerbattlehpbar = document.getElementById("playerbattlehpbar");
            let playerhpinner = document.getElementById("playerbattlehp-inner");
            let playerhpinnerdelay = document.getElementById("playerbattlehpbar_inner");
            let playerhptext = document.getElementById("playerbattlehptext");
            // Start from current HP
            playerhpinner.style.width = `${(originalHP / player.HP) * 490}px`;
            playerhptext.textContent = `${Math.floor(originalHP)} / ${player.HP}`;
            playerhpinnerdelay.style.width = `${(originalHP / player.HP) * 100}%`;

            setTimeout(() => {
                playerhpinner.style.width = `${(Math.max(0, originalHP - tempdamge) / player.HP) * 490}px`;
                playerhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge) / player.HP) * 100}%`;
                playerhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge))} / ${player.HP}`;
                playerbattlehpbar.style.animation = "lll 0.3s linear forwards";
                setTimeout(() => {
                    playerbattlehpbar.style.animation = "null";
                }, 100)
                setTimeout(() => {
                    // Show first damage
                    playerhpinner.style.width = `${(Math.max(0, originalHP - tempdamge * 2) / player.HP) * 490}px`;
                    playerhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge * 2) / player.HP) * 100}%`;
                    playerhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge * 2))} / ${player.HP}`;
                    playerbattlehpbar.style.animation = "lll 0.3s linear forwards";
                    setTimeout(() => {
                        playerbattlehpbar.style.animation = "null";
                    }, 100)
                    setTimeout(() => {
                        // Show final damage
                        playerhpinner.style.width = `${(Math.max(0, originalHP - enemydamage) / player.HP) * 490}px`;
                        playerhpinnerdelay.style.width = `${(Math.max(0, originalHP - enemydamage) / player.HP) * 100}%`;
                        playerhptext.textContent = `${Math.floor(Math.max(0, originalHP - enemydamage))} / ${player.HP}`;
                        player.CurHP = Math.max(0, originalHP - enemydamage);  // 只在動畫完成後更新實際HP
                        playerbattlehpbar.style.animation = "lll 0.3s linear forwards";
                        setTimeout(() => {
                            playerbattlehpbar.style.animation = "null";
                            resolve(player.CurHP <= 0); // 返回玩家是否死亡
                        }, 100)

                    }, 600)
                }, 300)
            }, 300)
        })
    }
}

const Enemy1 = {
    name: "四象門術",
    "是否電腦操作": true,
    lastdirect: "down",
    hitframe: 0.5,
    level: 1,
    HP: 120,
    CurHP: 120,
    MP: 20,
    CurMP: 20,
    EXP: 0,
    CurEXP: 0,
    "是否蓄力": false,
    "是否釋放蓄力": false,
    "已增加移動力": false,
    ATK: 65,//45
    ATKRange: 2,
    AvoidRate: 20, //20
    HitRate: 110,
    CriticalHitRate: 45,
    Move: 4,
    DEF: 8,
    Position: null,
    "五內": {
        "迅": 0,
        "烈": 0,
        "神": 0,
        "魔": 0,
        "魂": 0
    },
    "法抗": {
        "火": 15,
        "雷": 25,
        "冰": 5,
        "幻": 5,
        "華": 15,
        "冥": 5
    },
    "法術": [{
        name: "離火神訣",
        type: "法術",
        Classify: "火系",
        describsion: "引離火之氣包於敵人身邊，可焚燒小群敵人，造成一般程度的創傷",
        Who: ["封寒月"],
        NeedMove: 0,
        NeedLV: 3,
        distance: 4,
        Range: 1,
        NeedMP: 10,
        NeedSoul: {
            "迅": 0,
            "烈": 0,
            "神": 0,
            "魔": 0,
            "魂": 0
        },
        Icon: "./Public/Magic/2/2.png",
        Animations: "./Public/Magic/0/",
        Sounds: ".Public/Magic/0/0.mp3",
        effect: function (playerMP) {
            const damageAmount = Math.floor(playerMP * 0.35 + 60);
            return {
                type: "fire",
                value: damageAmount,
                target: "enemy"
            };
        },
        exp: function getExpFromRemainingHp(remainingHp, maxHp) {
            const remainingRatio = remainingHp / maxHp;
            const maxExp = 70;
            const exp = 70 + Math.floor(maxExp * (1 - remainingRatio));
            return exp;
        }
    }, {
        name: "氣愈之術",
        type: "法術",
        Classify: "治癒",
        describsion: "最初級的療癒法術，集聚體內之氣於一處，可治癒較輕微的傷勢",
        Who: ["四象門術"],
        NeedMove: 0,
        NeedLV: 1,
        distance: 4,
        Rmdistance: 0,
        Range: 1,
        NeedMP: 15,
        NeedSoul: {
            "迅": 0,
            "烈": 0,
            "神": 0,
            "魔": 0,
            "魂": 0
        },
        Icon: "./Public/Magic/0/icon.png",
        Animaties: ["./Public/Magic/0/Animation/0.png", "./Public/Magic/0/Animation/1.png", "./Public/Magic/0/Animation/2.png", "./Public/Magic/0/Animation/3.png", "./Public/Magic/0/Animation/4.png", "./Public/Magic/0/Animation/5.png", "./Public/Magic/0/Animation/6.png", "./Public/Magic/0/Animation/7.png", "./Public/Magic/0/Animation/8.png", "./Public/Magic/0/Animation/9.png", "./Public/Magic/0/Animation/10.png", "./Public/Magic/0/Animation/11.png", "./Public/Magic/0/Animation/12.png", "./Public/Magic/0/Animation/13.png", "./Public/Magic/0/Animation/14.png", "./Public/Magic/0/Animation/15.png", "./Public/Magic/0/Animation/16.png", "./Public/Magic/0/Animation/17.png", "./Public/Magic/0/Animation/18.png", "./Public/Magic/0/Animation/19.png", "./Public/Magic/0/Animation/20.png", "./Public/Magic/0/Animation/21.png", "./Public/Magic/0/Animation/22.png", "./Public/Magic/0/Animation/23.png", "./Public/Magic/0/Animation/24.png", "./Public/Magic/0/Animation/25.png", "./Public/Magic/0/Animation/26.png", "./Public/Magic/0/Animation/27.png", "./Public/Magic/0/Animation/28.png"],
        EffAnimates: ["./Public/maneff/recover/0.png", "./Public/maneff/recover/1.png", "./Public/maneff/recover/2.png", "./Public/maneff/recover/3.png", "./Public/maneff/recover/4.png", "./Public/maneff/recover/5.png", "./Public/maneff/recover/6.png", "./Public/maneff/recover/7.png", "./Public/maneff/recover/8.png", "./Public/maneff/recover/9.png", "./Public/maneff/recover/10.png", "./Public/maneff/recover/11.png", "./Public/maneff/recover/12.png"],
        Sounds: "./Public/Magic/0/sound.mp3",
        effect: function (playerMP) {
            const healAmount = Math.floor(playerMP * 0.55 + 60);
            return {
                type: "heal",
                value: healAmount,
                target: "ally"
            };
        },
        exp: function calculateHealExp(healAmount, healerLevel, targetLevel) {
            // 等級差影響
            const levelDiff = healerLevel - targetLevel;

            if (levelDiff >= 6) {
                return 1; // 高出太多，只得1點
            }

            // 基礎經驗：補血量 * 40%
            return Math.floor(healAmount * 0.4);
        }
    }],
    Equipment: {
        Weapon: null,
        Armor: null,
        Fitting: null
    },
    // 背包
    Inventory: [],
    RewardItems: [Fittings["燧石"]],
    "圖片": "./Public/Enemys/1/man.gif",
    width: "90",
    height: "122",
    usemagic: ["./Public/Enemys/1/usemagic/0.png", "./Public/Enemys/1/usemagic/1.png"],
    Stand: {
        up: ["./Public/Enemys/1/Stand/up/0.png", "./Public/Enemys/1/Stand/up/1.png", "./Public/Enemys/1/Stand/up/2.png", "./Public/Enemys/1/Stand/up/3.png", "./Public/Enemys/1/Stand/up/4.png", "./Public/Enemys/1/Stand/up/5.png"],
        down: ["./Public/Enemys/1/Stand/down/0.png", "./Public/Enemys/1/Stand/down/1.png", "./Public/Enemys/1/Stand/down/2.png", "./Public/Enemys/1/Stand/down/3.png", "./Public/Enemys/1/Stand/down/4.png", "./Public/Enemys/1/Stand/down/5.png"],
        left: ["./Public/Enemys/1/Stand/left/0.png", "./Public/Enemys/1/Stand/left/1.png", "./Public/Enemys/1/Stand/left/2.png", "./Public/Enemys/1/Stand/left/3.png", "./Public/Enemys/1/Stand/left/4.png", "./Public/Enemys/1/Stand/left/5.png"],
        right: ["./Public/Enemys/1/Stand/right/0.png", "./Public/Enemys/1/Stand/right/1.png", "./Public/Enemys/1/Stand/right/2.png", "./Public/Enemys/1/Stand/right/3.png", "./Public/Enemys/1/Stand/right/4.png", "./Public/Enemys/1/Stand/right/5.png"],
    },
    MoveRes: {
        up: ["./Public/Enemys/1/Move/up/0.png", "./Public/Enemys/1/Move/up/1.png", "./Public/Enemys/1/Move/up/2.png", "./Public/Enemys/1/Move/up/3.png"],
        down: ["./Public/Enemys/1/Move/down/0.png", "./Public/Enemys/1/Move/down/1.png", "./Public/Enemys/1/Move/down/2.png", "./Public/Enemys/1/Move/down/3.png"],
        left: ["./Public/Enemys/1/Move/left/0.png", "./Public/Enemys/1/Move/left/1.png", "./Public/Enemys/1/Move/left/2.png", "./Public/Enemys/1/Move/left/3.png"],
        right: ["./Public/Enemys/1/Move/right/0.png", "./Public/Enemys/1/Move/right/1.png", "./Public/Enemys/1/Move/right/2.png", "./Public/Enemys/1/Move/right/3.png"],
    },
    AtkRes: {
        animates: ["./Public/Enemys/1/ATK/0.png", "./Public/Enemys/1/ATK/1.png", "./Public/Enemys/1/ATK/2.png", "./Public/Enemys/1/ATK/3.png", "./Public/Enemys/1/ATK/4.png", "./Public/Enemys/1/ATK/5.png", "./Public/Enemys/1/ATK/6.png", "./Public/Enemys/1/ATK/7.png", "./Public/Enemys/1/ATK/8.png", "./Public/Enemys/1/ATK/9.png", "./Public/Enemys/1/ATK/10.png", "./Public/Enemys/1/ATK/11.png", "./Public/Enemys/1/ATK/12.png", "./Public/Enemys/1/ATK/13.png", "./Public/Enemys/1/ATK/14.png", "./Public/Enemys/1/ATK/15.png", "./Public/Enemys/1/ATK/16.png", "./Public/Enemys/1/ATK/17.png", "./Public/Enemys/1/ATK/18.png", "./Public/Enemys/1/ATK/19.png"],
        sound: "./Public/Enemys/1/ATK/ATK.mp3",
        x: 80,
        y: 30,
    },
    "遭受傷害": {
        animates: ["./Public/Enemys/1/MISC/1.png"],
        x: 80,
        y: 30,
    },
    AtkMiss: {
        animates: ["./Public/Enemys/1/ATKB/0.png", "./Public/Enemys/1/ATKB/1.png", "./Public/Enemys/1/ATKB/2.png", "./Public/Enemys/1/ATKB/3.png", "./Public/Enemys/1/ATKB/4.png", "./Public/Enemys/1/ATKB/5.png", "./Public/Enemys/1/ATKB/6.png", "./Public/Enemys/1/ATKB/7.png", "./Public/Enemys/1/ATKB/8.png", "./Public/Enemys/1/ATKB/9.png", "./Public/Enemys/1/ATKB/10.png", "./Public/Enemys/1/ATKB/11.png"],
        sound: "./Public/Enemys/1/ATKB/ATKB.mp3",
        x: 80,
        y: 30,
    },
    BattleIdleRes: {
        animates: ["./Public/Enemys/1/NORMAL/0.png", "./Public/Enemys/1/NORMAL/1.png", "./Public/Enemys/1/NORMAL/2.png", "./Public/Enemys/1/NORMAL/3.png"],
        x: 80,
        y: 30,
    },
    bloodfun: function (enemydamage, player) {
        return new Promise((resolve) => {
            let tempdamge = enemydamage / 3
            let originalHP = player.CurHP;  // 保存原始HP值

            let playerbattlehpbar = document.getElementById("playerbattlehpbar");
            let playerhpinner = document.getElementById("playerbattlehp-inner");
            let playerhpinnerdelay = document.getElementById("playerbattlehpbar_inner");
            let playerhptext = document.getElementById("playerbattlehptext");
            // Start from current HP
            playerhpinner.style.width = `${(originalHP / player.HP) * 490}px`;
            playerhptext.textContent = `${Math.floor(originalHP)} / ${player.HP}`;
            playerhpinnerdelay.style.width = `${(originalHP / player.HP) * 100}%`;

            setTimeout(() => {
                playerhpinner.style.width = `${(Math.max(0, originalHP - tempdamge) / player.HP) * 490}px`;
                playerhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge) / player.HP) * 100}%`;
                playerhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge))} / ${player.HP}`;
                playerbattlehpbar.style.animation = "lll 0.3s linear forwards";
                setTimeout(() => {
                    playerbattlehpbar.style.animation = "null";
                }, 100)

                setTimeout(() => {
                    // Show final damage
                    playerhpinner.style.width = `${(Math.max(0, originalHP - enemydamage) / player.HP) * 490}px`;
                    playerhpinnerdelay.style.width = `${(Math.max(0, originalHP - enemydamage) / player.HP) * 100}%`;
                    playerhptext.textContent = `${Math.floor(Math.max(0, originalHP - enemydamage))} / ${player.HP}`;
                    player.CurHP = Math.max(0, originalHP - enemydamage);  // 只在動畫完成後更新實際HP
                    playerbattlehpbar.style.animation = "lll 0.3s linear forwards";
                    setTimeout(() => {
                        playerbattlehpbar.style.animation = "null";
                        resolve(player.CurHP <= 0); // 返回玩家是否死亡
                    }, 100)

                }, 600)

            }, 300)
        })
    }
}

