var mybag = [
    Weapons["長劍"],
    Weapons["長劍"],
    Armors["布衣"],
    Fittings["避邪玉珮"],
    Eatting["金創藥"]
];
var mymoney = 1000;

// ===== 戰場存取系統 (Battle Save/Load System) =====

/**
 * 戰場存取管理器 - 負責戰場狀態的保存和載入
 */
class BattleSaveManager {
    constructor() {
        this.maxSlots = 5; // 最多5個存檔槽
        this.storagePrefix = 'battleSave_'; // localStorage 前綴
    }

    // 獲取當前戰場狀態
    getCurrentBattleState() {
        console.log("收集當前戰場狀態");

        const battleState = {
            // 基本信息
            timestamp: Date.now(),
            currentLevel: typeof currentLevel !== 'undefined' ? currentLevel : 0,
            levelTitle: typeof controlLayer !== 'undefined' && controlLayer[currentLevel]
                ? controlLayer[currentLevel]["標題"]
                : "未知關卡",

            // 玩家狀態
            players: this.getPlayersState(),

            // 敵人狀態
            enemies: this.getEnemiesState(),

            // 遊戲狀態
            gameState: this.getGameState(),

            // 地圖狀態
            mapState: this.getMapState(),

            // 背包和金錢
            inventory: {
                mybag: typeof mybag !== 'undefined' ? [...mybag] : [],
                mymoney: typeof mymoney !== 'undefined' ? mymoney : 0
            }
        };

        console.log("戰場狀態收集完成:", battleState);
        return battleState;
    }

    // 獲取玩家狀態
    getPlayersState() {
        if (typeof gameplayers === 'undefined') {
            console.warn("gameplayers 未定義");
            return [];
        }

        return gameplayers.map(player => ({
            id: player.id,
            name: player.name,
            level: player.level,
            HP: player.HP,
            CurHP: player.CurHP,
            MP: player.MP,
            CurMP: player.CurMP,
            ATK: player.ATK,
            DEF: player.DEF,
            Move: player.Move,
            EXP: player.EXP,
            CurEXP: player.CurEXP,
            Position: player.Position,
            OldPosition: player.OldPosition,
            AvoidRate: player.AvoidRate,
            HitRate: player.HitRate,
            CriticalHitRate: player.CriticalHitRate,
            AlreadyMove: player.AlreadyMove,
            "是否蓄力": player["是否蓄力"],
            "是否釋放蓄力": player["是否釋放蓄力"],
            "已增加移動力": player["已增加移動力"],
            "是否電腦操作": player["是否電腦操作"],
            "點數": player["點數"],
            "五內": { ...player["五內"] },
            "法抗": { ...player["法抗"] },
            "法術": [...(player["法術"] || [])],
            Equipment: {
                Weapon: player.Equipment?.Weapon ? { ...player.Equipment.Weapon } : null,
                Armor: player.Equipment?.Armor ? { ...player.Equipment.Armor } : null,
                Fitting: player.Equipment?.Fitting ? { ...player.Equipment.Fitting } : null
            },
            Inventory: [...(player.Inventory || [])]
        }));
    }

    // 獲取敵人狀態
    getEnemiesState() {
        if (typeof gameenemys === 'undefined') {
            console.warn("gameenemys 未定義");
            return [];
        }

        return gameenemys.map(enemy => ({
            id: enemy.id,
            name: enemy.name,
            HP: enemy.HP,
            CurHP: enemy.CurHP,
            MP: enemy.MP,
            CurMP: enemy.CurMP,
            ATK: enemy.ATK,
            DEF: enemy.DEF,
            Position: enemy.Position,
            OldPosition: enemy.OldPosition,
            AlreadyMove: enemy.AlreadyMove,
            "是否電腦操作": enemy["是否電腦操作"],
            AvoidRate: enemy.AvoidRate,
            HitRate: enemy.HitRate,
            CriticalHitRate: enemy.CriticalHitRate,
            "法術": [...(enemy["法術"] || [])]
        }));
    }

    // 獲取遊戲狀態
    getGameState() {
        return {
            runOBJ: typeof runOBJ !== 'undefined' ? { ...runOBJ } : {},
            roundCount: typeof roundCount !== 'undefined' ? roundCount : 1,
            actionIndex: typeof actionIndex !== 'undefined' ? actionIndex : 0,
            isActionInProgress: typeof isActionInProgress !== 'undefined' ? isActionInProgress : false
        };
    }

    // 獲取地圖狀態
    getMapState() {
        return {
            treasures: typeof gametreasures !== 'undefined' ? [...gametreasures] : [],
            obstacles: typeof controlLayer !== 'undefined' && controlLayer[currentLevel]
                ? [...(controlLayer[currentLevel].Obstacles || [])]
                : []
        };
    }

    // 保存戰場狀態到指定槽位
    saveBattleState(slotIndex) {
        console.log(`保存戰場狀態到槽位 ${slotIndex}`);

        if (slotIndex < 0 || slotIndex >= this.maxSlots) {
            console.error("無效的存檔槽位:", slotIndex);
            return false;
        }

        try {
            const battleState = this.getCurrentBattleState();
            const storageKey = this.storagePrefix + slotIndex;

            localStorage.setItem(storageKey, JSON.stringify(battleState));
            console.log(`戰場狀態已保存到槽位 ${slotIndex}`);
            return true;
        } catch (error) {
            console.error("保存戰場狀態失敗:", error);
            return false;
        }
    }

    // 從指定槽位載入戰場狀態
    loadBattleState(slotIndex) {
        console.log(`從槽位 ${slotIndex} 載入戰場狀態`);

        if (slotIndex < 0 || slotIndex >= this.maxSlots) {
            console.error("無效的存檔槽位:", slotIndex);
            return null;
        }

        try {
            const storageKey = this.storagePrefix + slotIndex;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                console.warn(`槽位 ${slotIndex} 沒有保存的戰場狀態`);
                return null;
            }

            const battleState = JSON.parse(savedData);
            console.log(`戰場狀態已從槽位 ${slotIndex} 載入`);
            return battleState;
        } catch (error) {
            console.error("載入戰場狀態失敗:", error);
            return null;
        }
    }

    // 應用載入的戰場狀態
    applyBattleState(battleState) {
        console.log("應用戰場狀態");

        try {
            // 應用關卡
            if (typeof currentLevel !== 'undefined') {
                currentLevel = battleState.currentLevel;
            }

            // 應用玩家狀態
            this.applyPlayersState(battleState.players);

            // 應用敵人狀態
            this.applyEnemiesState(battleState.enemies);

            // 應用遊戲狀態
            this.applyGameState(battleState.gameState);

            // 應用地圖狀態
            this.applyMapState(battleState.mapState);

            // 應用背包和金錢
            this.applyInventoryState(battleState.inventory);

            console.log("戰場狀態應用完成");
            return true;
        } catch (error) {
            console.error("應用戰場狀態失敗:", error);
            return false;
        }
    }

    // 應用玩家狀態
    applyPlayersState(playersData) {
        if (typeof gameplayers === 'undefined') {
            console.warn("gameplayers 未定義，無法應用玩家狀態");
            return;
        }

        playersData.forEach((playerData, index) => {
            if (index < gameplayers.length) {
                const player = gameplayers[index];

                // 應用基本屬性
                Object.keys(playerData).forEach(key => {
                    if (key !== 'Equipment' && key !== 'Inventory') {
                        player[key] = playerData[key];
                    }
                });

                // 應用裝備
                if (playerData.Equipment) {
                    player.Equipment = {
                        Weapon: playerData.Equipment.Weapon,
                        Armor: playerData.Equipment.Armor,
                        Fitting: playerData.Equipment.Fitting
                    };
                }

                // 應用背包
                if (playerData.Inventory) {
                    player.Inventory = [...playerData.Inventory];
                }
            }
        });

        console.log("玩家狀態已應用");
    }

    // 應用敵人狀態
    applyEnemiesState(enemiesData) {
        if (typeof gameenemys === 'undefined') {
            console.warn("gameenemys 未定義，無法應用敵人狀態");
            return;
        }

        // 清空現有敵人
        gameenemys.length = 0;

        // 重新創建敵人（這裡需要根據實際的敵人創建邏輯調整）
        enemiesData.forEach(enemyData => {
            gameenemys.push({ ...enemyData });
        });

        console.log("敵人狀態已應用");
    }

    // 應用遊戲狀態
    applyGameState(gameStateData) {
        if (typeof runOBJ !== 'undefined' && gameStateData.runOBJ) {
            Object.keys(gameStateData.runOBJ).forEach(key => {
                runOBJ[key] = gameStateData.runOBJ[key];
            });
        }

        if (typeof roundCount !== 'undefined') {
            roundCount = gameStateData.roundCount || 1;
        }

        if (typeof actionIndex !== 'undefined') {
            actionIndex = gameStateData.actionIndex || 0;
        }

        if (typeof isActionInProgress !== 'undefined') {
            isActionInProgress = gameStateData.isActionInProgress || false;
        }

        console.log("遊戲狀態已應用");
    }

    // 應用地圖狀態
    applyMapState(mapStateData) {
        if (typeof gametreasures !== 'undefined' && mapStateData.treasures) {
            gametreasures.length = 0;
            gametreasures.push(...mapStateData.treasures);
        }

        console.log("地圖狀態已應用");
    }

    // 應用背包和金錢狀態
    applyInventoryState(inventoryData) {
        if (typeof mybag !== 'undefined' && inventoryData.mybag) {
            mybag.length = 0;
            mybag.push(...inventoryData.mybag);
        }

        if (typeof mymoney !== 'undefined') {
            mymoney = inventoryData.mymoney || 0;
        }

        console.log("背包和金錢狀態已應用");
    }

    // 獲取存檔槽位信息
    getSlotInfo(slotIndex) {
        if (slotIndex < 0 || slotIndex >= this.maxSlots) {
            return null;
        }

        try {
            const storageKey = this.storagePrefix + slotIndex;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                return null;
            }

            const battleState = JSON.parse(savedData);
            return {
                timestamp: battleState.timestamp,
                levelTitle: battleState.levelTitle,
                currentLevel: battleState.currentLevel,
                playersCount: battleState.players ? battleState.players.length : 0,
                enemiesCount: battleState.enemies ? battleState.enemies.length : 0
            };
        } catch (error) {
            console.error("獲取存檔槽位信息失敗:", error);
            return null;
        }
    }

    // 刪除存檔槽位
    deleteSlot(slotIndex) {
        if (slotIndex < 0 || slotIndex >= this.maxSlots) {
            return false;
        }

        try {
            const storageKey = this.storagePrefix + slotIndex;
            localStorage.removeItem(storageKey);
            console.log(`存檔槽位 ${slotIndex} 已刪除`);
            return true;
        } catch (error) {
            console.error("刪除存檔槽位失敗:", error);
            return false;
        }
    }
}

// 創建全域戰場存取管理器實例
const battleSaveManager = new BattleSaveManager();

// ===== 戰場存取UI函數 (Battle Save/Load UI Functions) =====

/**
 * 存取戰場功能 - 參考 Main.js 的 saveGameFunc 樣式
 */
function saveBattleFunc() {
    // 檢查是否可以存檔
    if (typeof runOBJ !== 'undefined' && runOBJ["當前行動方"] === "Enemys") {
        console.warn("敵人行動中，無法存取戰場");
        return;
    }
    if (typeof runOBJ !== 'undefined' && runOBJ["當前操作"] !== null) {
        console.warn("操作進行中，無法存取戰場");
        return;
    }

    console.log("開啟存取戰場介面");

    // 創建 dialog 元素
    let saveDialog = document.createElement("dialog");
    saveDialog.id = "save-battle-dialog";

    // 創建 dialog 內容容器
    let saveContent = document.createElement("div");
    saveContent.className = "save-content";

    // 添加標題
    let title = document.createElement("div");
    title.className = "save-title";
    title.textContent = "存取戰場";
    saveContent.appendChild(title);

    // 添加關閉按鈕
    let closeButton = document.createElement("button");
    closeButton.className = "save-close-btn";
    closeButton.textContent = "↩";
    closeButton.addEventListener("click", () => {
        saveDialog.close();
        if (typeof Dom !== 'undefined' && Dom.GameMap) {
            Dom.GameMap.removeChild(saveDialog);
        } else {
            document.body.removeChild(saveDialog);
        }
    });
    saveContent.appendChild(closeButton);

    // 創建存檔槽容器
    let slotsContainer = document.createElement("div");
    slotsContainer.className = "save-slots-container";

    // 創建五個存檔槽
    for (let i = 0; i < 5; i++) {
        let slot = document.createElement("div");
        slot.className = "save-slot";

        // 左欄：圖騰
        let totem = document.createElement("div");
        totem.className = "save-slot-totem";
        let totemImage = document.createElement("img");
        totemImage.draggable = false;
        totemImage.src = "./Public/saveicon.png";
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：戰場信息和存檔時間
        let middle = document.createElement("div");
        middle.className = "save-slot-middle";
        let battleInfo = document.createElement("div");
        battleInfo.className = "save-slot-level";
        let saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";

        // 獲取存檔信息
        const slotInfo = battleSaveManager.getSlotInfo(i);
        if (slotInfo) {
            battleInfo.textContent = `${slotInfo.levelTitle} (${slotInfo.playersCount}人 vs ${slotInfo.enemiesCount}敵)`;
            saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            battleInfo.style.display = "none";
            saveTime.textContent = "";
        }

        middle.appendChild(battleInfo);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        let right = document.createElement("div");
        right.className = "save-slot-right";
        let levelTitle = document.createElement("div");
        levelTitle.className = "save-slot-title";
        levelTitle.textContent = slotInfo ? slotInfo.levelTitle : "空的戰場";
        right.appendChild(levelTitle);
        slot.appendChild(right);

        // 存檔槽點擊事件
        slot.addEventListener("click", () => {
            let confirmSave = confirm(`是否要將當前戰場存檔在第 ${i + 1} 格的位置？`);
            if (confirmSave) {
                const success = battleSaveManager.saveBattleState(i);
                if (success) {
                    // 更新顯示
                    const newSlotInfo = battleSaveManager.getSlotInfo(i);
                    if (newSlotInfo) {
                        battleInfo.style.display = "block";
                        battleInfo.textContent = `${newSlotInfo.levelTitle} (${newSlotInfo.playersCount}人 vs ${newSlotInfo.enemiesCount}敵)`;
                        saveTime.textContent = new Date(newSlotInfo.timestamp).toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        levelTitle.textContent = newSlotInfo.levelTitle;
                    }
                    alert("戰場存檔成功！");
                } else {
                    alert("戰場存檔失敗！");
                }
            }
        });

        // 添加動畫
        const delay = i * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        slotsContainer.appendChild(slot);
    }

    saveContent.appendChild(slotsContainer);
    saveDialog.appendChild(saveContent);

    // 添加到頁面
    if (typeof Dom !== 'undefined' && Dom.GameMap) {
        Dom.GameMap.appendChild(saveDialog);
    } else {
        document.body.appendChild(saveDialog);
    }

    saveDialog.showModal();
}

/**
 * 讀取戰場功能 - 參考 Main.js 的 loadGameFunc 樣式
 */
function loadBattleFunc() {
    return new Promise((resolve) => {
        // 檢查是否可以讀檔
        if (typeof runOBJ !== 'undefined' && runOBJ["當前行動方"] === "Enemys") {
            console.warn("敵人行動中，無法讀取戰場");
            resolve({ loadBattle: false });
            return;
        }
        if (typeof runOBJ !== 'undefined' && runOBJ["當前操作"] !== null) {
            console.warn("操作進行中，無法讀取戰場");
            resolve({ loadBattle: false });
            return;
        }

        console.log("開啟讀取戰場介面");

        // 創建 dialog 元素
        let loadDialog = document.createElement("dialog");
        loadDialog.id = "load-battle-dialog";

        // 創建 dialog 內容容器
        let loadContent = document.createElement("div");
        loadContent.className = "save-content";

        // 添加標題
        let title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取戰場";
        loadContent.appendChild(title);

        // 添加關閉按鈕
        let closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            if (typeof Dom !== 'undefined' && Dom.GameMap) {
                Dom.GameMap.removeChild(loadDialog);
            } else {
                document.body.removeChild(loadDialog);
            }
            resolve({ loadBattle: false });
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        let slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            let slot = document.createElement("div");
            slot.className = "save-slot";

            // 左欄：圖騰
            let totem = document.createElement("div");
            totem.className = "save-slot-totem";
            let totemImage = document.createElement("img");
            totemImage.draggable = false;
            totemImage.src = "./Public/saveicon.png";
            totem.appendChild(totemImage);
            slot.appendChild(totem);

            // 中間欄：戰場信息和存檔時間
            let middle = document.createElement("div");
            middle.className = "save-slot-middle";
            let battleInfo = document.createElement("div");
            battleInfo.className = "save-slot-level";
            let saveTime = document.createElement("div");
            saveTime.className = "save-slot-time";

            // 獲取存檔信息
            const slotInfo = battleSaveManager.getSlotInfo(i);
            if (slotInfo) {
                battleInfo.textContent = `${slotInfo.levelTitle} (${slotInfo.playersCount}人 vs ${slotInfo.enemiesCount}敵)`;
                saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } else {
                battleInfo.style.display = "none";
                saveTime.textContent = "空的戰場";
            }

            middle.appendChild(battleInfo);
            middle.appendChild(saveTime);
            slot.appendChild(middle);

            // 右欄：關卡標題
            let right = document.createElement("div");
            right.className = "save-slot-right";
            let levelTitle = document.createElement("div");
            levelTitle.className = "save-slot-title";
            levelTitle.textContent = slotInfo ? slotInfo.levelTitle : "空的戰場";
            right.appendChild(levelTitle);
            slot.appendChild(right);

            // 存檔槽點擊事件
            slot.addEventListener("click", () => {
                if (!slotInfo) {
                    alert("此槽位沒有戰場存檔！");
                    return;
                }

                let confirmLoad = confirm(`是否要讀取第 ${i + 1} 格的戰場存檔？\n關卡：${slotInfo.levelTitle}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
                if (confirmLoad) {
                    const battleState = battleSaveManager.loadBattleState(i);
                    if (battleState) {
                        const success = battleSaveManager.applyBattleState(battleState);
                        if (success) {
                            loadDialog.close();
                            if (typeof Dom !== 'undefined' && Dom.GameMap) {
                                Dom.GameMap.removeChild(loadDialog);
                            } else {
                                document.body.removeChild(loadDialog);
                            }

                            // 重新初始化遊戲畫面
                            if (typeof Game !== 'undefined' && typeof Game.reloadBattleField === 'function') {
                                Game.reloadBattleField();
                            } else if (typeof render === 'function') {
                                render();
                            }

                            alert("戰場讀取成功！");
                            resolve({ loadBattle: true });
                        } else {
                            alert("戰場讀取失敗！");
                            resolve({ loadBattle: false });
                        }
                    } else {
                        alert("戰場讀取失敗！");
                        resolve({ loadBattle: false });
                    }
                }
            });

            // 添加動畫
            const delay = i * 0.2;
            slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

            slotsContainer.appendChild(slot);
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);

        // 添加到頁面
        if (typeof Dom !== 'undefined' && Dom.GameMap) {
            Dom.GameMap.appendChild(loadDialog);
        } else {
            document.body.appendChild(loadDialog);
        }

        loadDialog.showModal();
    });
}

// ===== 戰場重載和輔助函數 =====

/**
 * 重新載入戰場 - 在 Game 對象中添加此方法
 */
if (typeof Game !== 'undefined') {
    Game.reloadBattleField = function() {
        console.log("重新載入戰場");

        try {
            // 重新初始化Canvas
            if (typeof initCanvas === 'function' && typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined') {
                initCanvas(controlLayer[currentLevel].size.cols, controlLayer[currentLevel].size.rows);
            }

            // 重新載入地圖物件
            if (typeof updateMapObjects === 'function') {
                updateMapObjects();
            }

            // 重新渲染
            if (typeof render === 'function') {
                render();
            }

            // 重新初始化UI
            if (typeof initUI === 'function') {
                initUI();
            }

            console.log("戰場重新載入完成");
        } catch (error) {
            console.error("重新載入戰場失敗:", error);
        }
    };
}

// ===== 測試和調試功能 =====

/**
 * 測試戰場存取功能
 */
function testBattleSaveLoad() {
    console.log("=== 測試戰場存取功能 ===");

    // 測試1: 獲取當前戰場狀態
    console.log("測試1: 獲取當前戰場狀態");
    const currentState = battleSaveManager.getCurrentBattleState();
    console.log("當前戰場狀態:", currentState);

    // 測試2: 保存到槽位0
    console.log("測試2: 保存戰場狀態到槽位0");
    const saveSuccess = battleSaveManager.saveBattleState(0);
    console.log("保存結果:", saveSuccess ? "成功" : "失敗");

    // 測試3: 獲取槽位信息
    console.log("測試3: 獲取槽位0信息");
    const slotInfo = battleSaveManager.getSlotInfo(0);
    console.log("槽位0信息:", slotInfo);

    // 測試4: 載入戰場狀態
    console.log("測試4: 載入槽位0的戰場狀態");
    const loadedState = battleSaveManager.loadBattleState(0);
    console.log("載入的戰場狀態:", loadedState);

    // 測試5: 比較狀態一致性
    if (loadedState) {
        const isConsistent = JSON.stringify(currentState) === JSON.stringify(loadedState);
        console.log("狀態一致性測試:", isConsistent ? "通過" : "失敗");
    }

    console.log("=== 戰場存取功能測試完成 ===");
}

/**
 * 清除所有戰場存檔
 */
function clearAllBattleSaves() {
    console.log("清除所有戰場存檔");

    let confirmClear = confirm("確定要清除所有戰場存檔嗎？此操作無法復原！");
    if (confirmClear) {
        for (let i = 0; i < 5; i++) {
            battleSaveManager.deleteSlot(i);
        }
        alert("所有戰場存檔已清除！");
        console.log("所有戰場存檔已清除");
    }
}

/**
 * 顯示戰場存檔信息
 */
function showBattleSaveInfo() {
    console.log("=== 戰場存檔信息 ===");

    for (let i = 0; i < 5; i++) {
        const slotInfo = battleSaveManager.getSlotInfo(i);
        if (slotInfo) {
            console.log(`槽位 ${i + 1}:`, {
                關卡: slotInfo.levelTitle,
                時間: new Date(slotInfo.timestamp).toLocaleString(),
                玩家數: slotInfo.playersCount,
                敵人數: slotInfo.enemiesCount
            });
        } else {
            console.log(`槽位 ${i + 1}: 空的`);
        }
    }

    console.log("=== 戰場存檔信息結束 ===");
}




var Player0 = {
    id: "player0",
    "是否電腦操作": false,
    lastdirect: "down",
    hitframe: 0.35,
    name: "應奉仁",
    level: 20,
    avatar: "./Public/Players/0/avatar.png",
    HP: 490,
    CurHP: 490,
    MP: 0,
    CurMP: 0,
    EXP: 0,
    CurEXP: 0,
    ATK: 45,//45
    ATKRange: 1,
    "是否蓄力": false,
    "是否釋放蓄力": false,
    "已增加移動力": false,
    AvoidRate: 70,//70
    HitRate: 200,
    CriticalHitRate: 20,//20
    Move: 6,
    AlreadyMove: false,
    DEF: 120,
    Position: null,
    OldPosition: null,
    "點數": 0,
    "五內": {
        "迅": 80,
        "烈": 80,
        "神": 80,
        "魔": 80,
        "魂": 80
    },
    "法抗": {
        "火": 15,
        "雷": 15,
        "冰": 15,
        "幻": 15,
        "華": 15,
        "冥": 15
    },
    "法術": [],
    // 裝備欄位
    Equipment: {
        Weapon: Weapons["長劍"],
        Armor: Armors["軟革裏衣"],
        Fitting: Fittings["避邪玉珮"]
    },
    // 背包
    Inventory: [],
    "圖片": "./Public/Players/0/man.gif",
    width: "83.6",   // 改成數字字串
    height: "132",  // 改成數字字串
    Stand: {
        up: ["./Public/Players/0/Stand/up/0.png", "./Public/Players/0/Stand/up/1.png", "./Public/Players/0/Stand/up/2.png", "./Public/Players/0/Stand/up/3.png"],
        down: ["./Public/Players/0/Stand/down/0.png", "./Public/Players/0/Stand/down/1.png", "./Public/Players/0/Stand/down/2.png", "./Public/Players/0/Stand/down/3.png"],
        left: ["./Public/Players/0/Stand/left/0.png", "./Public/Players/0/Stand/left/1.png", "./Public/Players/0/Stand/left/2.png", "./Public/Players/0/Stand/left/3.png"],
        right: ["./Public/Players/0/Stand/right/0.png", "./Public/Players/0/Stand/right/1.png", "./Public/Players/0/Stand/right/2.png", "./Public/Players/0/Stand/right/3.png"],
    },
    MoveRes: {  //移動時圖片
        up: ["./Public/Players/0/Move/up/0.png", "./Public/Players/0/Move/up/1.png", "./Public/Players/0/Move/up/2.png", "./Public/Players/0/Move/up/3.png"],
        down: ["./Public/Players/0/Move/down/0.png", "./Public/Players/0/Move/down/1.png", "./Public/Players/0/Move/down/2.png", "./Public/Players/0/Move/down/3.png"],
        left: ["./Public/Players/0/Move/left/0.png", "./Public/Players/0/Move/left/1.png", "./Public/Players/0/Move/left/2.png", "./Public/Players/0/Move/left/3.png"],
        right: ["./Public/Players/0/Move/right/0.png", "./Public/Players/0/Move/right/1.png", "./Public/Players/0/Move/right/2.png", "./Public/Players/0/Move/right/3.png"],
    },
    AtkRes: {
        animates: ["./Public/Players/0/ATK/0.png", "./Public/Players/0/ATK/1.png", "./Public/Players/0/ATK/2.png", "./Public/Players/0/ATK/3.png", "./Public/Players/0/ATK/4.png", "./Public/Players/0/ATK/5.png", "./Public/Players/0/ATK/6.png", "./Public/Players/0/ATK/7.png", "./Public/Players/0/ATK/8.png", "./Public/Players/0/ATK/9.png", "./Public/Players/0/ATK/10.png", "./Public/Players/0/ATK/11.png", "./Public/Players/0/ATK/12.png", "./Public/Players/0/ATK/13.png", "./Public/Players/0/ATK/14.png", "./Public/Players/0/ATK/15.png", "./Public/Players/0/ATK/16.png", "./Public/Players/0/ATK/17.png", "./Public/Players/0/ATK/18.png", "./Public/Players/0/ATK/19.png", "./Public/Players/0/ATK/20.png"],
        sound: "./Public/Players/0/ATK/ATK.mp3", // Corrected path
        x: 55,
        y: 55,
    },
    "遭受攻擊": {
        animates: ["./Public/Players/0/MISC/0.png"],
        x: 10,
        y: 10
    },
    AtkMiss: {
        animates: ["./Public/Players/0/ATKB/0.png", "./Public/Players/0/ATKB/1.png", "./Public/Players/0/ATKB/2.png", "./Public/Players/0/ATKB/3.png", "./Public/Players/0/ATKB/4.png", "./Public/Players/0/ATKB/5.png", "./Public/Players/0/ATKB/6.png", "./Public/Players/0/ATKB/7.png", "./Public/Players/0/ATKB/8.png", "./Public/Players/0/ATKB/9.png", "./Public/Players/0/ATKB/10.png", "./Public/Players/0/ATKB/11.png", "./Public/Players/0/ATKB/12.png", "./Public/Players/0/ATKB/13.png", "./Public/Players/0/ATKB/14.png", "./Public/Players/0/ATKB/15.png", "./Public/Players/0/ATKB/16.png", "./Public/Players/0/ATKB/17.png", "./Public/Players/0/ATKB/18.png", "./Public/Players/0/ATKB/19.png"],
        sound: "./Public/Players/0/ATKB/ATKB.mp3", // Assuming sound is in ATKB folder
        x: 55,
        y: 55,
    },
    BattleIdleRes: { // For NORMAL folder (battle standing animation)
        animates: ["./Public/Players/0/NORMAL/0.png", "./Public/Players/0/NORMAL/1.png", "./Public/Players/0/NORMAL/2.png", "./Public/Players/0/NORMAL/3.png"],
        x: 10,
        y: 15,
    },
    function: function () {
        //畫面震動
        setTimeout(() => {
            Dom.BattleScreen.style.animation = "shakeOtoLU 0.2s ease-in-out";
            setTimeout(() => {
                Dom.BattleScreen.style.animation = "shakeOtoRU 0.5s ease-in-out";
                setTimeout(() => {
                    Dom.BattleScreen.style.animation = "null";
                }, 100)
            }, 500)
        }, 1200);
    },
    bloodfun: function (playergamage, enemy) {
        return new Promise((resolve) => {
            let tempdamge = playergamage / 3
            let originalHP = enemy.CurHP;  // 保存原始HP值


            let enemyhpbar = document.getElementById("enemybattlehpbar");
            let enemyhpinner = document.getElementById("enemybattlehp-inner");
            let enemyhpinnerdelay = document.getElementById("enemybattlehpbar_inner");
            let enemyhptext = document.getElementById("enemybattlehptext");

            enemyhpinner.style.width = `${(originalHP / enemy.HP) * 490}px`;
            enemyhptext.textContent = `${Math.floor(originalHP)} / ${enemy.HP}`;
            enemyhpinnerdelay.style.width = `${(originalHP / enemy.HP) * 100}%`;

            setTimeout(() => {
                enemyhpinner.style.width = `${(Math.max(0, originalHP - tempdamge) / enemy.HP) * 490}px`;
                enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge) / enemy.HP) * 100}%`;
                enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge))} / ${enemy.HP}`;
                enemyhpbar.style.animation = "lll 0.3s linear forwards";
                setTimeout(() => {
                    enemyhpbar.style.animation = "null";
                }, 100)
                setTimeout(() => {

                    enemyhpinner.style.width = `${(Math.max(0, originalHP - tempdamge * 2) / enemy.HP) * 490}px`;
                    enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge * 2) / enemy.HP) * 100}%`;
                    enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge * 2))} / ${enemy.HP}`;
                    enemyhpbar.style.animation = "lll 0.3s linear forwards";
                    setTimeout(() => {
                        enemyhpbar.style.animation = "null";
                    }, 100)
                    setTimeout(() => {

                        enemyhpinner.style.width = `${(Math.max(0, originalHP - playergamage) / enemy.HP) * 490}px`;
                        enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - playergamage) / enemy.HP) * 100}%`;
                        enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - playergamage))} / ${enemy.HP}`;
                        enemy.CurHP = Math.max(0, originalHP - playergamage);  // 只在動畫完成後更新實際HP
                        enemyhpbar.style.animation = "lll 0.3s linear forwards";
                        setTimeout(() => {
                            enemyhpbar.style.animation = "null";
                            resolve(enemy.CurHP <= 0); // 返回是否敵人死亡
                        }, 100)

                    }, 400)
                }, 400)
            }, 1000)
        })
    }
}

var Player1 = {
    id: "player1",
    "是否電腦操作": false,
    hitframe: 0.25,
    lastdirect: "down",
    name: "殷劍平",
    avatar: "./Public/Players/1/avatar.png",
    level: 1,
    HP: 380,//60
    CurHP: 380,
    MP: 14,
    CurMP: 14,
    EXP: 500,
    CurEXP: 499,
    ATK: 144, //20
    ATKRange: 1,
    "是否蓄力": false,
    "是否釋放蓄力": false,
    "已增加移動力": false,
    AvoidRate: 18,//18
    HitRate: 990,//90
    CriticalHitRate: 18,//18
    Move: 6,
    AlreadyMove: false,
    DEF: 9,
    Position: null,
    OldPosition: null,
    "點數": 0,
    "五內": {
        "迅": 20,
        "烈": 24,
        "神": 16,
        "魔": 22,
        "魂": 18
    },
    "法抗": {
        "火": -10,
        "雷": -5,
        "冰": -15,
        "幻": -25,
        "華": -15,
        "冥": -10
    },
    "法術": [
        {
            name: "氣愈之術",
            type: "法術",
            Classify: "治癒",
            describsion: "最初級的療癒法術，集聚體內之氣於一處，可治癒較輕微的傷勢",
            Who: ["殷劍平"],
            NeedMove: 0,
            NeedLV: 3,
            distance: 4,
            Rmdistance: 0,
            Range: 1,
            NeedMP: 14,
            NeedSoul: {
                "迅": 0,
                "烈": 0,
                "神": 0,
                "魔": 0,
                "魂": 0
            },
            Icon: "./Public/Magic/0/icon.png",
            Animaties: ["./Public/Magic/0/Animation/0.png", "./Public/Magic/0/Animation/1.png", "./Public/Magic/0/Animation/2.png", "./Public/Magic/0/Animation/3.png", "./Public/Magic/0/Animation/4.png", "./Public/Magic/0/Animation/5.png", "./Public/Magic/0/Animation/6.png", "./Public/Magic/0/Animation/7.png", "./Public/Magic/0/Animation/8.png", "./Public/Magic/0/Animation/9.png", "./Public/Magic/0/Animation/10.png", "./Public/Magic/0/Animation/11.png", "./Public/Magic/0/Animation/12.png", "./Public/Magic/0/Animation/13.png", "./Public/Magic/0/Animation/14.png", "./Public/Magic/0/Animation/15.png", "./Public/Magic/0/Animation/16.png", "./Public/Magic/0/Animation/17.png", "./Public/Magic/0/Animation/18.png", "./Public/Magic/0/Animation/19.png", "./Public/Magic/0/Animation/20.png", "./Public/Magic/0/Animation/21.png", "./Public/Magic/0/Animation/22.png", "./Public/Magic/0/Animation/23.png", "./Public/Magic/0/Animation/24.png", "./Public/Magic/0/Animation/25.png", "./Public/Magic/0/Animation/26.png", "./Public/Magic/0/Animation/27.png", "./Public/Magic/0/Animation/28.png", "./Public/Magic/0/Animation/29.png", "./Public/Magic/0/Animation/30.png", "./Public/Magic/0/Animation/31.png"],
            Sounds: "./Public/Magic/0/sound.mp3",
            effect: function (playerMP) {
                const healAmount = Math.floor(playerMP * 0.55 + 60);
                return {
                    type: "heal",
                    value: healAmount,
                    target: "self"
                };
            }
        },
        {
            name: "無方飛劍",
            type: "武功",
            Classify: "傷害",
            describsion: "殷劍平初段的御劍之法，可聚氣於劍尖遠射攻敵，威力不弱",
            Who: ["殷劍平"],
            NeedMove: 2.4,
            NeedLV: 15,
            distance: 5,
            Rmdistance: 4,
            Range: 0,
            NeedMP: 20,
            NeedSoul: {
                "迅": 15,
                "烈": 25,
                "神": 25,
                "魔": 25,
                "魂": 25
            },
            Icon: "./Public/Magic/1/icon.png",
            Animations: "./Public/Magic/1/icon.png",
            Sounds: ".Public/Magic/1/1.mp3",
            effect: function (playeratk) {
                const damageAmount = Math.floor(playeratk * 1.55 + 60);
                return {
                    type: "damage",
                    value: damageAmount,
                    target: "self"
                };
            }
        }
    ],
    // 裝備欄位
    Equipment: {
        Weapon: Weapons["長劍"],
        Armor: Armors["軟革裏衣"],
        Fitting: null
    },
    // 背包
    Inventory: [Weapons["古劍‧龍形"], Eatting["金創藥"], Eatting["金創藥"], Weapons["匕首"]],
    "圖片": "./Public/Players/1/man.gif",
    width: "77",
    height: "122",
    lvup: {
        animates: ["./Public/Players/1/LVup/0.png", "./Public/Players/1/LVup/1.png", "./Public/Players/1/LVup/2.png", "./Public/Players/1/LVup/3.png", "./Public/Players/1/LVup/4.png", "./Public/Players/1/LVup/5.png", "./Public/Players/1/LVup/6.png", "./Public/Players/1/LVup/7.png", "./Public/Players/1/LVup/8.png", "./Public/Players/1/LVup/9.png"],
        sound: "./Public/Players/1/LVup/lvup.mp3",
    },
    Stand: {
        up: ["./Public/Players/1/Stand/up/0.png", "./Public/Players/1/Stand/up/1.png", "./Public/Players/1/Stand/up/2.png", "./Public/Players/1/Stand/up/3.png", "./Public/Players/1/Stand/up/4.png", "./Public/Players/1/Stand/up/5.png", "./Public/Players/1/Stand/up/6.png", "./Public/Players/1/Stand/up/7.png", "./Public/Players/1/Stand/up/8.png", "./Public/Players/1/Stand/up/9.png", "./Public/Players/1/Stand/up/10.png"],
        down: ["./Public/Players/1/Stand/down/0.png", "./Public/Players/1/Stand/down/1.png", "./Public/Players/1/Stand/down/2.png", "./Public/Players/1/Stand/down/3.png", "./Public/Players/1/Stand/down/4.png", "./Public/Players/1/Stand/down/5.png", "./Public/Players/1/Stand/down/6.png", "./Public/Players/1/Stand/down/7.png", "./Public/Players/1/Stand/down/8.png", "./Public/Players/1/Stand/down/9.png", "./Public/Players/1/Stand/down/10.png"],
        left: ["./Public/Players/1/Stand/left/0.png", "./Public/Players/1/Stand/left/1.png", "./Public/Players/1/Stand/left/2.png", "./Public/Players/1/Stand/left/3.png", "./Public/Players/1/Stand/left/4.png", "./Public/Players/1/Stand/left/5.png", "./Public/Players/1/Stand/left/6.png", "./Public/Players/1/Stand/left/7.png", "./Public/Players/1/Stand/left/8.png", "./Public/Players/1/Stand/left/9.png", "./Public/Players/1/Stand/left/10.png"],
        right: ["./Public/Players/1/Stand/right/0.png", "./Public/Players/1/Stand/right/1.png", "./Public/Players/1/Stand/right/2.png", "./Public/Players/1/Stand/right/3.png", "./Public/Players/1/Stand/right/4.png", "./Public/Players/1/Stand/right/5.png", "./Public/Players/1/Stand/right/6.png", "./Public/Players/1/Stand/right/7.png", "./Public/Players/1/Stand/right/8.png", "./Public/Players/1/Stand/right/9.png", "./Public/Players/1/Stand/right/10.png"],
    },
    MoveRes: {
        up: ["./Public/Players/1/Move/up/0.png", "./Public/Players/1/Move/up/1.png", "./Public/Players/1/Move/up/2.png", "./Public/Players/1/Move/up/3.png"],
        down: ["./Public/Players/1/Move/down/0.png", "./Public/Players/1/Move/down/1.png", "./Public/Players/1/Move/down/2.png", "./Public/Players/1/Move/down/3.png"],
        left: ["./Public/Players/1/Move/left/0.png", "./Public/Players/1/Move/left/1.png", "./Public/Players/1/Move/left/2.png", "./Public/Players/1/Move/left/3.png"],
        right: ["./Public/Players/1/Move/right/0.png", "./Public/Players/1/Move/right/1.png", "./Public/Players/1/Move/right/2.png", "./Public/Players/1/Move/right/3.png"],
    },
    usemagic: ["./Public/Players/1/usemagic/0.png", "./Public/Players/1/usemagic/1.png"],
    AtkRes: {
        animates: ["./Public/Players/1/ATK/0.png", "./Public/Players/1/ATK/1.png", "./Public/Players/1/ATK/2.png", "./Public/Players/1/ATK/3.png", "./Public/Players/1/ATK/4.png", "./Public/Players/1/ATK/5.png", "./Public/Players/1/ATK/6.png", "./Public/Players/1/ATK/7.png", "./Public/Players/1/ATK/8.png", "./Public/Players/1/ATK/9.png", "./Public/Players/1/ATK/10.png", "./Public/Players/1/ATK/11.png", "./Public/Players/1/ATK/12.png", "./Public/Players/1/ATK/13.png", "./Public/Players/1/ATK/14.png", "./Public/Players/1/ATK/15.png", "./Public/Players/1/ATK/16.png", "./Public/Players/1/ATK/17.png", "./Public/Players/1/ATK/18.png", "./Public/Players/1/ATK/19.png", "./Public/Players/1/ATK/20.png", "./Public/Players/1/ATK/21.png", "./Public/Players/1/ATK/22.png"],
        sound: "./Public/Players/1/ATK/ATK.mp3",
        x: 35,
        y: 5,
    },
    "遭受攻擊": {
        animates: ["./Public/Players/1/MISC/0.png"],
        x: 20,
        y: 5,
    },
    AtkMiss: {
        animates: ["./Public/Players/1/ATKB/0.png", "./Public/Players/1/ATKB/1.png", "./Public/Players/1/ATKB/2.png", "./Public/Players/1/ATKB/3.png", "./Public/Players/1/ATKB/4.png", "./Public/Players/1/ATKB/5.png", "./Public/Players/1/ATKB/6.png", "./Public/Players/1/ATKB/7.png", "./Public/Players/1/ATKB/8.png", "./Public/Players/1/ATKB/9.png", "./Public/Players/1/ATKB/10.png", "./Public/Players/1/ATKB/11.png", "./Public/Players/1/ATKB/12.png", "./Public/Players/1/ATKB/13.png"],
        sound: "./Public/Players/1/ATKB/ATKB.mp3",
        x: 35,
        y: 5,
    },
    BattleIdleRes: {
        animates: ["./Public/Players/1/NORMAL/0.png", "./Public/Players/1/NORMAL/1.png", "./Public/Players/1/NORMAL/2.png", "./Public/Players/1/NORMAL/3.png", "./Public/Players/1/NORMAL/4.png", "./Public/Players/1/NORMAL/5.png", "./Public/Players/1/NORMAL/6.png", "./Public/Players/1/NORMAL/7.png", "./Public/Players/1/NORMAL/8.png", "./Public/Players/1/NORMAL/9.png"],
        x: 0,
        y: 0,
    },
    function: function () {
        //畫面震動
        setTimeout(() => {
            Dom.BattleScreen.style.animation = "shakeOtoLU 0.2s ease-in-out";
            setTimeout(() => {
                Dom.BattleScreen.style.animation = "null";
            }, 100)
        }, 700);
    },
    bloodfun: function (playergamage, enemy) {
        return new Promise((resolve) => {
            let tempdamge = playergamage / 3
            let originalHP = enemy.CurHP;  // 保存原始HP值

            let enemyhpbar = document.getElementById("enemybattlehpbar");
            let enemyhpinner = document.getElementById("enemybattlehp-inner");
            let enemyhpinnerdelay = document.getElementById("enemybattlehpbar_inner");
            let enemyhptext = document.getElementById("enemybattlehptext");

            enemyhpinner.style.width = `${(originalHP / enemy.HP) * 490}px`;
            enemyhptext.textContent = `${Math.floor(originalHP)} / ${enemy.HP}`;
            enemyhpinnerdelay.style.width = `${(originalHP / enemy.HP) * 100}%`;

            setTimeout(() => {
                enemyhpinner.style.width = `${(Math.max(0, originalHP - tempdamge) / enemy.HP) * 490}px`;
                enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge) / enemy.HP) * 100}%`;
                enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge))} / ${enemy.HP}`;
                enemyhpbar.style.animation = "lll 0.3s linear forwards";
                setTimeout(() => {
                    enemyhpbar.style.animation = "null";
                }, 100)
                setTimeout(() => {

                    enemyhpinner.style.width = `${(Math.max(0, originalHP - tempdamge * 2) / enemy.HP) * 490}px`;
                    enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge * 2) / enemy.HP) * 100}%`;
                    enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge * 2))} / ${enemy.HP}`;
                    enemyhpbar.style.animation = "lll 0.3s linear forwards";
                    setTimeout(() => {
                        enemyhpbar.style.animation = "null";
                    }, 100)
                    setTimeout(() => {

                        enemyhpinner.style.width = `${(Math.max(0, originalHP - playergamage) / enemy.HP) * 490}px`;
                        enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - playergamage) / enemy.HP) * 100}%`;
                        enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - playergamage))} / ${enemy.HP}`;
                        enemy.CurHP = Math.max(0, originalHP - playergamage);  // 只在動畫完成後更新實際HP
                        enemyhpbar.style.animation = "lll 0.3s linear forwards";
                        setTimeout(() => {
                            enemyhpbar.style.animation = "null";
                            resolve(enemy.CurHP <= 0); // 返回是否敵人死亡
                        }, 100)
                    }, 600)
                }, 500)
            }, 600)
        })
    }
}

var Player2 = {
    id: "player2",
    "是否電腦操作": false,
    lastdirect: "down",
    hitframe: 0.2,
    name: "封寒月",
    avatar: "./Public/Players/2/avatar.png",
    level: 1,
    HP: 245,//70
    CurHP: 245,
    MP: 26,
    CurMP: 26,
    EXP: 500,
    CurEXP: 0,
    ATK: 16,
    ATKRange: 1,
    "是否蓄力": false,
    "是否釋放蓄力": false,
    "已增加移動力": false,
    AvoidRate: 24,//5
    HitRate: 95,//95
    CriticalHitRate: 20,//5
    Move: 3.5,
    AlreadyMove: false,
    DEF: 2,
    Position: null,
    OldPosition: null,
    "點數": 0,
    "五內": {
        "迅": 20,
        "烈": 20,
        "神": 18,
        "魔": 25,
        "魂": 22
    },
    "法抗": {
        "火": 53,
        "雷": 53,
        "冰": 53,
        "幻": 29,
        "華": 38,
        "冥": 38
    },
    "法術": [{
        name: "離火神訣",
        type: "法術",
        Classify: "火系",
        describsion: "引離火之氣包於敵人身邊，可焚燒小群敵人，造成一般程度的創傷",
        Who: ["封寒月"],
        NeedMove: 0,
        NeedLV: 3,
        distance: 4,
        Range: 1,
        NeedMP: 10,
        NeedSoul: {
            "迅": 0,
            "烈": 0,
            "神": 0,
            "魔": 0,
            "魂": 0
        },
        Icon: "./Public/Magic/2/2.png",
        Animations: "./Public/Magic/0/",
        Sounds: ".Public/Magic/0/0.mp3",
        effect: function (playerMP) {
            const healAmount = Math.floor(playerMP * 0.45 + 60);
            return {
                type: "heal",
                value: healAmount,
                target: "self"
            };
        }
    },],
    // 裝備欄位
    Equipment: {
        Weapon: Weapons["匕首"],
        Armor: Armors["法袍"],
        Fitting: null
    },
    // 背包
    Inventory: [],
    "圖片": "./Public/Players/2/man.gif",
    width: "80",
    height: "130",
    lvup: {
        animates: ["./Public/Players/2/LVup/0.png", "./Public/Players/2/LVup/1.png", "./Public/Players/2/LVup/2.png", "./Public/Players/2/LVup/3.png", "./Public/Players/2/LVup/4.png", "./Public/Players/2/LVup/5.png", "./Public/Players/2/LVup/6.png", "./Public/Players/2/LVup/7.png", "./Public/Players/2/LVup/8.png", "./Public/Players/2/LVup/9.png", "./Public/Players/2/LVup/10.png", "./Public/Players/2/LVup/11.png", "./Public/Players/2/LVup/12.png", "./Public/Players/2/LVup/13.png", "./Public/Players/2/LVup/14.png", "./Public/Players/2/LVup/15.png", "./Public/Players/2/LVup/16.png", "./Public/Players/2/LVup/17.png"],
        sound: "./Public/Players/2/LVup/lvup.mp3",
    },
    Stand: {
        up: ["./Public/Players/2/Stand/up/0.png", "./Public/Players/2/Stand/up/1.png", "./Public/Players/2/Stand/up/2.png", "./Public/Players/2/Stand/up/3.png", "./Public/Players/2/Stand/up/4.png", "./Public/Players/2/Stand/up/5.png", "./Public/Players/2/Stand/up/6.png", "./Public/Players/2/Stand/up/7.png", "./Public/Players/2/Stand/up/8.png", "./Public/Players/2/Stand/up/9.png"],
        down: ["./Public/Players/2/Stand/down/0.png", "./Public/Players/2/Stand/down/1.png", "./Public/Players/2/Stand/down/2.png", "./Public/Players/2/Stand/down/3.png", "./Public/Players/2/Stand/down/4.png", "./Public/Players/2/Stand/down/5.png", "./Public/Players/2/Stand/down/6.png", "./Public/Players/2/Stand/down/7.png", "./Public/Players/2/Stand/down/8.png", "./Public/Players/2/Stand/down/9.png"],
        left: ["./Public/Players/2/Stand/left/0.png", "./Public/Players/2/Stand/left/1.png", "./Public/Players/2/Stand/left/2.png", "./Public/Players/2/Stand/left/3.png", "./Public/Players/2/Stand/left/4.png", "./Public/Players/2/Stand/left/5.png", "./Public/Players/2/Stand/left/6.png", "./Public/Players/2/Stand/left/7.png", "./Public/Players/2/Stand/left/8.png", "./Public/Players/2/Stand/left/9.png"],
        right: ["./Public/Players/2/Stand/right/0.png", "./Public/Players/2/Stand/right/1.png", "./Public/Players/2/Stand/right/2.png", "./Public/Players/2/Stand/right/3.png", "./Public/Players/2/Stand/right/4.png", "./Public/Players/2/Stand/right/5.png", "./Public/Players/2/Stand/right/6.png", "./Public/Players/2/Stand/right/7.png", "./Public/Players/2/Stand/right/8.png", "./Public/Players/2/Stand/right/9.png"],
    },
    MoveRes: {
        up: ["./Public/Players/2/Move/up/0.png", "./Public/Players/2/Move/up/1.png", "./Public/Players/2/Move/up/2.png", "./Public/Players/2/Move/up/3.png", "./Public/Players/2/Move/up/4.png", "./Public/Players/2/Move/up/5.png", "./Public/Players/2/Move/up/6.png", "./Public/Players/2/Move/up/7.png"],
        down: ["./Public/Players/2/Move/down/0.png", "./Public/Players/2/Move/down/1.png", "./Public/Players/2/Move/down/2.png", "./Public/Players/2/Move/down/3.png", "./Public/Players/2/Move/down/4.png", "./Public/Players/2/Move/down/5.png", "./Public/Players/2/Move/down/6.png", "./Public/Players/2/Move/down/7.png"],
        left: ["./Public/Players/2/Move/left/0.png", "./Public/Players/2/Move/left/1.png", "./Public/Players/2/Move/left/2.png", "./Public/Players/2/Move/left/3.png" ,"./Public/Players/2/Move/left/4.png", "./Public/Players/2/Move/left/5.png", "./Public/Players/2/Move/left/6.png", "./Public/Players/2/Move/left/7.png"],
        right: ["./Public/Players/2/Move/right/0.png", "./Public/Players/2/Move/right/1.png", "./Public/Players/2/Move/right/2.png", "./Public/Players/2/Move/right/3.png", "./Public/Players/2/Move/right/4.png", "./Public/Players/2/Move/right/5.png", "./Public/Players/2/Move/right/6.png", "./Public/Players/2/Move/right/7.png"],
    },
    BattleIdleRes: {
        animates: ["./Public/Players/2/NORMAL/0.png", "./Public/Players/2/NORMAL/1.png", "./Public/Players/2/NORMAL/2.png", "./Public/Players/2/NORMAL/3.png", "./Public/Players/2/NORMAL/4.png", "./Public/Players/2/NORMAL/5.png", "./Public/Players/2/NORMAL/6.png", "./Public/Players/2/NORMAL/7.png", "./Public/Players/2/NORMAL/8.png", "./Public/Players/2/NORMAL/9.png"],
        x: 0,
        y: 0,
    },
    AtkRes: {
        animates: ["./Public/Players/2/ATK/0.png", "./Public/Players/2/ATK/1.png", "./Public/Players/2/ATK/2.png", "./Public/Players/2/ATK/3.png", "./Public/Players/2/ATK/4.png", "./Public/Players/2/ATK/5.png", "./Public/Players/2/ATK/6.png", "./Public/Players/2/ATK/7.png", "./Public/Players/2/ATK/8.png", "./Public/Players/2/ATK/9.png", "./Public/Players/2/ATK/10.png", "./Public/Players/2/ATK/11.png", "./Public/Players/2/ATK/12.png", "./Public/Players/2/ATK/13.png", "./Public/Players/2/ATK/14.png", "./Public/Players/2/ATK/15.png", "./Public/Players/2/ATK/16.png", "./Public/Players/2/ATK/17.png", "./Public/Players/2/ATK/18.png", "./Public/Players/2/ATK/19.png", "./Public/Players/2/ATK/20.png"],
        sound: "./Public/Players/2/ATK/ATK.mp3",
        x: 35,
        y: 5,
    },
    "遭受攻擊": {
        animates: ["./Public/Players/2/MISC/0.png"],
        x: 20,
        y: 5,
    },
    AtkMiss: {
        animates: ["./Public/Players/2/ATKB/0.png", "./Public/Players/2/ATKB/1.png", "./Public/Players/2/ATKB/2.png", "./Public/Players/2/ATKB/3.png", "./Public/Players/2/ATKB/4.png", "./Public/Players/2/ATKB/5.png", "./Public/Players/2/ATKB/6.png", "./Public/Players/2/ATKB/7.png", "./Public/Players/2/ATKB/8.png", "./Public/Players/2/ATKB/9.png", "./Public/Players/2/ATKB/10.png", "./Public/Players/2/ATKB/11.png", "./Public/Players/2/ATKB/12.png", "./Public/Players/2/ATKB/13.png", "./Public/Players/2/ATKB/14.png", "./Public/Players/2/ATKB/15.png", "./Public/Players/2/ATKB/16.png", "./Public/Players/2/ATKB/17.png", "./Public/Players/2/ATKB/18.png", "./Public/Players/2/ATKB/19.png"],
        sound: "./Public/Players/2/ATKB/ATKB.mp3",
        x: 35,
        y: 5,
    },
    usemagic: ["./Public/Players/2/usemagic/0.png", "./Public/Players/2/usemagic/1.png"],
    function: function () {
        //畫面震動
        setTimeout(() => {
            Dom.BattleScreen.style.animation = "shakeOtoLU 0.2s ease-in-out";
            setTimeout(() => {
                Dom.BattleScreen.style.animation = "null";
            }, 100)
        }, 500);
    },
    bloodfun: function (playergamage, enemy) {
        return new Promise((resolve) => {
            let tempdamge = playergamage / 3
            let originalHP = enemy.CurHP;  // 保存原始HP值

            let enemyhpbar = document.getElementById("enemybattlehpbar");
            let enemyhpinner = document.getElementById("enemybattlehp-inner");
            let enemyhpinnerdelay = document.getElementById("enemybattlehpbar_inner");
            let enemyhptext = document.getElementById("enemybattlehptext");

            enemyhpinner.style.width = `${(originalHP / enemy.HP) * 490}px`;
            enemyhptext.textContent = `${Math.floor(originalHP)} / ${enemy.HP}`;
            enemyhpinnerdelay.style.width = `${(originalHP / enemy.HP) * 100}%`;

            setTimeout(() => {
                enemyhpinner.style.width = `${(Math.max(0, originalHP - tempdamge) / enemy.HP) * 490}px`;
                enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - tempdamge) / enemy.HP) * 100}%`;
                enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - tempdamge))} / ${enemy.HP}`;
                enemyhpbar.style.animation = "lll 0.3s linear forwards";
                setTimeout(() => {
                    enemyhpbar.style.animation = "null";
                }, 100)
                setTimeout(() => {
                    enemyhpinner.style.width = `${(Math.max(0, originalHP - playergamage) / enemy.HP) * 490}px`;
                    enemyhpinnerdelay.style.width = `${(Math.max(0, originalHP - playergamage) / enemy.HP) * 100}%`;
                    enemyhptext.textContent = `${Math.floor(Math.max(0, originalHP - playergamage))} / ${enemy.HP}`;
                    enemy.CurHP = Math.max(0, originalHP - playergamage);  // 只在動畫完成後更新實際HP
                    enemyhpbar.style.animation = "lll 0.3s linear forwards";
                    setTimeout(() => {
                        enemyhpbar.style.animation = "null";
                        resolve(enemy.CurHP <= 0); // 返回是否敵人死亡
                    }, 100)
                }, 400)
            }, 400)
        }, 400)

    }
}



