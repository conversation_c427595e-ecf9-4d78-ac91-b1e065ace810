<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客棧場景測試</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft JhengHei', <PERSON>l, sans-serif;
            background: #222;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            padding: 10px 20px;
            margin: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        #sidebar {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 200px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🏨 客棧場景測試頁面</h1>
        
        <div class="test-result">
            <h3>測試狀態</h3>
            <div id="testStatus">正在檢查...</div>
        </div>

        <div>
            <button class="test-button" onclick="testHostelSceneClass()">測試 HostelScene 類別</button>
            <button class="test-button" onclick="testCreateHostelScene()">測試創建客棧場景</button>
            <button class="test-button" onclick="testSceneManager()">測試場景管理器</button>
            <button class="test-button" onclick="simulateHostelEntry()">模擬進入客棧</button>
        </div>

        <div id="testResults"></div>
    </div>

    <div id="sidebar">
        <h4>Sidebar 測試</h4>
        <p>這裡會顯示客棧按鈕</p>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="APP/HostelScene.js"></script>
    <script src="APP/Init.js"></script>

    <script>
        // 測試函數
        function addTestResult(title, result, isError = false) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isError ? 'error' : ''}`;
            resultDiv.innerHTML = `<h4>${title}</h4><pre>${result}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }

        function testHostelSceneClass() {
            try {
                const result = [];
                result.push(`typeof HostelScene: ${typeof HostelScene}`);
                result.push(`typeof window.HostelScene: ${typeof window.HostelScene}`);
                
                if (typeof HostelScene !== 'undefined') {
                    result.push('✅ HostelScene 類別已定義');
                    result.push(`HostelScene.name: ${HostelScene.name}`);
                    result.push(`HostelScene.prototype: ${Object.getOwnPropertyNames(HostelScene.prototype)}`);
                } else {
                    result.push('❌ HostelScene 類別未定義');
                }

                addTestResult('HostelScene 類別測試', result.join('\n'));
            } catch (error) {
                addTestResult('HostelScene 類別測試', `錯誤: ${error.message}`, true);
            }
        }

        function testCreateHostelScene() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義');
                }

                const hostelScene = new HostelScene(0, {}, 'test');
                const result = [];
                result.push('✅ HostelScene 實例創建成功');
                result.push(`hostelIndex: ${hostelScene.hostelIndex}`);
                result.push(`previousScene: ${hostelScene.previousScene}`);
                result.push(`isLoaded: ${hostelScene.isLoaded}`);
                result.push(`isRunning: ${hostelScene.isRunning}`);

                addTestResult('創建 HostelScene 實例', result.join('\n'));
            } catch (error) {
                addTestResult('創建 HostelScene 實例', `錯誤: ${error.message}`, true);
            }
        }

        function testSceneManager() {
            try {
                const result = [];
                result.push(`typeof sceneManager: ${typeof sceneManager}`);
                
                if (typeof sceneManager !== 'undefined') {
                    result.push('✅ sceneManager 已定義');
                    result.push(`switchToHostel 方法: ${typeof sceneManager.switchToHostel}`);
                } else {
                    result.push('❌ sceneManager 未定義');
                }

                addTestResult('場景管理器測試', result.join('\n'));
            } catch (error) {
                addTestResult('場景管理器測試', `錯誤: ${error.message}`, true);
            }
        }

        function simulateHostelEntry() {
            try {
                if (typeof HostelScene === 'undefined') {
                    throw new Error('HostelScene 類別未定義，無法模擬');
                }

                // 創建模擬的場景管理器
                if (typeof sceneManager === 'undefined') {
                    window.sceneManager = {
                        currentScene: null,
                        sceneType: null,
                        previousScene: null,
                        gameData: { playerData: {} },
                        isTransitioning: false,
                        
                        switchToHostel: function(hostelIndex, previousScene) {
                            console.log(`模擬切換到客棧: ${hostelIndex}, 來源: ${previousScene}`);
                            this.previousScene = this.sceneType;
                            this.sceneType = 'hostel';
                            
                            const hostelScene = new HostelScene(hostelIndex, this.gameData.playerData, previousScene);
                            this.currentScene = hostelScene;
                            hostelScene.init();
                            
                            return true;
                        }
                    };
                }

                const success = sceneManager.switchToHostel(0, 'camp');
                addTestResult('模擬進入客棧', success ? '✅ 成功進入客棧場景' : '❌ 進入客棧失敗');
                
            } catch (error) {
                addTestResult('模擬進入客棧', `錯誤: ${error.message}`, true);
            }
        }

        // 頁面載入完成後執行初始檢查
        window.addEventListener('load', function() {
            const statusDiv = document.getElementById('testStatus');
            const status = [];
            
            status.push(`HostelScene: ${typeof HostelScene !== 'undefined' ? '✅' : '❌'}`);
            status.push(`window.HostelScene: ${typeof window.HostelScene !== 'undefined' ? '✅' : '❌'}`);
            
            statusDiv.innerHTML = status.join('<br>');
            
            // 自動執行基本測試
            setTimeout(() => {
                testHostelSceneClass();
            }, 500);
        });

        // 監聽 HostelScene 準備就緒事件
        document.addEventListener('hostelSceneReady', function(event) {
            console.log('HostelScene 準備就緒事件觸發:', event.detail);
            addTestResult('事件監聽', '✅ HostelScene 準備就緒事件已觸發');
        });
    </script>
</body>
</html>
