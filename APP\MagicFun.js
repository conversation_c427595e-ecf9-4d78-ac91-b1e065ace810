function magicfun(player, leftmove) {
    let myMagic = player["法術"]

    CreateMagicWin(player, leftmove, myMagic)
}

// 清除所有法術相關的事件監聽器
function clearAllMagicEventHandlers() {
    console.log("清除所有法術事件處理器");

    // 只清除Canvas上的法術相關事件監聽器
    if (window.currentMagicUseClickHandler) {
        canvas.removeEventListener('click', window.currentMagicUseClickHandler);
        window.currentMagicUseClickHandler = null;
        console.log("已清除法術使用點擊事件處理器");
    }

    if (window.currentMagicUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentMagicUseMouseMoveHandler);
        window.currentMagicUseMouseMoveHandler = null;
        console.log("已清除法術使用滑鼠移動事件處理器");
    }

    console.log("法術事件處理器清除完成");
}

// 新的Canvas系統法術範圍繪製函數（類似BagFun.js的cursor機制）
function drawMagicRangeCanvas(player, magicname, distance, rmdistance, range) {
    console.log(`使用Canvas系統繪製法術範圍: ${magicname}, 施行距離: ${distance}, 施行範圍: ${range}`);

    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    // 清除之前的高亮
    clearAllHighlights();

    // 存儲可施法的位置
    const usablePositions = [];
    const currentMapSize = controlLayer[currentLevel].size;
    const playerX = player.Position % currentMapSize.cols;
    const playerY = Math.floor(player.Position / currentMapSize.cols);

    // 如果玩家自己的位置在施法距離內，添加到可用位置
    if (rmdistance <= 0 && 0 <= distance) {
        usablePositions.push(player.Position);
        highlightCells.push({
            x: playerX,
            y: playerY,
            color: 'rgba(0, 255, 255, 0.6)' // 青色表示可施法位置
        });
    }

    // 使用BFS計算施行距離範圍（distance 是玩家可以移動到的範圍來施法）
    const queue = [{ position: player.Position, distance: 0 }];
    const visited = new Set();
    visited.add(player.Position);

    while (queue.length > 0) {
        const { position, distance: currentDistance } = queue.shift();

        // distance 是施行距離，表示玩家可以移動多遠來施法
        if (currentDistance < distance) {
            directions.forEach(direction => {
                const newX = (position % currentMapSize.cols) + direction.x;
                const newY = Math.floor(position / currentMapSize.cols) + direction.y;
                const newPosition = newY * currentMapSize.cols + newX;

                // 檢查邊界
                if (newX >= 0 && newX < currentMapSize.cols && newY >= 0 && newY < currentMapSize.rows) {
                    if (!visited.has(newPosition)) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: currentDistance + 1 });

                        // 計算曼哈頓距離
                        const manhattanDistance = Math.abs(playerX - newX) + Math.abs(playerY - newY);

                        // 只高亮在施行距離範圍內的位置（distance 是施行距離）
                        if (manhattanDistance >= rmdistance && manhattanDistance <= distance) {
                            usablePositions.push(newPosition);
                            highlightCells.push({
                                x: newX,
                                y: newY,
                                color: 'rgba(245, 0, 216, 0.9)' // 紫色表示可施法位置
                            });
                        }
                    }
                }
            });
        }
    }

    // 渲染高亮
    render();

    // 設置canvas點擊事件處理，傳遞 range 作為施行範圍
    setupMagicUseClickHandler(usablePositions, player, magicname, range);

    console.log(`法術施行距離計算完成，共 ${usablePositions.length} 個可用位置`);
}

// 設置法術使用的canvas點擊事件處理（類似BagFun.js的cursor機制）
function setupMagicUseClickHandler(usablePositions, player, magicname, range) {
    // 移除之前的點擊事件監聽器
    if (window.currentMagicUseClickHandler) {
        canvas.removeEventListener('click', window.currentMagicUseClickHandler);
        window.currentMagicUseClickHandler = null;
    }

    // 移除之前的滑鼠移動事件監聽器
    if (window.currentMagicUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentMagicUseMouseMoveHandler);
        window.currentMagicUseMouseMoveHandler = null;
    }

    // 存儲當前高亮的法術作用範圍
    let currentMagicRangeHighlights = [];

    // 創建滑鼠移動處理器 - 用於顯示法術作用範圍（cursor機制）
    const magicUseMouseMoveHandler = async function (event) {
        // 獲取滑鼠位置（使用當前的鏡頭位置，避免鏡頭移動導致的座標錯誤）
        const rect = canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // 使用當前的鏡頭位置計算世界座標
        const worldX = mouseX + cameraX;
        const worldY = mouseY + cameraY;

        // 計算滑鼠所在的格子位置
        const hoveredCol = Math.floor(worldX / cellWidth);
        const hoveredRow = Math.floor(worldY / cellHeight);
        const hoveredPosition = hoveredRow * controlLayer[currentLevel].size.cols + hoveredCol;

        // 檢查是否在可施法範圍內
        if (usablePositions.includes(hoveredPosition)) {
            // 檢查防呆機制：範圍內是否有有效目標
            const hasValidTargets = checkValidTargetsInRange(hoveredPosition, magicname, range);

            // 清除之前的法術作用範圍高亮
            clearAllMagicRangeHighlights();
            currentMagicRangeHighlights = [];

            // 只有在有有效目標時才顯示法術作用範圍
            if (hasValidTargets) {
                try {
                    currentMagicRangeHighlights = await showMagicEffectRange(hoveredPosition, magicname, range);
                } catch (error) {
                    console.error('顯示法術作用範圍失敗:', error);
                    currentMagicRangeHighlights = [];
                }
            }
        } else {
            // 如果不在可施法範圍內，清除法術作用範圍高亮
            clearAllMagicRangeHighlights();
            currentMagicRangeHighlights = [];
        }
    };

    // 創建新的點擊處理器
    const magicUseClickHandler = function (event) {
        // 獲取點擊位置（使用當前的鏡頭位置，避免鏡頭移動導致的座標錯誤）
        const rect = canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // 添加小延遲以確保鏡頭移動完成後再計算座標
        setTimeout(() => {
            // 使用當前的鏡頭位置計算世界座標
            const worldX = mouseX + cameraX;
            const worldY = mouseY + cameraY;

            // 計算點擊的格子位置
            const clickedCol = Math.floor(worldX / cellWidth);
            const clickedRow = Math.floor(worldY / cellHeight);
            const clickedPosition = clickedRow * controlLayer[currentLevel].size.cols + clickedCol;

            console.log(`點擊位置: 滑鼠(${mouseX}, ${mouseY}) + 鏡頭(${cameraX}, ${cameraY}) = 世界(${worldX}, ${worldY}) = 格子(${clickedRow}, ${clickedCol}) = 位置${clickedPosition}`);

            // 檢查是否點擊了可施法的位置
            if (usablePositions.includes(clickedPosition)) {
                // 檢查防呆機制：範圍內是否有有效目標
                const hasValidTargets = checkValidTargetsInRange(clickedPosition, magicname, range);

                if (hasValidTargets) {
                    console.log(`在可施法位置 ${clickedPosition} 施放法術 ${magicname}`);

                    // 移除事件監聽器
                    canvas.removeEventListener('click', magicUseClickHandler);
                    canvas.removeEventListener('mousemove', magicUseMouseMoveHandler);
                    window.currentMagicUseClickHandler = null;
                    window.currentMagicUseMouseMoveHandler = null;

                    // 清除法術作用範圍高亮
                    clearAllMagicRangeHighlights();

                    // 執行法術施放邏輯
                    executeMagicUse(clickedPosition, player, magicname);
                } else {
                    console.log("法術作用範圍內沒有有效目標，無法施法");
                }
            } else {
                console.log("點擊位置不在可施法範圍內");
            }
        }, 100); // 100ms延遲，確保鏡頭移動完成
    };

    // 添加事件監聽器
    canvas.addEventListener('click', magicUseClickHandler);
    canvas.addEventListener('mousemove', magicUseMouseMoveHandler);
    window.currentMagicUseClickHandler = magicUseClickHandler;
    window.currentMagicUseMouseMoveHandler = magicUseMouseMoveHandler;

    console.log("法術使用點擊和滑鼠移動事件監聽器已設置");
}

// 檢查法術作用範圍內是否有有效目標（防呆機制）
function checkValidTargetsInRange(centerPosition, magicname, range) {
    // 獲取法術信息
    const magicInfo = MagicDB.find(m => m.name === magicname);
    if (!magicInfo) {
        console.error(`找不到法術信息: ${magicname}`);
        return false;
    }

    const classify = magicInfo.Classify || "";
    console.log(`檢查法術 ${magicname} (${classify}) 在位置 ${centerPosition} 範圍 ${range} 內的有效目標`);

    if (classify === "治癒") {
        // 治癒法術：檢查範圍內是否有存活的玩家（暫時放寬條件以確保cursor顯示）
        const playersInRange = getPlayersInMagicRange(centerPosition, range);
        const validPlayers = playersInRange.filter(player => player.CurHP > 0);
        console.log(`治癒法術範圍內有 ${validPlayers.length} 個存活玩家目標`);
        return validPlayers.length > 0;
    } else {
        // 攻擊法術：檢查範圍內是否有存活的敵人
        const enemiesInRange = getEnemiesInMagicRange(centerPosition, range);
        const validEnemies = enemiesInRange.filter(enemy => enemy.CurHP > 0);
        console.log(`攻擊法術範圍內有 ${validEnemies.length} 個有效敵人目標`);
        return validEnemies.length > 0;
    }
}

// 清除所有法術作用範圍高亮（參考BagFun.js的方式）
function clearAllMagicRangeHighlights() {
    // 移除所有 rangeImage 類型的高亮（與法術相關的）
    for (let i = highlightCells.length - 1; i >= 0; i--) {
        if (highlightCells[i].type === 'rangeImage' && highlightCells[i].magicType) {
            highlightCells.splice(i, 1);
        }
    }
    render();
}

// 顯示法術作用範圍（類似BagFun.js的cursor機制）
async function showMagicEffectRange(centerPosition, magicname, range) {
    const magicRangeHighlights = [];

    // 獲取法術信息以確定類型
    const magicInfo = MagicDB.find(m => m.name === magicname);
    const classify = magicInfo?.Classify || "";

    // 確定使用的範圍目錄和尺寸
    const centerX = centerPosition % controlLayer[currentLevel].size.cols;
    const centerY = Math.floor(centerPosition / controlLayer[currentLevel].size.cols);

    // 根據range確定圖片尺寸（與BagFun.js完全一致）
    let imageWidth, imageHeight;
    switch (range) {
        case 0:
            imageWidth = 110;
            imageHeight = 88;
            break;
        case 1:
            imageWidth = 330;
            imageHeight = 264;
            break;
        default:
            // 可以根據需要添加更多range的尺寸
            imageWidth = 110 + (range * 110); // 預設按比例增加
            imageHeight = 88 + (range * 88);
            break;
    }

    // 載入範圍圖片並創建高亮物件（參考BagFun.js的方式）
    try {
        const rangeImages = await loadMagicRangeImages(range, classify);
        console.log(`法術 ${magicname} 載入了 ${rangeImages.length} 張cursor圖片`);

        if (rangeImages.length === 0) {
            console.warn(`法術 ${magicname} 沒有載入到任何cursor圖片，跳過顯示`);
            return [];
        }

        const rangeHighlightObj = {
            x: centerX,
            y: centerY,
            type: 'rangeImage', // 使用與BagFun.js相同的類型
            images: rangeImages,
            currentFrame: 0,
            frameInterval: 100, // 與BagFun.js一致，每100ms切換一幀
            lastFrameTime: 0, // 與BagFun.js一致
            imageWidth: imageWidth, // 與BagFun.js一致
            imageHeight: imageHeight, // 與BagFun.js一致
            magicType: classify // 記錄法術類型
        };

        // 添加到highlightCells（與BagFun.js一致）
        highlightCells.push(rangeHighlightObj);
        magicRangeHighlights.push(rangeHighlightObj);

        // 重新渲染
        render();

        // 如果有圖片動畫，啟動動畫循環
        if (rangeImages.length > 0 && typeof startAnimationLoop === 'function') {
            startAnimationLoop();
        }

        console.log(`顯示法術作用範圍: ${magicname} (${classify}), range=${range}, 位置=(${centerX}, ${centerY}), 圖片數量=${rangeImages.length}`);

    } catch (error) {
        console.error(`載入法術範圍cursor圖片失敗`, error);
    }

    return magicRangeHighlights;
}

// 載入法術範圍圖片陣列（參考BagFun.js）
async function loadMagicRangeImages(rangeDir, magicType) {
    console.log(`載入法術範圍圖片: 類型=${magicType}, 範圍=${rangeDir}`);

    // 暫時讓所有法術都使用通用cursor，確保cursor能正常顯示
    const images = [];
    const imagePromises = [];

    // 直接載入通用cursor目錄下的圖片
    for (let i = 0; i < 36; i++) { // 與BagFun.js一致，最多36張圖片
        const imagePath = `./Public/Cursor/${rangeDir}/${i}.png`;
        const promise = preloadImage(imagePath).then(img => {
            images[i] = img;
            return img;
        }).catch(() => {
            // 如果圖片不存在，返回null
            return null;
        });
        imagePromises.push(promise);
    }

    // 等待所有圖片載入完成
    await Promise.all(imagePromises);

    // 過濾掉null值，只保留成功載入的圖片
    const validImages = images.filter(img => img !== null && img !== undefined);

    console.log(`成功載入法術範圍 ${rangeDir} 的 ${validImages.length} 張圖片`);
    return validImages;
}

// 準備法術使用
function prepareMagicUse(player, magicname) {
    console.log(`準備法術使用: ${player.name} - ${magicname}`);

    // 獲取法術信息
    const magicInfo = MagicDB.find(m => m.name === magicname);
    if (!magicInfo) {
        console.error(`找不到法術信息: ${magicname}`);
        return false;
    }

    // 檢查玩家是否已經行動過
    if (player.AlreadyMove) {
        console.error(`${player.name} 已經完成行動，無法使用法術`);
        return false;
    }

    // 檢查MP是否足夠
    const needMP = magicInfo.NeedMP || 0;
    const currentMP = getPlayerMP(player);
    if (currentMP < needMP) {
        console.error(`${player.name} MP不足: 需要${needMP}, 當前${currentMP}`);
        return false;
    }

    // 檢查移動力是否足夠
    const needMove = magicInfo.NeedMove || 0;
    if (player.Move < needMove) {
        console.error(`${player.name} 移動力不足: 需要${needMove}, 當前${player.Move}`);
        return false;
    }

    // 檢查玩家是否存活
    if (player.CurHP <= 0) {
        console.error(`${player.name} 已死亡，無法使用法術`);
        return false;
    }

    // 禁用玩家控制，防止重複操作
    disablePlayerControls();

    console.log(`法術使用準備完成: ${player.name} - ${magicname}`);
    return true;
}

// 獲取玩家當前MP
function getPlayerMP(player) {
    return player.CurMP || player.curMp || 0;
}

// 設置玩家當前MP
function setPlayerMP(player, newMP) {
    if (player.CurMP !== undefined) {
        player.CurMP = newMP;
    } else if (player.curMp !== undefined) {
        player.curMp = newMP;
    } else {
        // 如果都不存在，創建CurMP屬性
        player.CurMP = newMP;
    }
}

// 獲取玩家最大MP
function getPlayerMaxMP(player) {
    return player.MP || player.Mp || 0;
}

// 禁用玩家控制
function disablePlayerControls() {
    console.log("禁用玩家控制");

    // 只禁用UI元素，不禁用角色點擊
    const clickableElements = document.querySelectorAll('button, .clickable, .usemagicitem');
    clickableElements.forEach(element => {
        if (!element.hasAttribute('disabled')) {
            element.setAttribute('disabled', 'true');
            element.classList.add('magic-disabled');
        }
    });

    // 暫時禁用角色選擇（但不清除事件）
    mapObjects.forEach(obj => {
        if (obj.type === 'player') {
            obj.tempDisabled = true; // 使用臨時禁用標記
        }
    });

    console.log("玩家控制已禁用（保留角色點擊事件）");
}

// 執行法術施放邏輯
async function executeMagicUse(targetPosition, player, magicname) {
    console.log(`執行法術施放: 位置${targetPosition}, 玩家${player.name}, 法術${magicname}`);

    // 檢查玩家是否已經完成行動
    if (player.AlreadyMove) {
        console.warn(`${player.name} 已經完成行動，無法再使用法術`);
        return;
    }

    // 清除所有事件監聽器
    if (window.currentMagicUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentMagicUseMouseMoveHandler);
        window.currentMagicUseMouseMoveHandler = null;
    }

    // 清除高亮
    clearAllHighlights();
    clearAllMagicRangeHighlights();

    // 準備法術使用
    if (!prepareMagicUse(player, magicname)) {
        console.error(`法術使用準備失敗: ${player.name} - ${magicname}`);
        await completeMagicUse(player);
        return;
    }

    // 獲取法術信息
    const magicInfo = MagicDB.find(m => m.name === magicname);
    if (!magicInfo) {
        console.error(`找不到法術信息: ${magicname}`);
        await completeMagicUse(player);
        return;
    }

    // 扣除MP和移動力
    const oldMP = getPlayerMP(player);
    const oldMove = player.Move;
    const needMP = magicInfo.NeedMP || 0;
    const needMove = magicInfo.NeedMove || 0;

    // 扣除MP
    const newMP = Math.max(0, oldMP - needMP);
    setPlayerMP(player, newMP);

    // 扣除移動力
    player.Move -= needMove;

    console.log(`${player.name} 消耗: MP ${oldMP} → ${newMP} (-${needMP}), 移動力 ${oldMove} → ${player.Move} (-${needMove})`);

    // 獲取目標
    const classify = magicInfo.Classify || "";
    let targets = [];

    if (classify === "治癒") {
        // 治癒法術只影響玩家
        targets = getPlayersInMagicRange(targetPosition, magicInfo.Range || 0);
    } else {
        // 攻擊法術只影響敵人
        targets = getEnemiesInMagicRange(targetPosition, magicInfo.Range || 0);
    }

    if (targets.length > 0) {
        console.log(`法術 ${magicname} 影響目標:`, targets.map(t => t.name));

        try {
            // 執行法術動畫和效果
            await playMagicAnimation(player, magicname, targets, targetPosition);
            console.log(`法術 ${magicname} 動畫和效果執行完成`);
        } catch (error) {
            console.error(`法術 ${magicname} 執行過程中發生錯誤:`, error);
            // 即使出錯也要繼續清理流程
        }
    } else {
        console.log(`法術 ${magicname} 沒有有效目標`);
    }

    // 完成法術使用後的清理工作
    try {
        await completeMagicUse(player);
        console.log(`法術使用完成，${player.name} 的回合結束`);
    } catch (error) {
        console.error(`完成法術使用時發生錯誤:`, error);
    }
}

// 獲取法術範圍內的玩家
function getPlayersInMagicRange(centerPosition, range) {
    const targets = [];
    const centerX = centerPosition % controlLayer[currentLevel].size.cols;
    const centerY = Math.floor(centerPosition / controlLayer[currentLevel].size.cols);

    for (const player of gameplayers) {
        if (player.CurHP <= 0) continue; // 跳過死亡玩家

        const playerX = player.Position % controlLayer[currentLevel].size.cols;
        const playerY = Math.floor(player.Position / controlLayer[currentLevel].size.cols);
        const distance = Math.abs(centerX - playerX) + Math.abs(centerY - playerY);

        if (distance <= range) {
            targets.push(player);
        }
    }

    return targets;
}

// 獲取法術範圍內的敵人
function getEnemiesInMagicRange(centerPosition, range) {
    const targets = [];
    const centerX = centerPosition % controlLayer[currentLevel].size.cols;
    const centerY = Math.floor(centerPosition / controlLayer[currentLevel].size.cols);

    for (const enemy of gameenemys) {
        if (enemy.CurHP <= 0) continue; // 跳過死亡敵人

        const enemyX = enemy.Position % controlLayer[currentLevel].size.cols;
        const enemyY = Math.floor(enemy.Position / controlLayer[currentLevel].size.cols);
        const distance = Math.abs(centerX - enemyX) + Math.abs(centerY - enemyY);

        if (distance <= range) {
            targets.push(enemy);
        }
    }

    return targets;
}

// 完成法術使用後的清理工作
async function completeMagicUse(player) {
    console.log(`玩家 ${player.name} 完成法術使用`);

    // 施法者動畫恢復現在在playMagicAnimation函數中處理

    // 標記玩家已完成行動（使用強制設置）
    const moveSetSuccess = forceSetPlayerMoved(player);

    if (!moveSetSuccess) {
        console.error(`嚴重錯誤: 無法設置 ${player.name} 的 AlreadyMove 狀態`);
    }

    // 驗證玩家狀態
    console.log(`${player.name} 法術使用後狀態: AlreadyMove=${player.AlreadyMove}, HP=${player.CurHP}/${player.HP}, MP=${player.curMp}/${player.Mp}, Move=${player.Move}`);

    // 關閉法術對話框
    const magicDialog = document.getElementById("magicdialog");
    if (magicDialog) {
        magicDialog.close();
        console.log("法術對話框已關閉");
    }

    // 清除信息對話框
    const infoDialog = document.getElementById("InfoDialog");
    if (infoDialog) {
        infoDialog.innerHTML = "";
        console.log("信息對話框已清除");
    }

    // 調試：檢查清除前的狀態
    debugRunOBJState("completeMagicUse開始時");

    // 立即清除所有可能阻止點擊的狀態
    runOBJ["當前操作"] = null;
    runOBJ["當前物品操作"] = null;
    runOBJ["當前選取"] = null;
    runOBJ["第二次選取"] = null;
    runOBJ["當前選取物品"] = null;
    console.log("已立即清除所有runOBJ狀態");

    // 調試：檢查清除後的狀態
    debugRunOBJState("runOBJ狀態清除後");

    // 清除所有高亮和事件監聽器
    clearAllHighlights();
    clearAllMagicRangeHighlights();
    clearAllMagicEventHandlers();
    console.log("所有高亮和事件監聽器已清除");

    // 清除所有法術相關的動畫物件
    clearAllMagicAnimations();

    // 更新地圖物件
    updateMapObjects();
    console.log("地圖物件已更新");

    // 更新玩家狀態顯示
    updatePlayerStatusDisplay();

    // 檢查遊戲狀態（是否有玩家死亡等）
    checkGameStatus();

    // 驗證並顯示所有玩家的行動狀態
    validateAllPlayersStatus();

    // 檢查是否所有玩家都已完成行動
    const allMoved = allPlayersMoved();
    console.log(`檢查所有玩家行動狀態: ${allMoved ? '全部完成' : '還有玩家未行動'}`);

    if (allMoved) {
        runOBJ["當前行動方"] = "Enemys";
        console.log("所有玩家已完成行動，切換到敵人回合");

        // 給玩家一點時間看到最終結果
        setTimeout(() => {
            console.log("開始敵人行動");
            if (typeof EnemysAction === 'function') {
                EnemysAction();
            } else {
                console.error("EnemysAction 函數不存在");
            }
        }, 1000); // 增加到1秒，讓玩家看到效果
    } else {
        runOBJ["當前行動方"] = "Players";
        console.log("等待其他玩家行動");

        // 重新啟用玩家控制
        enablePlayerControls();
    }
}

// 驗證所有玩家的狀態
function validateAllPlayersStatus() {
    console.log("=== 驗證所有玩家狀態 ===");

    gameplayers.forEach((player, index) => {
        console.log(`玩家 ${index + 1}: ${player.name}`);
        console.log(`  - AlreadyMove: ${player.AlreadyMove}`);
        console.log(`  - HP: ${player.CurHP}/${player.HP}`);
        console.log(`  - MP: ${player.curMp}/${player.Mp}`);
        console.log(`  - Move: ${player.Move}`);
        console.log(`  - 存活狀態: ${player.CurHP > 0 ? '存活' : '死亡'}`);

        // 檢查狀態一致性
        if (player.CurHP <= 0 && !player.AlreadyMove) {
            console.warn(`  警告: ${player.name} 已死亡但 AlreadyMove 為 false，自動設置為 true`);
            player.AlreadyMove = true;
        }
    });

    console.log("=== 狀態驗證完成 ===");
}

// 強制設置玩家已完成行動
function forceSetPlayerMoved(player) {
    console.log(`強制設置 ${player.name} 為已完成行動`);

    // 多重確保設置成功
    player.AlreadyMove = true;

    // 驗證設置是否成功
    if (player.AlreadyMove !== true) {
        console.error(`錯誤: 無法設置 ${player.name} 的 AlreadyMove 狀態`);

        // 嘗試使用不同的方式設置
        try {
            Object.defineProperty(player, 'AlreadyMove', {
                value: true,
                writable: true,
                enumerable: true,
                configurable: true
            });
        } catch (error) {
            console.error(`設置 ${player.name} AlreadyMove 屬性失敗:`, error);
        }
    }

    console.log(`${player.name} AlreadyMove 最終狀態: ${player.AlreadyMove}`);
    return player.AlreadyMove === true;
}

// 清除所有法術相關的動畫物件
function clearAllMagicAnimations() {
    console.log("清除所有法術動畫物件");

    // 清除所有法術相關的動畫物件
    for (let i = mapObjects.length - 1; i >= 0; i--) {
        const obj = mapObjects[i];
        if (obj.type === 'spellEffect' ||
            obj.type === 'targetEffect' ||
            obj.type === 'useMagicEffect' ||
            obj.type === 'magicEffect' ||
            obj.type === 'recoveryNumber' ||
            obj.type === 'damageNumber') {
            mapObjects.splice(i, 1);
            console.log(`移除法術動畫物件: ${obj.type}`);
        }
    }

    // 重新渲染
    render();
}

// 更新玩家狀態顯示
function updatePlayerStatusDisplay() {
    console.log("更新玩家狀態顯示");

    // 更新所有玩家的HP/MP顯示
    gameplayers.forEach(player => {
        // 這裡可以添加更新UI的邏輯
        console.log(`${player.name}: HP ${player.CurHP}/${player.HP}, MP ${player.curMp}/${player.Mp}`);
    });

    // 如果有狀態欄，更新狀態欄
    if (typeof updateStatusBar === 'function') {
        updateStatusBar();
    }
}

// 檢查遊戲狀態
function checkGameStatus() {
    console.log("檢查遊戲狀態");

    // 檢查是否有玩家死亡
    const deadPlayers = gameplayers.filter(player => player.CurHP <= 0);
    if (deadPlayers.length > 0) {
        console.log(`發現死亡玩家: ${deadPlayers.map(p => p.name).join(', ')}`);

        // 處理玩家死亡邏輯
        deadPlayers.forEach(player => {
            if (!player.isDead) {
                player.isDead = true;
                console.log(`${player.name} 死亡`);

                // 可以添加死亡動畫或效果
                if (typeof handlePlayerDeath === 'function') {
                    handlePlayerDeath(player);
                }
            }
        });
    }

    // 檢查是否所有玩家都死亡（遊戲結束）
    const alivePlayers = gameplayers.filter(player => player.CurHP > 0);
    if (alivePlayers.length === 0) {
        console.log("所有玩家死亡，遊戲結束");
        if (typeof handleGameOver === 'function') {
            handleGameOver();
        }
    }

    // 檢查是否所有敵人都死亡（勝利）
    if (typeof gameEnemys !== 'undefined') {
        const aliveEnemies = gameEnemys.filter(enemy => enemy.CurHP > 0);
        if (aliveEnemies.length === 0) {
            console.log("所有敵人死亡，玩家勝利");
            if (typeof handleVictory === 'function') {
                handleVictory();
            }
        }
    }
}

// 重新啟用玩家控制
function enablePlayerControls() {
    console.log("重新啟用玩家控制");

    // 移除所有禁用狀態
    const disabledElements = document.querySelectorAll('[disabled]');
    disabledElements.forEach(element => {
        if (element.classList.contains('magic-disabled')) {
            element.removeAttribute('disabled');
            element.classList.remove('magic-disabled');
        }
    });

    // 重新啟用地圖點擊事件
    if (typeof enableMapClicks === 'function') {
        enableMapClicks();
        console.log("已重新啟用地圖點擊事件");
    }

    // 重新啟用角色選擇
    if (typeof enableCharacterSelection === 'function') {
        enableCharacterSelection();
        console.log("已重新啟用角色選擇");
    }

    // 重新設置角色點擊事件（確保角色可以被點擊）
    restoreCharacterClickEvents();

    // 重新啟用可點擊角色高亮
    if (typeof highlightClickableCharacters === 'function') {
        highlightClickableCharacters();
        console.log("已重新啟用可點擊角色高亮");
    }

    // 確保遊戲狀態正確
    resetGameStateForPlayerControl();

    // 調試：檢查最終狀態
    debugRunOBJState("enablePlayerControls完成後");

    // 檢查哪些玩家可以被點擊
    const clickablePlayers = checkClickablePlayers();

    if (clickablePlayers.length === 0) {
        console.warn("警告: 沒有可點擊的玩家！");
    }

    console.log("玩家控制重新啟用完成");
}

// 調試函數：檢查runOBJ狀態
function debugRunOBJState(context) {
    console.log(`=== ${context} - runOBJ狀態檢查 ===`);
    console.log("當前行動方:", runOBJ["當前行動方"]);
    console.log("當前操作:", runOBJ["當前操作"]);
    console.log("當前物品操作:", runOBJ["當前物品操作"]);
    console.log("當前選取:", runOBJ["當前選取"]);
    console.log("第二次選取:", runOBJ["第二次選取"]);
    console.log("當前選取物品:", runOBJ["當前選取物品"]);
    console.log("=== 狀態檢查完成 ===");
}

// 檢查哪些玩家可以被點擊
function checkClickablePlayers() {
    console.log("=== 檢查可點擊玩家 ===");

    gameplayers.forEach((player, index) => {
        const canClick = !player.AlreadyMove && player.CurHP > 0 && !player["是否電腦操作"];
        console.log(`玩家 ${index}: ${player.name}`);
        console.log(`  - AlreadyMove: ${player.AlreadyMove}`);
        console.log(`  - CurHP: ${player.CurHP}`);
        console.log(`  - 是否電腦操作: ${player["是否電腦操作"]}`);
        console.log(`  - 可點擊: ${canClick ? '是' : '否'}`);

        if (!canClick) {
            if (player.AlreadyMove) console.log(`    原因: 已完成行動`);
            if (player.CurHP <= 0) console.log(`    原因: 已死亡`);
            if (player["是否電腦操作"]) console.log(`    原因: 電腦操作`);
        }
    });

    const clickablePlayers = gameplayers.filter((player, index) =>
        !player.AlreadyMove && player.CurHP > 0 && !player["是否電腦操作"]
    );

    console.log(`可點擊玩家數量: ${clickablePlayers.length}`);
    console.log("=== 檢查完成 ===");

    return clickablePlayers;
}

// 重置遊戲狀態以便玩家控制
function resetGameStateForPlayerControl() {
    console.log("重置遊戲狀態以便玩家控制");

    // 確保當前行動方是玩家
    if (runOBJ["當前行動方"] !== "Players") {
        console.log(`當前行動方從 ${runOBJ["當前行動方"]} 重置為 Players`);
        runOBJ["當前行動方"] = "Players";
    }

    // 再次確保所有狀態都被清除（防止其他地方的延遲設置）
    runOBJ["當前操作"] = null;
    runOBJ["當前物品操作"] = null;
    runOBJ["當前選取"] = null;
    runOBJ["第二次選取"] = null;
    runOBJ["當前選取物品"] = null;

    console.log("遊戲狀態重置完成");
    console.log("當前runOBJ狀態:", {
        "當前行動方": runOBJ["當前行動方"],
        "當前操作": runOBJ["當前操作"],
        "當前物品操作": runOBJ["當前物品操作"],
        "當前選取": runOBJ["當前選取"],
        "第二次選取": runOBJ["第二次選取"]
    });
}

// 恢復角色點擊事件
function restoreCharacterClickEvents() {
    console.log("恢復角色點擊事件");

    // 確保角色物件可以被點擊
    mapObjects.forEach(obj => {
        if (obj.type === 'player') {
            // 移除臨時禁用標記
            delete obj.tempDisabled;

            // 確保角色物件有正確的點擊屬性
            obj.clickable = true;

            // 如果角色還沒有完成行動，確保可以被選擇
            const player = gameplayers[obj.playerIndex];
            if (player && !player.AlreadyMove && player.CurHP > 0) {
                obj.selectable = true;
                console.log(`恢復 ${player.name} 的點擊事件`);
            } else if (player && player.AlreadyMove) {
                obj.selectable = false;
                console.log(`${player.name} 已完成行動，不可選擇`);
            }
        }
    });

    // 重新渲染以確保點擊事件生效
    render();

    console.log("角色點擊事件恢復完成");
}

function CreateMagicWin(player, leftmove, myMagic) {
    let MagicWin = `<dialog id="magicdialog">
                        <div id="outer-magic">
                            <div id="magiccontain">
                                <div id="magiccontent">
                                ${myMagic.map(element => {
        let classify = element.Classify || element.type || "法術";
        let needmove = element.NeedMove || 0;
        let needmp = element.NeedMP || 0;
        let textColor = (classify !== "武功" && leftmove !== player.Move) ||
            (leftmove < needmove) ||
            (player.CurMP < needmp) ? "color: gray;opacity: 0.2;" : "";
        return `<div class="usemagicitem" id='${element.name}'>
                                           <div class="usemagicitem_name" style="${textColor}">${element.name}</div>          
                                            <div class="usemagicitem_consume" style="${textColor}">
                                                <div class="consumeitem">🔮：${element.NeedMP}</div>
                                                <div class="consumeitem">👣：${element.NeedMove}</div>
                                            </div>
                                    </div>`
    }).join('')}
                                </div>
                            </div>
                            <div id="magicinfo">
                                 <div id="magicinfotext">
                            
                                </div>
                            </div>
                        </div>
                        <div id="magicclosebtn"> ↩ </div>
                    </dialog>
                   `;

    document.getElementById("InfoDialog").innerHTML = MagicWin;
    document.getElementById("magicdialog").showModal();

    document.getElementById('magicclosebtn').addEventListener('click', () => {
        // Clear playermagicrange highlights when closing the dialog
        document.querySelectorAll('.playermagicrange' + player.name).forEach(cell => {
            cell.classList.remove('playermagicrange' + player.name);
        });
        document.querySelectorAll('.playermagicrangework' + player.name).forEach(cell => {
            cell.classList.remove('playermagicrangework' + player.name);
        });

        // 清除Canvas高亮顯示
        clearAllHighlights();

        // 清除法術作用範圍高亮（cursor）
        clearAllMagicRangeHighlights();

        // 清除所有法術相關的事件監聽器
        clearAllMagicEventHandlers();

        document.getElementById("magicdialog").close();
        document.getElementById("InfoDialog").innerHTML = "";
    });

    document.querySelectorAll('.usemagicitem').forEach(magic => {
        magic.addEventListener('mouseover', debounce(() => {
            const magicName = magic.id;
            console.log(magicName);

            const magicInfo = myMagic.find(m => m.name === magicName) || window.MagicDB?.find(m => m.name === magicName);
            console.log(magicInfo?.describsion);

            let infoText = document.getElementById("magicinfotext");
            if (infoText) {
                infoText.innerText = magicInfo?.describsion || '';
            }
        }, 200));

        magic.addEventListener('click', () => {
            let magicname = magic.id;

            const magicData = myMagic.find(m => m.name === magicname) || window.MagicDB?.find(m => m.name === magicname);
            let needmove = magicData?.NeedMove || 0;
            let needmp = magicData?.NeedMP || 0;
            let classify = magicData?.Classify || magicData?.type || "法術";

            if (leftmove < needmove || player.CurMP < needmp) return;

            if (classify !== "武功" && leftmove !== player.Move) return;

            // 清除操作選單（Canvas系統兼容）
            const playerCell = document.getElementById(`${player.Position}`);
            if (playerCell) {
                playerCell.innerHTML = "";
            }

            // 清除操作選單（如果存在）
            const existingOptions = document.getElementById('options');
            if (existingOptions) {
                existingOptions.remove();
            }

            document.getElementById("magicdialog").close();
            document.getElementById("InfoDialog").innerHTML = "";

            const magicRangeData = myMagic.find(m => m.name === magicname) || window.MagicDB?.find(m => m.name === magicname);
            let distance = magicRangeData?.distance || 0;
            let rmdistance = magicRangeData?.Rmdistance || 0;
            let range = magicRangeData?.Range || 0;

            DrawPlayerWorkRange(player, magicname, distance, rmdistance, range);
        });
    });
}

function DrawPlayerWorkRange(player, magicname, distance, rmdistance, range) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    // 獲取法術類型
    const magicType = MagicDB.find(m => m.name === magicname)?.type || "";

    // 如果是法術類型，使用新的Canvas系統
    if (magicType === "法術") {
        drawMagicRangeCanvas(player, magicname, distance, rmdistance, range);
        return;
    }

    // 舊的DOM系統（保留給非法術類型）
    // Clear previous playermagicrange highlights
    document.querySelectorAll('.playermagicrange' + player.name).forEach(cell => {
        cell.classList.remove('playermagicrange' + player.name);
    });

    // Calculate player's coordinates using current level
    const currentMapSize = controlLayer[currentLevel].size;
    const playerX = player.Position % currentMapSize.cols;
    const playerY = Math.floor(player.Position / currentMapSize.cols);

    // Highlight player's own position if within rmdistance and distance
    if (rmdistance <= 0 && 0 <= distance) {
        const playerCell = document.getElementById(player.Position);
        if (playerCell) {
            playerCell.classList.add("playermagicrange" + player.name);
            playerCell.onmouseover = function () {
                // Clear previous magic work range highlights
                document.querySelectorAll('.playermagicrangework' + player.name).forEach(cell => {
                    cell.classList.remove('playermagicrangework' + player.name);
                });
                DrawMagicRange(player.Position, player.Position, range, rmdistance, player, magicname);
            };
        }
    }

    const queue = [{ position: player.Position, distance: 0 }];
    const visited = new Set();
    visited.add(player.Position);

    while (queue.length > 0) {
        const { position, distance: currentDistance } = queue.shift();

        if (currentDistance < distance) {
            directions.forEach(direction => {
                const newX = (position % currentMapSize.cols) + direction.x;
                const newY = Math.floor(position / currentMapSize.cols) + direction.y;
                const newPosition = newY * currentMapSize.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < currentMapSize.cols && newY >= 0 && newY < currentMapSize.rows) {

                    if (!visited.has(newPosition)) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: currentDistance + 1 });

                        // Calculate Manhattan distance to new position
                        const manhattanDistance = Math.abs(playerX - newX) + Math.abs(playerY - newY);

                        // Highlight only if within rmdistance and distance
                        if (manhattanDistance >= rmdistance && manhattanDistance <= distance) {
                            const newPositionCell = document.getElementById(newPosition);
                            if (newPositionCell) {
                                newPositionCell.classList.add("playermagicrange" + player.name);

                                // Show magic work range on mouseover
                                newPositionCell.onmouseover = function () {
                                    // Clear previous magic work range highlights
                                    document.querySelectorAll('.playermagicrangework' + player.name).forEach(cell => {
                                        cell.classList.remove('playermagicrangework' + player.name);
                                    });
                                    DrawMagicRange(newPosition, player.Position, range, rmdistance, player, magicname);
                                };
                            }
                        }
                    }

                }
            });
        }
    }

    // Add mouseout event to clear magic work range
    document.querySelectorAll('.playermagicrange' + player.name).forEach(cell => {
        cell.onmouseout = function () {
            // Clear magic work range highlights
            document.querySelectorAll('.playermagicrangework' + player.name).forEach(rangeCell => {
                rangeCell.classList.remove('playermagicrangework' + player.name);
            });
        };
    });
}


function DrawMagicRange(position, playerIndex, range, rmdistance, player, magicname) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    // 使用當前關卡的地圖尺寸
    const currentMapSize = controlLayer[currentLevel].size;

    // Add starting position to magic work range
    const positionCell = document.getElementById(position);
    if (positionCell) {
        positionCell.classList.add("playermagicrangework" + player.name);
    }

    // 統一所有 playermagicrangework 點擊事件（不論 range 為 0 或 >0）
    setTimeout(() => {
        document.querySelectorAll('.playermagicrangework' + player.name).forEach(cell => {
            cell.onclick = async function () {
                // 取得魔法類型
                const classify = MagicDB.find(m => m.name === magicname)?.Classify || 0;
                const magictype = MagicDB.find(m => m.name === magicname)?.type || 0;
                let targets = [];
                if (classify === "治癒") {
                    // 只抓玩家
                    targets = gameplayers.filter(p => {
                        const playerCell = document.getElementById(p.Position);
                        return playerCell && playerCell.classList.contains('playermagicrangework' + player.name) && p.CurHP > 0;
                    });
                } else {
                    // 只抓敵人
                    targets = gameenemys.filter(e => {
                        const enemyCell = document.getElementById(e.Position);
                        return enemyCell && enemyCell.classList.contains('playermagicrangework' + player.name) && e.CurHP > 0;
                    });
                }
                if (targets.length > 0) {
                    console.log("魔法點擊到目標：", targets);


                    // 清除所有魔法範圍高亮
                    document.querySelectorAll('.playermagicrangework' + player.name).forEach(cell => {
                        cell.classList.remove('playermagicrangework' + player.name);
                    });
                    document.querySelectorAll('.playermagicrange' + player.name).forEach(cell => {
                        cell.classList.remove('playermagicrange' + player.name);
                    });

                    // 清除所有格子的事件處理器
                    document.querySelectorAll('[id]').forEach(cell => {
                        cell.onmouseover = null;
                        cell.onmouseout = null;
                        cell.onclick = null;
                    });

                    // 這裡可以加動畫、訊息、回合結束等

                    // 判斷是否為法術類型
                    if (magictype === "法術") {
                        // 法術類型直接處理
                        let clickpos = this.id; // 取得玩家點擊的格子 id
                        await Magicanifunc(player, magicname, targets, clickpos)



                    } else {
                        // 非法術類型，創建新的場景
                        // 這裡呼叫你要的場景切換函數
                        // 例如：CreateNewScene(targets, magicname, player);
                    }


                }
            };
        });
    }, 0);

    const queue = [{ position: position, distance: 0 }];
    const visited = new Set();
    visited.add(position);

    while (queue.length > 0) {
        const { position: currentPosition, distance: currentDistance } = queue.shift();

        if (currentDistance < range) {
            directions.forEach(direction => {
                const newX = (currentPosition % currentMapSize.cols) + direction.x;
                const newY = Math.floor(currentPosition / currentMapSize.cols) + direction.y;
                const newPosition = newY * currentMapSize.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < currentMapSize.cols && newY >= 0 && newY < currentMapSize.rows) {
                    if (!visited.has(newPosition)) {
                        visited.add(newPosition);
                        queue.push({ position: newPosition, distance: currentDistance + 1 });

                        // Highlight cells within range, but only as work range if beyond rmdistance
                        const playerX = playerIndex % currentMapSize.cols;
                        const playerY = Math.floor(playerIndex / currentMapSize.cols);
                        const targetX = newX;
                        const targetY = newY;
                        const manhattanDistance = Math.abs(playerX - targetX) + Math.abs(playerY - targetY);

                        if (manhattanDistance >= rmdistance) {
                            const newPositionCell = document.getElementById(newPosition);
                            if (newPositionCell) {
                                newPositionCell.classList.add("playermagicrangework" + player.name);
                            }
                        }
                    }
                }
            });
        }
    }
}


async function Magicanifunc(magicuser, magicname, targets, clickpos) {
    let userPos = magicuser.Position

    let magicSoundsrc = window.MagicDB?.find(e => e.name === magicname)?.Sounds || 0;

    // 在Canvas系統中，我們不直接操作DOM背景圖片
    // 而是通過角色動畫系統來處理施法動畫
    console.log(`玩家 ${magicuser.name} 在位置 ${userPos} 施放法術 ${magicname}`);

    // 安全地設置背景效果（如果DOM元素存在）
    const userCell = document.getElementById(`${userPos}`);
    if (userCell && magicuser.usemagic) {
        userCell.style.backgroundImage = `url(${magicuser.usemagic})`;
        userCell.style.backgroundSize = "100% 165%";
        userCell.style.right = "10%";
    }

    // 設置遊戲板背景效果
    if (Dom && Dom.GameBoard) {
        Dom.GameBoard.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
        Dom.GameBoard.style.backgroundBlendMode = "multiply";
    }

    // 播放法術音效
    if (magicSoundsrc && magicSoundsrc !== 0) {
        const magicSound = operates.playSound(magicSoundsrc);
        if (!magicSound) {
            console.warn("播放法術音效失敗:", magicSoundsrc);
        }
    }

    // 呼叫施法特效動畫
    playUseMagicEffect(userPos);


    let magicAni = MagicDB.find(e => e.name === magicname)?.Animaties || 0;

    console.log(magicAni, clickpos);


    // 使用Canvas系統繪製法術動畫
    if (!magicAni || !Array.isArray(magicAni) || magicAni.length === 0) {
        console.log("沒有法術動畫或動畫陣列為空");
        return;
    }

    // 將clickpos轉換為格子座標
    let clickPosition = parseInt(clickpos);
    let gridX = clickPosition % controlLayer[currentLevel].size.cols;
    let gridY = Math.floor(clickPosition / controlLayer[currentLevel].size.cols);

    console.log(`在位置 (${gridX}, ${gridY}) 播放法術動畫`);

    // 創建法術效果物件並添加到mapObjects
    const magicEffectObj = {
        type: 'magicEffect',
        gridX: gridX,
        gridY: gridY,
        animationImages: magicAni,
        currentFrame: 0,
        frameInterval: 100, // 每100ms切換一幀
        lastFrameTime: performance.now(),
        zIndex: 9998, // 高層級，在角色上方但在恢復數值下方
        width: cellWidth * 1.5,
        height: cellHeight * 1.5,
        isMagicAnimation: true,
        // 濾鏡效果
        useFilter: true,
        brightness: 1.5,
        contrast: 1.2,
        saturate: 1.3
    };

    // 載入第一幀圖片
    if (magicAni[0]) {
        preloadImage(magicAni[0]).then(img => {
            magicEffectObj.img = img;
            mapObjects.push(magicEffectObj);

            // 啟動動畫循環
            if (typeof startAnimationLoop === 'function') {
                startAnimationLoop();
            }
            render();

            // 播放法術動畫
            playMagicAnimation(magicEffectObj);
        }).catch(error => {
            console.error("載入法術動畫圖片失敗:", error);
        });
    }

}

// 播放法術動畫的函數（完整的法術施放流程）
async function playMagicAnimation(magicuser, magicname, targets, clickPosition) {
    console.log(`開始播放法術動畫: ${magicname}, 施法者: ${magicuser.name}, 目標數量: ${targets.length}`);

    // 獲取法術信息
    const magicInfo = MagicDB.find(m => m.name === magicname);
    if (!magicInfo) {
        console.error(`找不到法術信息: ${magicname}`);
        return;
    }

    // 第一階段：施法者動畫和特效
    const casterObj = await playPhase1_CasterAnimation(magicuser, magicInfo);

    // 第二階段：法術動畫（施法者動畫持續播放）
    await playPhase2_SpellAnimation(magicInfo, clickPosition);

    // 第三階段：目標效果動畫（施法者動畫持續播放）
    await playPhase3_TargetEffects(magicInfo, targets);

    // 第四階段：顯示恢復數字，並記錄實際受影響的目標
    const affectedTargets = await playPhase4_RecoveryNumbers(magicInfo, targets, magicuser);

    // 第五階段：顯示經驗值獲得訊息（只針對實際受影響的目標）
    await playPhase5_ExperienceGain(magicInfo, affectedTargets || targets, magicuser);

    // 最後：恢復施法者動畫
    restoreCasterAnimation(casterObj, magicuser);

    // 檢查是否有非AI玩家因法術死亡
    await checkMagicDeathAndGameOver(targets);

    console.log(`法術 ${magicname} 動畫播放完成`);
}

// 第一階段：施法者動畫和特效
async function playPhase1_CasterAnimation(magicuser, magicInfo) {
    console.log(`第一階段：施法者 ${magicuser.name} 動畫和特效`);

    const userPosition = magicuser.Position;
    const userGridX = userPosition % controlLayer[currentLevel].size.cols;
    const userGridY = Math.floor(userPosition / controlLayer[currentLevel].size.cols);

    // 1. 播放法術音效
    if (magicInfo.Sounds && magicInfo.Sounds !== 0) {
        const magicSound = operates.playSound(magicInfo.Sounds);
        if (!magicSound) {
            console.warn("播放法術音效失敗:", magicInfo.Sounds);
        }
    }

    // 2. 改變施法者動畫為usemagic動畫（循環播放）
    const casterObj = mapObjects.find(obj =>
        obj.type === 'player' &&
        obj.gridX === userGridX &&
        obj.gridY === userGridY
    );

    if (casterObj && magicuser.usemagic) {
        // 停止當前的站立動畫
        if (casterObj.isStandAnimating) {
            casterObj.isStandAnimating = false;
            if (casterObj.animationId) {
                clearTimeout(casterObj.animationId);
                casterObj.animationId = null;
            }
        }

        // 保存原始動畫數據（參考Init.js的結構）
        casterObj.originalStandImages = casterObj.standImages;
        casterObj.originalFrameInterval = casterObj.frameInterval;

        // 設置施法動畫（參考Init.js的結構）
        casterObj.standImages = magicuser.usemagic;
        casterObj.frameInterval = 250; // 施法動畫速度（更慢，從150改為250）
        casterObj.currentFrameIndex = 0;
        casterObj.lastFrameTime = performance.now();
        casterObj.isCasting = true;

        // 啟動施法動畫（參考Init.js的startStandAnimation）
        startCastingAnimation(casterObj);

        console.log(`施法者開始播放usemagic動畫，共 ${magicuser.usemagic.length} 幀`);
    }

    // 3. 在施法者上方添加./Public/usemagiceff特效（播放一次）
    const useMagicEffectObj = await createUseMagicEffect(userGridX, userGridY);

    // 等待usemagiceff動畫播放完成
    if (useMagicEffectObj) {
        showmagicname(magicInfo.name);
        await waitForUseMagicEffectComplete(useMagicEffectObj);
    }

    console.log("第一階段完成");

    // 返回施法者物件，供後續恢復動畫使用
    return casterObj;
}

// 啟動施法動畫（參考Init.js的startStandAnimation）
function startCastingAnimation(characterObj) {
    if (!characterObj || !characterObj.standImages || characterObj.standImages.length <= 1) {
        return;
    }

    characterObj.isStandAnimating = true;

    function animateFrame() {
        if (!characterObj.isStandAnimating) {
            return; // 動畫已停止
        }

        const currentTime = performance.now();

        // 檢查是否該切換到下一幀
        if (currentTime - characterObj.lastFrameTime >= characterObj.frameInterval) {
            characterObj.currentFrameIndex = (characterObj.currentFrameIndex + 1) % characterObj.standImages.length;
            characterObj.lastFrameTime = currentTime;

            // 載入新的圖片
            const newImgSrc = characterObj.standImages[characterObj.currentFrameIndex];
            preloadImage(newImgSrc).then(newImg => {
                // 再次檢查動畫是否仍在運行
                if (characterObj.isStandAnimating) {
                    characterObj.img = newImg;
                    render(); // 重新渲染以顯示新圖片
                }
            }).catch(error => {
                console.error('載入施法動畫圖片失敗:', error);
            });
        }

        // 設置下一次動畫檢查（第一階段使用較慢的檢查頻率）
        characterObj.animationId = setTimeout(animateFrame, 80); // 每80ms檢查一次（更慢）
    }

    // 開始動畫
    animateFrame();


}

// 第二階段：法術動畫
async function playPhase2_SpellAnimation(magicInfo, clickPosition) {
    console.log("第二階段：法術動畫");
    console.log("magicInfo:", magicInfo);
    console.log("magicInfo.animates:", magicInfo.animates);

    const clickGridX = clickPosition % controlLayer[currentLevel].size.cols;
    const clickGridY = Math.floor(clickPosition / controlLayer[currentLevel].size.cols);

    // 播放法術動畫（位置在點擊位置，大小為800*600）
    // 檢查多種可能的屬性名稱
    const magicAnimates = magicInfo.animates || magicInfo.Animaties || magicInfo.Animates || [];
    operates.MoveComera(clickPosition);
    if (magicAnimates && magicAnimates.length > 0) {
        console.log(`找到法術動畫，共 ${magicAnimates.length} 幀`);
        const spellEffectObj = {
            type: 'spellEffect',
            gridX: clickGridX,
            gridY: clickGridY + 3,
            animationImages: magicAnimates,
            currentFrame: 0,
            frameInterval: 100,
            lastFrameTime: performance.now(),
            zIndex: 9998,
            width: 800,  // 固定大小800*600
            height: 600,
            isSpellAnimation: true,
            // 濾鏡效果
            useFilter: true,

        };

        // 載入第一幀圖片
        if (magicAnimates[0]) {
            try {
                const img = await preloadImage(magicAnimates[0]);
                spellEffectObj.img = img;
                mapObjects.push(spellEffectObj);

                // 啟動動畫循環
                if (typeof startAnimationLoop === 'function') {
                    startAnimationLoop();
                }
                render();

                // 播放法術動畫並等待完成
                await playSpellEffectAnimation(spellEffectObj);

            } catch (error) {
                console.error("載入法術動畫圖片失敗:", error);
            }
        }
    } else {
        console.log("沒有找到法術動畫");
        console.log("magicInfo.animates 值:", magicInfo.animates);
        console.log("magicInfo.Animaties 值:", magicInfo.Animaties);
        console.log("magicInfo.Animates 值:", magicInfo.Animates);
        console.log("使用的 magicAnimates 值:", magicAnimates);

        // 即使沒有法術動畫，也要等待一段時間來模擬第二階段
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log("第二階段完成");
}

// 第三階段：目標效果動畫
async function playPhase3_TargetEffects(magicInfo, targets) {
    console.log(`第三階段：目標效果動畫，影響 ${targets.length} 個目標`);

    // 在有作用到的角色中添加EffAnimates陣列圖片動畫（參考bagfun.js）
    if (magicInfo.EffAnimates && magicInfo.EffAnimates.length > 0 && targets.length > 0) {
        const effectPromises = [];

        for (const target of targets) {
            const targetGridX = target.Position % controlLayer[currentLevel].size.cols;
            const targetGridY = Math.floor(target.Position / controlLayer[currentLevel].size.cols);

            // 創建目標效果動畫物件（參考BagFun.js的方式）
            const targetEffectObj = {
                type: 'targetEffect',
                gridX: targetGridX,
                gridY: targetGridY,
                animationImages: magicInfo.EffAnimates,
                currentFrame: 0,
                frameInterval: 100,
                lastFrameTime: performance.now(),
                zIndex: 9998,
                width: cellWidth + 50,
                height: cellHeight + 90,
                isTargetEffectAnimation: true,
                // 濾鏡效果
                useFilter: true,
                brightness: 1.5,
                contrast: 1.2,
                saturate: 1.3
            };

            // 載入第一幀圖片並播放動畫
            if (magicInfo.EffAnimates && magicInfo.EffAnimates.length > 0) {
                try {
                    // 檢查並修正圖片路徑
                    const firstImagePath = magicInfo.EffAnimates[0];
                    let correctedPath = firstImagePath;

                    // 檢查路徑是否有有效的副檔名
                    const hasValidExtension = /\.(png|jpg|jpeg|gif|webp)$/i.test(firstImagePath);
                    if (!hasValidExtension) {
                        // 如果路徑以.結尾，先移除它
                        if (firstImagePath.endsWith('.')) {
                            correctedPath = firstImagePath.slice(0, -1) + '.png';
                        } else {
                            correctedPath = firstImagePath + '.png';
                        }
                        console.log(`修正圖片路徑: ${firstImagePath} -> ${correctedPath}`);
                    }

                    // 修正整個動畫陣列的路徑
                    const correctedAnimates = magicInfo.EffAnimates.map(path => {
                        const hasValidExt = /\.(png|jpg|jpeg|gif|webp)$/i.test(path);
                        if (!hasValidExt) {
                            if (path.endsWith('.')) {
                                return path.slice(0, -1) + '.png';
                            } else {
                                return path + '.png';
                            }
                        }
                        return path;
                    });
                    targetEffectObj.animationImages = correctedAnimates;

                    const img = await preloadImage(correctedPath);
                    targetEffectObj.img = img;
                    mapObjects.push(targetEffectObj);

                    // 播放目標效果動畫
                    const effectPromise = playTargetEffectAnimation(targetEffectObj);
                    effectPromises.push(effectPromise);

                } catch (error) {
                    console.error(`載入目標效果動畫圖片失敗:`, error);
                    console.error(`問題路徑: ${magicInfo.EffAnimates[0]}`);
                }
            }
        }

        // 啟動動畫循環
        if (typeof startAnimationLoop === 'function') {
            startAnimationLoop();
        }
        render();

        // 等待所有目標效果動畫完成
        await Promise.all(effectPromises);
    }

    console.log("第三階段完成");
}

// 第四階段：顯示恢復數字
async function playPhase4_RecoveryNumbers(magicInfo, targets, caster) {
    console.log(`第四階段：顯示恢復數字，影響 ${targets.length} 個目標`);

    // 記錄實際受影響的目標（用於經驗值計算）
    const affectedTargets = [];

    // 檢查法術類型
    const classify = magicInfo.Classify || "";

    if (classify === "治癒" && targets.length > 0) {
        // 治癒法術：顯示恢復數字
        console.log("處理治癒法術效果");
        console.log("magicInfo.effect:", magicInfo.effect);

        for (const target of targets) {
            // 從 MagicDB.js 的 effect 函數中獲取恢復量
            let recoveryAmount = 0;
            let recoveryType = "";

            if (magicInfo.effect && typeof magicInfo.effect === 'function') {
                // effect 是一個函數，需要調用它來獲取實際效果
                // 對於治癒法術，通常傳入施法者的MP值
                const casterMP = getPlayerMP(caster) || getPlayerMaxMP(caster) || 100;
                const effectResult = magicInfo.effect(casterMP);

                console.log("effect函數返回結果:", effectResult);

                if (effectResult && effectResult.type === "heal" && effectResult.value > 0) {
                    recoveryAmount = effectResult.value;
                    recoveryType = "HP"; // 治癒法術通常恢復HP

                    // 實際恢復HP
                    const oldHP = target.CurHP;
                    target.CurHP = Math.min(target.CurHP + recoveryAmount, target.HP);
                    console.log(`${target.name} 恢復 ${recoveryAmount} HP：${oldHP} → ${target.CurHP}/${target.HP}`);
                }
            } else if (magicInfo.effect && typeof magicInfo.effect === 'object') {
                // 如果effect是物件，直接讀取數值
                if (magicInfo.effect.curHP && magicInfo.effect.curHP > 0) {
                    recoveryAmount = magicInfo.effect.curHP;
                    recoveryType = "HP";

                    // 實際恢復HP
                    const oldHP = target.CurHP;
                    target.CurHP = Math.min(target.CurHP + recoveryAmount, target.HP);
                    console.log(`${target.name} 恢復 ${recoveryAmount} HP：${oldHP} → ${target.CurHP}/${target.HP}`);

                } else if (magicInfo.effect.curMP && magicInfo.effect.curMP > 0) {
                    recoveryAmount = magicInfo.effect.curMP;
                    recoveryType = "MP";

                    // 實際恢復MP
                    const oldMP = getPlayerMP(target);
                    const maxMP = getPlayerMaxMP(target);
                    const newMP = Math.min(oldMP + recoveryAmount, maxMP);
                    setPlayerMP(target, newMP);

                    console.log(`${target.name} 恢復 ${recoveryAmount} MP：${oldMP} → ${newMP}/${maxMP}`);
                }
            } else {
                console.log("沒有找到有效的 magicInfo.effect");
            }

            // 顯示恢復數字（參考BagFun.js的showRecoveryNumbers）
            if (recoveryAmount > 0) {
                console.log(`準備顯示恢復數字：${target.name}, ${recoveryAmount}, ${recoveryType}`);
                showMagicRecoveryNumbers(target, recoveryAmount, recoveryType);
                // 記錄實際受到治癒的目標
                affectedTargets.push(target);
            } else {
                console.log(`沒有恢復效果：${target.name}`);
            }
        }

        // 等待恢復數字動畫播放一段時間
        await new Promise(resolve => setTimeout(resolve, 1500));

    } else if ((classify === "火系" || classify === "雷系" || classify === "冰系" || classify === "幻系" || classify === "華系" || classify === "冥系") && targets.length > 0) {
        // 攻擊法術：顯示傷害數字
        console.log(`處理${classify}攻擊法術效果`);
        console.log("magicInfo.effect:", magicInfo.effect);

        for (const target of targets) {
            // 計算命中率：施法者HitRate vs 目標AvoidRate
            const casterHitRate = caster.HitRate || 100;
            const targetAvoidRate = target.AvoidRate || 0;
            const hitChance = casterHitRate - targetAvoidRate + Math.floor(Math.random() * 100);

            console.log(`法術命中計算: 施法者命中率${casterHitRate} vs 目標閃避率${targetAvoidRate}, 命中判定${hitChance}`);

            if (hitChance < 100) {
                // 法術被閃避
                console.log(`${target.name} 閃避了 ${caster.name} 的法術攻擊`);
                showMagicDamageNumbers(target, "MISS");
                continue;
            }

            // 從 MagicDB.js 的 effect 函數中獲取基礎傷害量
            let baseDamage = 0;

            if (magicInfo.effect && typeof magicInfo.effect === 'function') {
                // effect 是一個函數，需要調用它來獲取實際效果
                // 對於攻擊法術，通常傳入施法者的MP值（火系法術基於MP計算）
                const casterMP = caster.CurMP || caster.MP || 100;
                const effectResult = magicInfo.effect(casterMP);

                console.log("effect函數返回結果:", effectResult);

                if (effectResult && (effectResult.type === "damage" || effectResult.type === "fire" || effectResult.type === "thunder" || effectResult.type === "ice" || effectResult.type === "illusion" || effectResult.type === "light" || effectResult.type === "dark") && effectResult.value > 0) {
                    baseDamage = effectResult.value;
                }
            }

            if (baseDamage > 0) {
                // 根據法術系統計算對應的法術抗性減免
                let magicResistance = 0;
                let resistanceType = "";

                switch (classify) {
                    case "火系":
                        magicResistance = target.法抗?.火 || 0;
                        resistanceType = "火";
                        break;
                    case "雷系":
                        magicResistance = target.法抗?.雷 || 0;
                        resistanceType = "雷";
                        break;
                    case "冰系":
                        magicResistance = target.法抗?.冰 || 0;
                        resistanceType = "冰";
                        break;
                    case "幻系":
                        magicResistance = target.法抗?.幻 || 0;
                        resistanceType = "幻";
                        break;
                    case "華系":
                        magicResistance = target.法抗?.華 || 0;
                        resistanceType = "華";
                        break;
                    case "冥系":
                        magicResistance = target.法抗?.冥 || 0;
                        resistanceType = "冥";
                        break;
                    default:
                        magicResistance = 0;
                        resistanceType = "無";
                        break;
                }

                // 計算法術抗性減免：傷害 * (100 - 目標法抗) / 100
                const resistanceMultiplier = (100 - magicResistance) / 100;
                const finalDamage = Math.floor(baseDamage * resistanceMultiplier);

                console.log(`${classify}法術傷害計算: 基礎傷害${baseDamage} * (100-${magicResistance})/100 = ${finalDamage} (${resistanceType}抗性${magicResistance})`);

                if (finalDamage > 0) {
                    // 實際造成傷害
                    const oldHP = target.CurHP;
                    target.CurHP = Math.max(target.CurHP - finalDamage, 0);
                    console.log(`${target.name} 受到 ${finalDamage} ${classify}傷害：${oldHP} → ${target.CurHP}/${target.HP}`);

                    // 如果是敵人對玩家的法術攻擊，增加成就計數器
                    if (caster["是否電腦操作"] === true && target["是否電腦操作"] === false) {
                        if (!window.achievementTracker) {
                            window.achievementTracker = {
                                magicAttackCount: 0,
                                itemsCollected: new Set()
                            };
                        }
                        window.achievementTracker.magicAttackCount++;
                        console.log(`玩家 ${target.name} 受到敵人法術攻擊，計數器: ${window.achievementTracker.magicAttackCount}`);

                        // 更新成就進度條（如果設定視窗開啟）
                        if (typeof Game !== 'undefined' && Game.updateAchievementProgress) {
                            Game.updateAchievementProgress();
                        }
                    }

                    // 顯示傷害數字（參考BattleStyle.js的setplayerdamagetext）
                    console.log(`準備顯示傷害數字：${target.name}, ${finalDamage}`);
                    showMagicDamageNumbers(target, finalDamage);

                    // 檢查目標是否死亡
                    if (target.CurHP <= 0) {
                        if (target["是否電腦操作"] === false) {
                            // 非AI玩家死亡，觸發遊戲結束
                            console.log(`非AI玩家 ${target.name} 因法術傷害死亡，觸發遊戲結束`);
                            target.isDiedByMagic = true;
                        } else {
                            // 敵人死亡，標記需要從列表中移除
                            console.log(`敵人 ${target.name} 因法術傷害死亡，標記移除`);
                            target.isDiedByMagic = true;
                            target.isEnemyKilledByMagic = true;
                        }
                    }

                    // 記錄實際受到傷害的目標（用於經驗值計算）
                    affectedTargets.push(target);
                } else {
                    // 傷害被完全抵抗
                    console.log(`${target.name} 完全抵抗了${classify}法術傷害`);
                    showMagicDamageNumbers(target, 0);

                    // 即使傷害為0，法術仍然命中了，也應該獲得經驗值
                    affectedTargets.push(target);
                }
            } else {
                console.log(`沒有找到有效的基礎傷害數據：${target.name}`);
            }
        }

        // 等待傷害數字動畫播放一段時間
        await new Promise(resolve => setTimeout(resolve, 1500));
    }

    console.log("第四階段完成");
    console.log(`實際受影響的目標數量: ${affectedTargets.length}`);

    // 返回實際受影響的目標列表（用於經驗值計算）
    return affectedTargets;
}

// 檢查法術死亡並處理遊戲結束
async function checkMagicDeathAndGameOver(targets) {
    console.log("檢查法術造成的死亡情況");

    // 記錄需要移除的敵人
    const enemiesToRemove = [];

    // 檢查所有目標中是否有死亡的角色
    for (const target of targets) {
        if (target.isDiedByMagic && target.CurHP <= 0) {
            if (target["是否電腦操作"] === false) {
                // 非AI玩家死亡，觸發遊戲結束
                console.log(`非AI玩家 ${target.name} 因法術死亡，觸發遊戲結束`);

                // 清除死亡標記
                delete target.isDiedByMagic;
                delete target.isEnemyKilledByMagic;

                // 創建一個臨時的playerCanvas用於gameover函數
                const virtualCanvas = {
                    style: { display: 'none' },
                    remove: () => {} // 空函數，避免錯誤
                };

                try {
                    // 調用Players.js中的gameover函數
                    await gameover(target, virtualCanvas);
                } catch (error) {
                    console.error("調用gameover函數時發生錯誤:", error);
                }

                return; // 只處理第一個死亡的玩家
            } else if (target.isEnemyKilledByMagic) {
                // 敵人死亡，記錄需要移除
                console.log(`敵人 ${target.name} 因法術死亡，準備從列表中移除`);
                enemiesToRemove.push(target);
            }
        }
    }

    // 移除死亡的敵人
    for (const deadEnemy of enemiesToRemove) {
        const enemyIndex = gameenemys.indexOf(deadEnemy);
        if (enemyIndex !== -1) {
            console.log(`從gameenemys中移除敵人: ${deadEnemy.name} (索引: ${enemyIndex})`);
            gameenemys.splice(enemyIndex, 1);

            // 從mapObjects中移除敵人物件
            const enemyObjIndex = mapObjects.findIndex(obj =>
                obj.type === 'enemy' && obj.enemyIndex === enemyIndex
            );
            if (enemyObjIndex !== -1) {
                console.log(`從mapObjects中移除敵人物件: ${deadEnemy.name}`);
                mapObjects.splice(enemyObjIndex, 1);

                // 更新其他敵人物件的索引
                mapObjects.forEach(obj => {
                    if (obj.type === 'enemy' && obj.enemyIndex > enemyIndex) {
                        obj.enemyIndex--;
                    }
                });
            }

            // 重新渲染地圖
            render();
        }
    }

    // 清除所有目標的死亡標記
    for (const target of targets) {
        if (target.isDiedByMagic) {
            delete target.isDiedByMagic;
        }
        if (target.isEnemyKilledByMagic) {
            delete target.isEnemyKilledByMagic;
        }
    }

    // 檢查是否所有敵人都已死亡（CurHP <= 0）
    if (enemiesToRemove.length > 0) {
        const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
        console.log(`法術攻擊後剩餘存活敵人數量: ${aliveEnemies.length}`);

        if (aliveEnemies.length === 0) {
            console.log("法術攻擊後所有敵人已死亡，關卡通關！準備前往營地場景");

            // 觸發關卡通關事件
            const levelCompleteEvent = new CustomEvent('levelComplete', {
                detail: {
                    levelIndex: currentLevel,
                    completionTime: Date.now(),
                    victoryMessage: controlLayer[currentLevel]["勝利訊息"],
                    achievementMessage: controlLayer[currentLevel]["成就訊息"]
                }
            });

            // 延遲觸發事件，讓玩家看到法術效果
            setTimeout(() => {
                document.dispatchEvent(levelCompleteEvent);
            }, 3000);
        }
    }
}

// 第五階段：顯示經驗值獲得訊息
async function playPhase5_ExperienceGain(magicInfo, targets, caster) {
    console.log(`第五階段：顯示經驗值獲得訊息，施法者: ${caster.name}`);

    // 檢查施法者是否為電腦操作
    if (caster["是否電腦操作"] === true) {
        console.log(`${caster.name} 是電腦操作，跳過經驗值獲得`);
        return;
    }

    // 檢查法術是否有經驗值計算函數
    if (!magicInfo.exp || typeof magicInfo.exp !== 'function') {
        console.log(`法術 ${magicInfo.name} 沒有經驗值計算函數`);
        return;
    }

    let totalExp = 0;

    // 根據法術類型計算經驗值
    const classify = magicInfo.Classify || "";

    if (classify === "治癒" && targets.length > 0) {
        // 治癒法術：根據治癒量和等級差計算經驗值
        console.log("計算治癒法術經驗值");

        for (const target of targets) {
            // 計算實際治癒量
            let healAmount = 0;
            if (magicInfo.effect && typeof magicInfo.effect === 'function') {
                const effectResult = magicInfo.effect(caster.curMp || caster.Mp || 100);
                if (effectResult && effectResult.type === "heal") {
                    healAmount = effectResult.value;
                }
            }

            if (healAmount > 0) {
                // 調用法術的經驗值計算函數
                const expGained = magicInfo.exp(healAmount, caster.level, target.level);
                totalExp += expGained;
                console.log(`治癒 ${target.name}: 治癒量=${healAmount}, 經驗值=${expGained}`);
            }
        }

    } else if ((classify === "火系" || classify === "雷系" || classify === "冰系" || classify === "幻系" || classify === "華系" || classify === "冥系") && targets.length > 0) {
        // 攻擊法術：根據目標剩餘HP計算經驗值（只針對實際命中的目標）
        console.log(`計算${classify}攻擊法術經驗值，實際命中目標: ${targets.length}`);

        for (const target of targets) {
            // 使用目標的剩餘HP和最大HP計算經驗值
            const expGained = magicInfo.exp(target.CurHP, target.HP);
            totalExp += expGained;
            console.log(`攻擊 ${target.name}: 剩餘HP=${target.CurHP}/${target.HP}, 經驗值=${expGained}`);
        }
    }

    // 如果獲得了經驗值，顯示系統訊息
    if (totalExp > 0) {
        console.log(`${caster.name} 總共獲得 ${totalExp} 經驗值`);

        // 添加經驗值到玩家
        caster.CurEXP += totalExp;

        // 調用Players.js的sysMsg函數顯示經驗值獲得訊息
        if (typeof sysMsg === 'function') {
            await sysMsg(caster, totalExp, null);
            console.log(`已顯示 ${caster.name} 的經驗值獲得訊息`);
        } else {
            console.error("sysMsg 函數不存在");
        }
    } else {
        console.log(`${caster.name} 沒有獲得經驗值`);
    }

    console.log("第五階段完成");
}

// 數字轉中文函數（參考BagFun.js）
function numberToChinese(num) {
    const chineseNumbers = ['〇', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    return num.toString().split('').map(n => chineseNumbers[parseInt(n)]).join('');
}

// 顯示法術恢復數字（參考BagFun.js的showRecoveryNumbers）
function showMagicRecoveryNumbers(target, amount, type) {
    const playerX = target.Position % controlLayer[currentLevel].size.cols;
    const playerY = Math.floor(target.Position / controlLayer[currentLevel].size.cols);

    // 確定顏色和文字
    let recoveryColor, recoveryText;
    if (type === "HP") {
        recoveryColor = '#87CEEB'; // 藍色
        recoveryText = `${numberToChinese(amount)}`;
    } else if (type === "MP") {
        recoveryColor = '#FFD700'; // 金色
        recoveryText = `${numberToChinese(amount)}`;
    }

    // 創建恢復數值物件
    const recoveryObj = {
        type: 'recoveryNumber',
        gridX: playerX,
        gridY: playerY,
        text: recoveryText,
        color: recoveryColor,
        fontFamily: '圓體', // 使用圓體字體
        fontSize: 28,
        zIndex: 9999,
        // 動畫相關屬性
        startTime: performance.now(),
        duration: 3000,
        // 字符彈跳動畫參數
        charAnimationDuration: 200,
        charDelay: 120,
        bounceHeight: 12,
        // 動畫階段參數
        fadeStartProgress: 0.7,
        isRecoveryAnimation: true
    };

    // 添加到mapObjects
    mapObjects.push(recoveryObj);

    // 3.5秒後移除恢復數值物件（動畫3秒 + 0.5秒緩衝）
    setTimeout(() => {
        const index = mapObjects.findIndex(obj => obj === recoveryObj);
        if (index !== -1) {
            mapObjects.splice(index, 1);
            render();
        }
    }, 3500);

    // 啟動動畫循環並立即渲染以顯示恢復數值（參考BagFun.js）
    if (typeof startAnimationLoop === 'function') {
        startAnimationLoop();
    }
    render();
    console.log(`顯示 ${target.name} 的恢復數字: ${recoveryText} (${type})`);
}

// 顯示法術傷害數字（參考BattleStyle.js的setplayerdamagetext）
function showMagicDamageNumbers(target, damage) {
    const playerX = target.Position % controlLayer[currentLevel].size.cols;
    const playerY = Math.floor(target.Position / controlLayer[currentLevel].size.cols);

    // 處理不同類型的顯示文字和顏色
    let displayText, displayColor;

    if (damage === "MISS") {
        displayText = "閃避";
        displayColor = '#FFF000'; // 黃色表示閃避
    } else if (damage === 0) {
        displayText = "免疫";
        displayColor = '#00FF00'; // 綠色表示完全抵抗
    } else {
        displayText = numberToChinese(damage);
        displayColor = '#FF0000'; // 紅色表示傷害
    }

    // 創建傷害數值物件
    const damageObj = {
        type: 'damageNumber',
        gridX: playerX,
        gridY: playerY,
        text: displayText,
        color: displayColor,
        fontFamily: '圓體', // 使用圓體字體
        fontSize: 32,
        zIndex: 9999,
        // 動畫相關屬性
        startTime: performance.now(),
        duration: 3000,
        // 字符彈跳動畫參數
        charAnimationDuration: 200,
        charDelay: 120,
        bounceHeight: 12,
        // 動畫階段參數
        fadeStartProgress: 0.7,
        isDamageAnimation: true
    };

    // 添加到mapObjects
    mapObjects.push(damageObj);

    // 3.5秒後移除傷害數值物件（動畫3秒 + 0.5秒緩衝）
    setTimeout(() => {
        const index = mapObjects.findIndex(obj => obj === damageObj);
        if (index !== -1) {
            mapObjects.splice(index, 1);
            render();
        }
    }, 3500);

    // 啟動動畫循環並立即渲染以顯示傷害數值（參考BagFun.js）
    if (typeof startAnimationLoop === 'function') {
        startAnimationLoop();
    }
    render();
    console.log(`顯示 ${target.name} 的傷害數字: ${numberToChinese(damage)}`);
}

// 恢復施法者動畫
function restoreCasterAnimation(casterObj, magicuser) {
    if (casterObj && casterObj.isCasting) {
        console.log(`開始恢復施法者 ${magicuser.name} 的原始動畫`);

        // 停止施法動畫
        casterObj.isStandAnimating = false;
        if (casterObj.animationId) {
            clearTimeout(casterObj.animationId);
            casterObj.animationId = null;
        }

        // 恢復原始動畫（參考Init.js的結構）
        if (casterObj.originalStandImages) {
            casterObj.standImages = casterObj.originalStandImages;
            casterObj.frameInterval = casterObj.originalFrameInterval || 150;
            casterObj.currentFrameIndex = 0;
            casterObj.lastFrameTime = performance.now();
            delete casterObj.originalStandImages;
            delete casterObj.originalFrameInterval;

            // 重新啟動站立動畫（參考Init.js的resumeStandAnimation）
            if (typeof resumeStandAnimation === 'function') {
                resumeStandAnimation(casterObj);
            } else {
                // 如果沒有resumeStandAnimation函數，手動啟動
                startCastingAnimation(casterObj);
            }
        }
        casterObj.isCasting = false;
        console.log(`恢復施法者 ${magicuser.name} 的原始動畫完成`);
    }
}

// 封裝施法特效動畫為函式 - 使用Canvas系統
function playUseMagicEffect(userPos) {
    console.log(`在位置 ${userPos} 播放施法特效`);

    // 將位置轉換為格子座標
    let gridX = userPos % controlLayer[currentLevel].size.cols;
    let gridY = Math.floor(userPos / controlLayer[currentLevel].size.cols);

    // 創建施法特效圖片陣列（只使用確實存在的圖片）
    let useMagicImages = [];

    // 檢查常見的圖片文件（0.png, 1.png, 2.png）
    const commonImages = ['0.png', '1.png', '2.png'];

    for (const imageName of commonImages) {
        const imagePath = `./Public/usemagiceff/${imageName}`;
        // 直接添加到陣列，錯誤處理在後面的載入過程中
        useMagicImages.push(imagePath);
    }

    console.log(`使用施法特效圖片:`, useMagicImages);

    // 創建施法特效物件
    const useMagicEffectObj = {
        type: 'useMagicEffect',
        gridX: gridX,
        gridY: gridY,
        animationImages: useMagicImages,
        currentFrame: 0,
        frameInterval: 120, // 每120ms切換一幀
        lastFrameTime: performance.now(),
        zIndex: 9999, // 最高層級
        width: cellWidth * 1.2,
        height: cellHeight * 1.2,
        isUseMagicAnimation: true,
        // 濾鏡效果
        useFilter: true,
        brightness: 2,
        contrast: 1.8,
        saturate: 1.5
    };

    // 載入第一幀圖片
    if (useMagicImages[0]) {
        preloadImage(useMagicImages[0]).then(img => {
            useMagicEffectObj.img = img;
            mapObjects.push(useMagicEffectObj);

            // 啟動動畫循環
            if (typeof startAnimationLoop === 'function') {
                startAnimationLoop();
            }
            render();

            // 播放施法特效動畫
            playUseMagicEffectAnimation(useMagicEffectObj);
        }).catch(error => {
            console.error("載入施法特效圖片失敗:", error);
        });
    }
}

// 播放施法特效動畫的函數
function playUseMagicEffectAnimation(useMagicEffectObj) {
    let currentFrame = 0;
    const totalFrames = useMagicEffectObj.animationImages.length;

    function playNextFrame() {
        // 檢查當前幀是否存在
        if (currentFrame < totalFrames && currentFrame < useMagicEffectObj.animationImages.length) {
            // 載入下一幀圖片
            preloadImage(useMagicEffectObj.animationImages[currentFrame]).then(img => {
                useMagicEffectObj.img = img;
                useMagicEffectObj.currentFrame = currentFrame;
                render(); // 重新渲染

                currentFrame++;
                setTimeout(playNextFrame, useMagicEffectObj.frameInterval);
            }).catch(error => {
                console.error(`載入施法特效第${currentFrame}幀失敗:`, error);
                console.error(`問題路徑: ${useMagicEffectObj.animationImages[currentFrame]}`);
                // 跳過這一幀，繼續下一幀
                currentFrame++;
                if (currentFrame < totalFrames) {
                    setTimeout(playNextFrame, useMagicEffectObj.frameInterval);
                } else {
                    // 如果沒有更多幀了，結束動畫
                    finishAnimation();
                }
            });
        } else {
            finishAnimation();
        }
    }

    function finishAnimation() {
        // 動畫播放完成，移除效果物件
        const effectIndex = mapObjects.findIndex(obj => obj === useMagicEffectObj);
        if (effectIndex !== -1) {
            mapObjects.splice(effectIndex, 1);
            render();
            console.log("施法特效動畫播放完成，已移除效果物件");
        }
    }

    playNextFrame();
}

// 創建施法特效物件
async function createUseMagicEffect(gridX, gridY) {
    console.log(`創建施法特效在位置 (${gridX}, ${gridY})`);

    // 創建施法特效圖片陣列（只使用確實存在的圖片）
    let useMagicImages = [];

    // 檢查常見的圖片文件（0.png, 1.png, 2.png）
    const commonImages = ['0.png', '1.png', '2.png', '3.png', '4.png', '5.png', '6.png'];

    for (const imageName of commonImages) {
        const imagePath = `./Public/usemagiceff/${imageName}`;
        try {
            // 嘗試載入圖片來檢查是否存在
            await preloadImage(imagePath);
            useMagicImages.push(imagePath);
            console.log(`找到施法特效圖片: ${imagePath}`);
        } catch (error) {
            console.log(`施法特效圖片不存在: ${imagePath}`);
        }
    }

    // 如果沒有找到任何圖片，創建一個空的特效物件
    if (useMagicImages.length === 0) {
        console.warn("沒有找到任何usemagiceff圖片，跳過施法特效");
        return null;
    }

    console.log(`usemagiceff圖片檢查完成，共找到 ${useMagicImages.length} 張圖片:`, useMagicImages);

    // 創建施法特效物件
    const useMagicEffectObj = {
        type: 'useMagicEffect',
        gridX: gridX,
        gridY: gridY + 1,
        animationImages: useMagicImages,
        currentFrame: 0,
        frameInterval: 180, // 施法特效速度（更慢，從120改為180）
        lastFrameTime: performance.now(),
        zIndex: 9999,
        width: cellWidth * 1.8,
        height: cellHeight * 2.5,
        isUseMagicAnimation: true,
        // 濾鏡效果
        useFilter: true,
        brightness: 2,
        contrast: 1.8,
        saturate: 1.5
    };

    // 載入第一幀圖片
    if (useMagicImages[0]) {
        try {
            const img = await preloadImage(useMagicImages[0]);
            useMagicEffectObj.img = img;
            mapObjects.push(useMagicEffectObj);

            // 啟動動畫循環
            if (typeof startAnimationLoop === 'function') {
                startAnimationLoop();
            }
            render();

            return useMagicEffectObj;
        } catch (error) {
            console.error("載入施法特效圖片失敗:", error);
            return null;
        }
    }

    return null;
}

// 等待施法特效動畫完成
function waitForUseMagicEffectComplete(useMagicEffectObj) {
    return new Promise((resolve) => {
        // 如果沒有特效物件，直接完成
        if (!useMagicEffectObj) {
            console.log("沒有施法特效物件，跳過等待");
            resolve();
            return;
        }

        const totalFrames = useMagicEffectObj.animationImages.length;
        const totalDuration = totalFrames * useMagicEffectObj.frameInterval;

        // 播放施法特效動畫
        playUseMagicEffectAnimation(useMagicEffectObj);

        // 等待動畫完成
        setTimeout(() => {
            resolve();
        }, totalDuration);
    });
}

// 播放法術效果動畫
function playSpellEffectAnimation(spellEffectObj) {
    return new Promise((resolve) => {
        let currentFrame = 0;
        const totalFrames = spellEffectObj.animationImages.length;

        function playNextFrame() {
            if (currentFrame < totalFrames) {
                // 載入下一幀圖片
                preloadImage(spellEffectObj.animationImages[currentFrame]).then(img => {
                    spellEffectObj.img = img;
                    spellEffectObj.currentFrame = currentFrame;
                    render();

                    currentFrame++;
                    setTimeout(playNextFrame, spellEffectObj.frameInterval);
                }).catch(error => {
                    console.error(`載入法術效果第${currentFrame}幀失敗:`, error);
                    currentFrame++;
                    setTimeout(playNextFrame, spellEffectObj.frameInterval);
                });
            } else {
                // 動畫播放完成，移除效果物件
                const effectIndex = mapObjects.findIndex(obj => obj === spellEffectObj);
                if (effectIndex !== -1) {
                    mapObjects.splice(effectIndex, 1);
                    render();
                    console.log("法術效果動畫播放完成");
                }
                resolve();
            }
        }

        playNextFrame();
    });
}

// 播放目標效果動畫
function playTargetEffectAnimation(targetEffectObj) {
    return new Promise((resolve) => {
        let currentFrame = 0;
        const totalFrames = targetEffectObj.animationImages.length;

        function playNextFrame() {
            if (currentFrame < totalFrames) {
                // 載入下一幀圖片
                let imagePath = targetEffectObj.animationImages[currentFrame];

                // 檢查並修正圖片路徑（使用更精確的檢查）
                const hasValidExtension = /\.(png|jpg|jpeg|gif|webp)$/i.test(imagePath);
                if (!hasValidExtension) {
                    if (imagePath.endsWith('.')) {
                        imagePath = imagePath.slice(0, -1) + '.png';
                    } else {
                        imagePath = imagePath + '.png';
                    }
                }

                preloadImage(imagePath).then(img => {
                    targetEffectObj.img = img;
                    targetEffectObj.currentFrame = currentFrame;
                    render();

                    currentFrame++;
                    setTimeout(playNextFrame, targetEffectObj.frameInterval);
                }).catch(error => {
                    console.error(`載入目標效果第${currentFrame}幀失敗:`, error);
                    console.error(`問題路徑: ${imagePath}`);
                    currentFrame++;
                    setTimeout(playNextFrame, targetEffectObj.frameInterval);
                });
            } else {
                // 動畫播放完成，移除效果物件
                const effectIndex = mapObjects.findIndex(obj => obj === targetEffectObj);
                if (effectIndex !== -1) {
                    mapObjects.splice(effectIndex, 1);
                    render();
                    console.log("目標效果動畫播放完成");
                }
                resolve();
            }
        }

        playNextFrame();
    });
}

async function showmagicname(magicname) {
    let magicnameText = `<dialog id="magicnamedialog">
                            <div id="magicname">${magicname}</div>
                        </dialog>`;

    document.getElementById("InfoDialog").innerHTML = magicnameText;
    document.getElementById("magicnamedialog").showModal();

    setTimeout(() => {
        document.getElementById("magicnamedialog").close();
        document.getElementById("InfoDialog").innerHTML = "";
    }, 1500);
}