function bfs(startPosition, moveRange, playerIndex) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];
    moveRange = Math.floor(moveRange)
    // 獲取所有敵人的位置
    const enemyPositions = gameenemys
        .filter(enemy => enemy.CurHP > 0)
        .map(enemy => enemy.Position);

    // 獲取所有敵人周圍的格子（上下左右）
    const enemyAdjacentCells = new Set();
    enemyPositions.forEach(enemyPos => {
        const enemyRow = Math.floor(enemyPos / controlLayer[currentLevel].size.cols);
        const enemyCol = enemyPos % controlLayer[currentLevel].size.cols;

        directions.forEach(dir => {
            const newRow = enemyRow + dir.y;
            const newCol = enemyCol + dir.x;
            if (newRow >= 0 && newRow < controlLayer[currentLevel].size.rows &&
                newCol >= 0 && newCol < controlLayer[currentLevel].size.cols) {
                const adjacentPos = newRow * controlLayer[currentLevel].size.cols + newCol;
                enemyAdjacentCells.add(adjacentPos);
            }
        });
    });

    const queue = [{ position: startPosition, distance: 0 }];
    const visited = new Set();
    visited.add(startPosition);

    // 用於存儲所有可到達的格子及其距離
    const reachableCells = new Map();
    reachableCells.set(startPosition, 0);

    // (移除局部宣告，改用全域 moveTargetCells / highlightCells)

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // Check boundaries
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols && newY >= 0 && newY < controlLayer[currentLevel].size.rows) {
                    // 檢查是否為隊友位置
                    const isTeammatePosition = gameplayers.some((player, index) =>
                        index !== playerIndex && player.Position === newPosition
                    );

                    if (
                        !visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameenemys.some(enemy => enemy.Position === newPosition && enemy.CurHP > 0) &&
                        (!enemyAdjacentCells.has(newPosition) || isTeammatePosition)  // 如果是隊友位置，允許穿過敵人周圍格子
                    ) {
                        visited.add(newPosition);
                        const newDistance = distance + 1;
                        queue.push({ position: newPosition, distance: newDistance });
                        reachableCells.set(newPosition, newDistance);

                        // ===== Canvas highlight integration =====
                        moveTargetCells.add(newPosition);
                        highlightCells.push({
                            x: newX,
                            y: newY,
                            color: 'rgba(245, 0, 216, 0.78)'
                        });
                    }
                }
            });
        }
    }

    // 檢查敵人周圍的格子是否可以到達
    enemyAdjacentCells.forEach(cell => {
        const cellRow = Math.floor(cell / controlLayer[currentLevel].size.cols);
        const cellCol = cell % controlLayer[currentLevel].size.cols;

        // 檢查這個格子是否合法（不是障礙物、沒有敵人）
        if (!gameobstacles.includes(cell) &&
            !gameenemys.some(enemy => enemy.Position === cell && enemy.CurHP > 0)) {

            // 檢查是否有相鄰的可到達格子，並找出最短距離
            let minDistance = Infinity;
            directions.forEach(dir => {
                const newRow = cellRow + dir.y;
                const newCol = cellCol + dir.x;
                if (newRow >= 0 && newRow < controlLayer[currentLevel].size.rows &&
                    newCol >= 0 && newCol < controlLayer[currentLevel].size.cols) {
                    const adjacentPos = newRow * controlLayer[currentLevel].size.cols + newCol;
                    if (reachableCells.has(adjacentPos)) {
                        const distance = reachableCells.get(adjacentPos);
                        minDistance = Math.min(minDistance, distance + 1);
                    }
                }
            });

            // 如果最短距離在移動範圍內，且沒有玩家站在這個位置，則標記為可移動
            if (minDistance <= moveRange) {
                const hasPlayer = gameplayers.some(player => player.Position === cell);
                if (!hasPlayer) {
                    // ===== Canvas highlight integration =====
                    moveTargetCells.add(cell);
                    highlightCells.push({
                        x: cellCol,
                        y: cellRow,
                        color: 'rgba(245, 0, 216, 0.78)'
                    });
                }
            }
        }
    });

    // 高亮所有可到達的格子
    reachableCells.forEach((distance, cell) => {
        // 檢查是否為隊友位置
        const isTeammatePosition = gameplayers.some((player, index) =>
            index !== playerIndex && player.Position === cell
        );

        // 檢查是否有任何角色在這個位置
        const hasAnyCharacter = gameplayers.some(player => player.Position === cell) ||
            gameenemys.some(enemy => enemy.Position === cell && enemy.CurHP > 0);

        // 只有不是隊友位置且沒有其他角色的格子才高亮
        if (!isTeammatePosition && !hasAnyCharacter) {
            // ===== Canvas highlight integration =====
            moveTargetCells.add(cell);
            highlightCells.push({
                x: cell % controlLayer[currentLevel].size.cols,
                y: Math.floor(cell / controlLayer[currentLevel].size.cols),
                color: 'rgba(245, 0, 216, 0.78)'
            });
        }
    });
}

function DrawPlayerMoveRange(startPosition, playerIndex, moveRange) {
    if (runOBJ["當前操作"] === "正在移動中" || runOBJ["當前操作"] === "已經移動但未進行操作") {
        return;
    }
    // 清理再畫
    clearAllHighlights();
    bfs(startPosition, moveRange, playerIndex);
    render();

    // 記錄當前可移動玩家與狀態
    currentMovePlayerIdx = playerIndex;
    runOBJ["當前操作"] = "選擇移動格子中";
}

// 只顯示移動範圍，不允許移動（用於已移動的玩家）
function DrawPlayerMoveRangeViewOnly(startPosition, playerIndex, moveRange) {
    if (runOBJ["當前操作"] === "正在移動中" || runOBJ["當前操作"] === "已經移動但未進行操作") {
        return;
    }

    console.log(`顯示玩家 ${playerIndex} 的移動範圍（僅查看模式）`);

    // 強制清理所有移動相關狀態
    clearAllHighlights();

    // 確保 moveTargetCells 被清空
    if (typeof moveTargetCells !== 'undefined') {
        moveTargetCells.clear();
        console.log("強制清空 moveTargetCells");
    }

    // 顯示僅查看的移動範圍
    bfsViewOnly(startPosition, moveRange, playerIndex);
    render();

    // 明確設置為不可移動狀態
    currentMovePlayerIdx = null;
    runOBJ["當前操作"] = "查看移動範圍中";

    console.log(`玩家 ${playerIndex} 設置為僅查看模式，currentMovePlayerIdx: ${currentMovePlayerIdx}`);
}

// BFS 算法（僅查看模式）- 只顯示移動範圍，不添加到可移動目標
function bfsViewOnly(startPosition, moveRange, playerIndex) {
    const directions = [
        { x: 0, y: 1 },   // Down
        { x: 1, y: 0 },   // Right
        { x: 0, y: -1 },  // Up
        { x: -1, y: 0 }   // Left
    ];

    moveRange = Math.floor(moveRange);

    // 獲取所有敵人的位置（與正常 BFS 一致）
    const enemyPositions = gameenemys
        .filter(enemy => enemy.CurHP > 0)
        .map(enemy => enemy.Position);

    // 獲取所有敵人周圍的格子（上下左右）
    const enemyAdjacentCells = new Set();
    enemyPositions.forEach(enemyPos => {
        const enemyRow = Math.floor(enemyPos / controlLayer[currentLevel].size.cols);
        const enemyCol = enemyPos % controlLayer[currentLevel].size.cols;

        directions.forEach(dir => {
            const newRow = enemyRow + dir.y;
            const newCol = enemyCol + dir.x;
            if (newRow >= 0 && newRow < controlLayer[currentLevel].size.rows &&
                newCol >= 0 && newCol < controlLayer[currentLevel].size.cols) {
                const adjacentPos = newRow * controlLayer[currentLevel].size.cols + newCol;
                enemyAdjacentCells.add(adjacentPos);
            }
        });
    });

    const queue = [{ position: startPosition, distance: 0 }];
    const visited = new Set();
    visited.add(startPosition);

    // 用於存儲所有可到達的格子及其距離
    const reachableCells = new Map();
    reachableCells.set(startPosition, 0);

    while (queue.length > 0) {
        const { position, distance } = queue.shift();

        if (distance < moveRange) {
            directions.forEach(direction => {
                const newX = (position % controlLayer[currentLevel].size.cols) + direction.x;
                const newY = Math.floor(position / controlLayer[currentLevel].size.cols) + direction.y;
                const newPosition = newY * controlLayer[currentLevel].size.cols + newX;

                // 檢查邊界
                if (newX >= 0 && newX < controlLayer[currentLevel].size.cols &&
                    newY >= 0 && newY < controlLayer[currentLevel].size.rows) {

                    // 檢查是否為隊友位置（與正常 BFS 一致）
                    const isTeammatePosition = gameplayers.some((player, index) =>
                        index !== playerIndex && player.Position === newPosition
                    );

                    // 使用與正常 BFS 相同的檢查邏輯
                    if (
                        !visited.has(newPosition) &&
                        !gameobstacles.includes(newPosition) &&
                        !gameenemys.some(enemy => enemy.Position === newPosition && enemy.CurHP > 0) &&
                        (!enemyAdjacentCells.has(newPosition) || isTeammatePosition)  // 如果是隊友位置，允許穿過敵人周圍格子
                    ) {
                        visited.add(newPosition);
                        const newDistance = distance + 1;
                        queue.push({ position: newPosition, distance: newDistance });
                        reachableCells.set(newPosition, newDistance);
                    }
                }
            });
        }
    }

    // 為每個可到達的格子高亮顯示（但不添加到可移動目標）
    reachableCells.forEach((minDistance, cell) => {
        const cellCol = cell % controlLayer[currentLevel].size.cols;
        const cellRow = Math.floor(cell / controlLayer[currentLevel].size.cols);

        // 檢查是否在移動範圍內
        if (minDistance <= moveRange) {
            const hasPlayer = gameplayers.some(player => player.Position === cell);
            if (!hasPlayer) {
                // 使用更深的顏色表示已移動玩家的查看模式
                highlightCells.push({
                    x: cellCol,
                    y: cellRow,
                    color: 'rgba(80, 80, 80, 0.8)' // 深灰色，透明度更高
                });
            }
        }
    });

    console.log(`玩家 ${playerIndex} 移動範圍顯示完成（僅查看模式，與 BFS 邏輯一致）`);
}

// 重寫的findPath函數 - 與canvas地圖系統完美配合
function findPath(start, end) {
    // 使用當前關卡的地圖尺寸
    const mapCols = controlLayer[currentLevel].size.cols;
    const mapRows = controlLayer[currentLevel].size.rows;

    // 方向陣列：上、右、下、左
    const directions = [
        { x: 0, y: -1 },  // up
        { x: 1, y: 0 },   // right
        { x: 0, y: 1 },   // down
        { x: -1, y: 0 }   // left
    ];

    // 初始化BFS所需的資料結構
    let queue = [start];
    let visited = new Set([start]);
    let parent = new Map();
    parent.set(start, null);

    // BFS尋路
    while (queue.length > 0) {
        let current = queue.shift();

        // 如果到達目標，重建路徑
        if (current === end) {
            let path = [];
            let node = end;
            while (node !== null) {
                path.unshift(node);
                node = parent.get(node);
            }
            return path;
        }

        // 探索四個方向
        let currentRow = Math.floor(current / mapCols);
        let currentCol = current % mapCols;

        for (let direction of directions) {
            let newRow = currentRow + direction.y;
            let newCol = currentCol + direction.x;
            let newPos = newRow * mapCols + newCol;

            // 檢查邊界
            if (newRow < 0 || newRow >= mapRows || newCol < 0 || newCol >= mapCols) {
                continue;
            }

            // 檢查是否已訪問
            if (visited.has(newPos)) {
                continue;
            }

            // 檢查障礙物
            if (gameobstacles.includes(newPos)) {
                continue;
            }

            // 檢查敵人（活著的敵人不能通過）
            if (gameenemys.some(enemy => enemy.Position === newPos && enemy.CurHP > 0)) {
                continue;
            }

            // 隊友位置可以穿過，不需要檢查隊友阻擋
            // 這樣角色就可以在移動過程中穿過隊友的座標

            // 加入佇列
            visited.add(newPos);
            parent.set(newPos, current);
            queue.push(newPos);
        }
    }

    // 找不到路徑
    return [];
}

// 重寫的animatePlayerMove函數 - 格子間平滑移動動畫
function animatePlayerMove(path, index) {
    if (path.length === 0) {
        console.warn("路徑為空，無法進行移動動畫");
        return;
    }

    let currentPathIndex = 0;
    let player = gameplayers[index];
    let stepaudio = operates.playSound("./Public/footstepsound.mp3", { loop: true });
    if (!stepaudio) {
        console.warn("播放玩家移動音效失敗");
    }

    // 停止玩家的站立動畫
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === index
    );
    if (playerObj) {
        stopStandAnimation(playerObj);
    }

    // 動畫參數
    const moveSpeed = 380; // 每步移動時間(毫秒)
    const frameRate = 16; // 約60fps (1000/60 ≈ 16ms)

    function animateStep() {
        // 檢查是否到達路徑終點
        if (currentPathIndex >= path.length - 1) {
            // 玩家到達目的地
            let finalPos = path[currentPathIndex];
            player.Position = finalPos;

            // 停止移動音效（如果存在）
            if (stepaudio) {
                stepaudio.pause();
            }

            clearAllHighlights();

            // 移動完成後使用 Stand 圖片
            setPlayerStandImage(player, index, finalPos);

            updateMapObjects(); // 重新更新所有物件到最終位置
            render(); // 重新渲染

            // 重新啟動站立動畫
            let playerObj = mapObjects.find(obj =>
                obj.type === 'player' && obj.playerIndex === index
            );
            if (playerObj) {
                resumeStandAnimation(playerObj);


            }

            // 設置遊戲狀態
            runOBJ["當前操作"] = "已經移動但未進行操作";
            ShowPlayerOperation(player, index);

            operates.MoveComera(player.Position);
            return;
        }

        let currentPos = path[currentPathIndex];
        let nextPos = path[currentPathIndex + 1];

        // 計算當前和下一個位置的行列座標
        let rowCurrent = Math.floor(currentPos / controlLayer[currentLevel].size.cols);
        let colCurrent = currentPos % controlLayer[currentLevel].size.cols;
        let rowNext = Math.floor(nextPos / controlLayer[currentLevel].size.cols);
        let colNext = nextPos % controlLayer[currentLevel].size.cols;

        // 獲取移動方向
        let directionIndex = getDirection(rowCurrent, colCurrent, rowNext, colNext);
        let directionName = getDirectionName(directionIndex);

        // 驗證方向和MoveRes
        if (directionIndex < 0 || !directionName || !player.MoveRes[directionName]) {
            console.error(`無效的移動方向: ${directionIndex}, ${directionName}`);
            console.error("玩家MoveRes:", player.MoveRes);
            return;
        }

        // 記錄最後移動的方向
        player.lastdirect = directionName;

        // 開始平滑移動動畫
        smoothMoveToNextCell(
            currentPos,
            nextPos,
            player,
            index,
            directionName,
            moveSpeed,
            () => {
                // 移動完成後的回調
                currentPathIndex++;
                animateStep(); // 繼續下一步
            }
        );
    }

    // 開始動畫
    animateStep();
}

// 平滑移動到下一個格子的函數
function smoothMoveToNextCell(fromPos, toPos, player, playerIndex, direction, duration, onComplete) {
    const startTime = performance.now();

    // 計算起始和目標座標
    const fromCol = fromPos % controlLayer[currentLevel].size.cols;
    const fromRow = Math.floor(fromPos / controlLayer[currentLevel].size.cols);
    const toCol = toPos % controlLayer[currentLevel].size.cols;
    const toRow = Math.floor(toPos / controlLayer[currentLevel].size.cols);

    // 計算像素座標
    const startX = fromCol * cellWidth;
    const startY = fromRow * cellHeight;
    const endX = toCol * cellWidth;
    const endY = toRow * cellHeight;

    // 獲取移動圖片
    let moveImages = player.MoveRes[direction];
    let animationFrameIndex = 0;

    function animate(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1); // 0 到 1 的進度

        // 使用緩動函數讓移動更自然
        const easeProgress = easeInOutQuad(progress);

        // 計算當前位置
        const currentX = startX + (endX - startX) * easeProgress;
        const currentY = startY + (endY - startY) * easeProgress;

        // 選擇當前動畫幀
        let currentImage;
        if (Array.isArray(moveImages)) {
            // 根據時間循環播放動畫幀
            const frameIndex = Math.floor((elapsed / 100)) % moveImages.length; // 每100ms換一幀
            currentImage = moveImages[frameIndex];
        } else if (typeof moveImages === 'string') {
            currentImage = moveImages;
        }

        // 更新玩家在mapObjects中的位置和圖片
        updatePlayerSmoothPosition(playerIndex, currentX, currentY, currentImage);

        // 重新渲染
        render();

        // 檢查動畫是否完成
        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            // 動畫完成，確保玩家位置精確
            updatePlayerSmoothPosition(playerIndex, endX, endY, currentImage);
            render();
            onComplete();
        }
    }

    requestAnimationFrame(animate);
}

// 緩動函數 - 讓移動更自然
function easeInOutQuad(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
}

// 設置玩家站立圖片的函數
function setPlayerStandImage(player, playerIndex, position) {
    // 使用記錄的最後移動方向
    let lastDirection = player.lastdirect || 'down'; // 使用 lastdirect 屬性，預設為 down

    console.log(`玩家 ${player.name} 最後移動方向: ${lastDirection}`);

    // 獲取對應方向的站立圖片
    let standImages = player.Stand && player.Stand[lastDirection];
    if (!standImages || !Array.isArray(standImages) || standImages.length === 0) {
        // 如果沒有對應方向的站立圖片，使用預設圖片
        console.warn(`玩家 ${player.name} 沒有 ${lastDirection} 方向的站立圖片，使用 down 方向`);
        standImages = player.Stand && player.Stand['down'];
        if (!standImages) {
            console.warn(`玩家 ${player.name} 沒有站立圖片`);
            return;
        }
    }

    // 找到玩家物件並更新其站立圖片陣列
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );

    if (playerObj) {
        // 更新站立圖片陣列為新方向
        playerObj.standImages = standImages;
        playerObj.currentFrameIndex = 0;

        // 使用第一張站立圖片
        let standImageSrc = standImages[0];

        console.log(`設置玩家 ${player.name} 站立圖片: ${standImageSrc}`);

        // 更新玩家物件的圖片和位置
        updatePlayerPositionInMapObjects(playerIndex, position, standImageSrc);
    }
}

// 更新玩家平滑移動位置的函數
function updatePlayerSmoothPosition(playerIndex, pixelX, pixelY, imageSrc) {
    // 找到對應的玩家物件
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );

    if (!playerObj) {
        // 如果找不到玩家物件，嘗試通過名稱查找
        let player = gameplayers[playerIndex];
        if (player) {
            playerObj = mapObjects.find(obj =>
                obj.type === 'player' && obj.name === player.name
            );
        }
    }

    if (playerObj) {
        // 設置自定義像素位置（用於平滑移動）
        playerObj.customX = pixelX;
        playerObj.customY = pixelY;
        playerObj.useCustomPosition = true;

        // 使用 PlayersData 中的 width 和 height
        let player = gameplayers[playerIndex];
        if (player) {
            // 解析 width 和 height（可能是字串或數字）
            let width = parseInt(player.width) || cellWidth;
            let height = parseInt(player.height) || cellHeight;

            // 更新物件尺寸
            playerObj.width = width;
            playerObj.height = height;
        }

        // 更新圖片
        if (imageSrc) {
            const newSrc = imageSrc.startsWith('http') ? imageSrc :
                imageSrc.startsWith('./') ? imageSrc :
                    './' + imageSrc;

            if (!playerObj.img.src.endsWith(imageSrc) && !playerObj.img.src.endsWith(newSrc)) {
                let newImg = new Image();
                newImg.onload = () => {
                    playerObj.img = newImg;
                };
                newImg.onerror = () => {
                    console.warn(`無法載入圖片: ${imageSrc}`);
                };
                newImg.src = newSrc;
            }
        }
    } else {
        console.warn(`找不到玩家物件: playerIndex=${playerIndex}`);
    }
}

// 輔助函數：根據方向索引獲取方向名稱
function getDirectionName(directionIndex) {
    const directionNames = ['up', 'right', 'down', 'left'];
    return directionNames[directionIndex];
}

// 輔助函數：更新玩家在mapObjects中的位置和圖片
function updatePlayerPositionInMapObjects(playerIndex, position, imageSrc) {
    // 找到對應的玩家物件
    let playerObj = mapObjects.find(obj =>
        obj.type === 'player' && obj.playerIndex === playerIndex
    );

    if (!playerObj) {
        // 如果找不到玩家物件，嘗試通過名稱查找
        let player = gameplayers[playerIndex];
        if (player) {
            playerObj = mapObjects.find(obj =>
                obj.type === 'player' && obj.name === player.name
            );
        }
    }

    if (playerObj) {
        // 更新位置
        playerObj.gridX = position % controlLayer[currentLevel].size.cols;
        playerObj.gridY = Math.floor(position / controlLayer[currentLevel].size.cols);

        // 清除自定義位置（回到格子對齊）
        playerObj.useCustomPosition = false;
        playerObj.customX = undefined;
        playerObj.customY = undefined;

        // 使用 PlayersData 中的 width 和 height
        let player = gameplayers[playerIndex];
        if (player) {
            // 解析 width 和 height（可能是字串或數字）
            let width = parseInt(player.width) || cellWidth;
            let height = parseInt(player.height) || cellHeight;

            // 更新物件尺寸
            playerObj.width = width;
            playerObj.height = height;
        }

        // 更新圖片
        if (imageSrc) {
            // 檢查是否需要載入新圖片
            const currentSrc = playerObj.img.src;
            const newSrc = imageSrc.startsWith('http') ? imageSrc :
                imageSrc.startsWith('./') ? imageSrc :
                    './' + imageSrc;

            if (!currentSrc.endsWith(imageSrc) && !currentSrc.endsWith(newSrc)) {
                let newImg = new Image();
                newImg.onload = () => {
                    playerObj.img = newImg;
                };
                newImg.onerror = () => {
                    console.warn(`無法載入圖片: ${imageSrc}`);
                };
                newImg.src = newSrc;
            }
        }
    } else {
        console.warn(`找不到玩家物件: playerIndex=${playerIndex}`);
    }
}

//移動完後或點擊兩次玩家顯示玩家操作選單
function ShowPlayerOperation(player, index) {
    let playerIndex = index;
    let playerPosition = player.Position; // 這是玩家移動後的新位置
    let playername = player.name;

    //角色是否座標改變
    let ischangePos = playerPosition !== player.OldPosition;

    console.log(`顯示玩家 ${playername} 操作選單:`);
    console.log(`- 舊位置: ${player.OldPosition}`);
    console.log(`- 新位置: ${playerPosition}`);
    console.log(`- 是否移動: ${ischangePos}`);

    // 計算玩家新位置的行列座標
    const playerCol = playerPosition % controlLayer[currentLevel].size.cols;
    const playerRow = Math.floor(playerPosition / controlLayer[currentLevel].size.cols);

    console.log(`- 格子座標: (${playerRow}, ${playerCol})`);

    // 獲取canvas的x,y座標（基於玩家的新位置）
    const canvasX = playerCol * cellWidth;
    const canvasY = playerRow * cellHeight;

    // 考慮相機偏移，計算操作選單的正確位置
    const menuX = canvasX - cameraX - 105;
    const menuY = canvasY - cameraY - 50;

    console.log(`- Canvas座標: (${canvasX}, ${canvasY})`);
    console.log(`- 相機偏移: (${cameraX}, ${cameraY})`);
    console.log(`- 選單位置: (${menuX}, ${menuY})`);

    // 在canvas的x,y座標中插入操作選單
    const options = document.createElement('div');
    options.id = 'options';
    options.style.position = 'absolute';
    options.style.left = `${menuX}px`;
    options.style.top = `${menuY}px`;
    options.style.zIndex = '1000'; // 確保選單在最上層

    const menuItems = ['strengthbtn', 'magicbtn', 'atkbtn', 'bagbtn', 'awaitbtn'];
    menuItems.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.className = 'menu';
        menuItem.id = item;
        if (ischangePos) {
            menuItem.style.opacity = '1';
        } else {
            menuItem.style.opacity = '1';
        }

        // 在創建按鈕的同時設置事件處理器
        setupButtonEventHandler(menuItem, item, player, playerIndex, playerPosition, ischangePos);

        options.appendChild(menuItem);
    });

    const targetElement = document.getElementById('GameMap'); // 確保targetElement是canvas本身
    if (!targetElement) {
        console.error("Element not found for canvas:", targetElement);
        return;
    }
    targetElement.appendChild(options)

    runOBJ["當前操作"] = "已經移動但未進行操作";

    //取消動作按鈕
    Dom.CancelBtn.onclick = async function () {
        console.log("點擊取消按鈕");

        // 檢查是否在營地場景
        if (typeof sceneManager !== 'undefined' && sceneManager.sceneType === 'camp') {
            console.log("在營地場景中，取消動作按鈕無效");
            return;
        }

        // 在戰鬥場景中的檢查
        if (typeof runOBJ !== 'undefined') {
            if (runOBJ["當前行動方"] === "Enemys") {
                console.log("敵人回合，無法取消");
                return;
            }
            // 允許在以下狀態取消動作：
            // - "選擇移動格子中" (顯示移動範圍時)
            // - "選擇攻擊目標中" (選擇攻擊目標時)
            // - "已經移動但未進行操作" (移動後顯示操作選單時)
            // 禁止在以下狀態取消動作：
            // - "正在移動中", "攻擊中"
            const blockedStates = ["正在移動中", "攻擊中"];
            if (blockedStates.includes(runOBJ["當前操作"])) {
                console.log(`當前操作狀態 "${runOBJ["當前操作"]}" 正在執行，無法取消`);
                return;
            }
            // 如果沒有任何操作可以取消
            if (runOBJ["當前操作"] === null && runOBJ["當前選取"] === null) {
                console.log("沒有可取消的操作");
                return;
            }
        } else {
            console.log("runOBJ 未定義，無法取消動作");
            return;
        }

        // 找到當前選中的玩家
        let currentPlayer = null;
        let currentPlayerIndex = -1;

        // 從runOBJ["當前選取"]獲取玩家索引
        if (runOBJ["當前選取"] !== null) {
            // runOBJ["當前選取"]通常是玩家索引
            currentPlayerIndex = runOBJ["當前選取"];
            if (currentPlayerIndex >= 0 && currentPlayerIndex < gameplayers.length) {
                currentPlayer = gameplayers[currentPlayerIndex];
            }
        }

        // 如果沒有找到當前選中的玩家，嘗試找到已移動但未完成操作的玩家
        if (!currentPlayer) {
            for (let i = 0; i < gameplayers.length; i++) {
                if (gameplayers[i].Position !== gameplayers[i].OldPosition && !gameplayers[i].AlreadyMove) {
                    currentPlayer = gameplayers[i];
                    currentPlayerIndex = i;
                    break;
                }
            }
        }

        // 如果還是沒有找到，檢查是否有玩家的當前位置與舊位置不同
        if (!currentPlayer) {
            for (let i = 0; i < gameplayers.length; i++) {
                if (gameplayers[i].Position !== gameplayers[i].OldPosition) {
                    currentPlayer = gameplayers[i];
                    currentPlayerIndex = i;
                    break;
                }
            }
        }

        console.log("當前選中的玩家:", currentPlayer ? currentPlayer.name : "無");

        // 檢查當前選中的玩家是否為AI控制
        if (currentPlayer && currentPlayer["是否電腦操作"]) {
            console.log(`玩家 ${currentPlayer.name} 是AI控制，取消按鈕對AI角色無效`);
            return;
        }

        // 清除攻擊相關的高亮和事件監聽器
        clearAllAttackRelated();
        clearAllHighlights();

        // 清除法術相關的事件監聽器和高亮
        if (typeof clearAllMagicEventHandlers === 'function') {
            clearAllMagicEventHandlers();
        }
        if (typeof clearAllMagicRangeHighlights === 'function') {
            clearAllMagicRangeHighlights();
        }

        // 清除所有格子的事件處理器
        document.querySelectorAll('[id]').forEach(cell => {
            cell.onmouseover = null;
            cell.onmouseout = null;
            cell.onclick = null;
        });

        // 清除背包的滑鼠事件
        document.querySelectorAll('.bagitem').forEach(item => {
            item.removeEventListener('click', operates.HandleBagItemClick);
        });

        // 移除操作選單
        const existingOptions = document.getElementById('options');
        if (existingOptions) {
            existingOptions.remove();
            console.log("移除操作選單");
        }

        // 移除背包操作選單
        const existingBagOptions = document.getElementById('optionsbag');
        if (existingBagOptions) {
            existingBagOptions.remove();
            console.log("移除背包操作選單");
        }

        // 關閉物品對話框
        const itemDialog = document.getElementById('itemdialog');
        if (itemDialog) {
            itemDialog.close();
            document.getElementById("InfoDialog").innerHTML = "";
            Dom.InfoDialog.style.zIndex = -1;
            console.log("關閉物品對話框");
        }

        // 關閉法術對話框
        const magicDialog = document.getElementById('magicdialog');
        if (magicDialog) {
            magicDialog.close();
            document.getElementById("InfoDialog").innerHTML = "";
            console.log("關閉法術對話框");
        }

        // 如果有選中的玩家且該玩家已移動但未完成操作，則返回原位置
        if (currentPlayer && currentPlayer.Position !== currentPlayer.OldPosition) {
            console.log(`取消移動：將玩家 ${currentPlayer.name} 從位置 ${currentPlayer.Position} 移回 ${currentPlayer.OldPosition}`);
            console.log(`玩家狀態 - AlreadyMove: ${currentPlayer.AlreadyMove}`);

            // 重置玩家位置
            const oldPosition = currentPlayer.OldPosition;
            currentPlayer.Position = oldPosition;

            // 重置移動狀態
            currentPlayer.AlreadyMove = false;

            console.log(`玩家 ${currentPlayer.name} 位置已重置為: ${currentPlayer.Position}`);

            // 更新相機位置到舊位置
            operates.MoveComera(oldPosition);

            // 強制更新canvas中的玩家位置
            console.log("開始更新 mapObjects...");
            await updateMapObjects();

            console.log("mapObjects 更新完成，開始渲染...");
            render();

            console.log(`玩家 ${currentPlayer.name} 已返回原位置 ${oldPosition}`);
        } else if (currentPlayer) {
            console.log(`玩家 ${currentPlayer.name} 沒有移動，無需返回原位置`);
            console.log(`當前位置: ${currentPlayer.Position}, 舊位置: ${currentPlayer.OldPosition}, AlreadyMove: ${currentPlayer.AlreadyMove}`);
        } else {
            console.log("沒有找到需要返回原位置的玩家");
        }

        // 重置遊戲狀態
        runOBJ["當前操作"] = null;
        runOBJ["當前選取"] = null;
        runOBJ["第二次選取"] = null;
        runOBJ["當前選取物品"] = null;
        runOBJ["當前物品操作"] = null;
        tempname = [];

        console.log("取消操作完成，所有狀態已重置");

        // 重新綁定 EndTurnBtn 的點擊事件
        Dom.EndTurnBtn.onclick = () => {
            console.log(runOBJ["當前操作"]);
            if (runOBJ["當前行動方"] === "Enemys") {
                return;
            }
            if (runOBJ["當前操作"] !== null) {
                return;
            }

            let questionwindow = document.createElement("dialog");
            questionwindow.id = "questiondialog";
            questionwindow.innerHTML = `
                <div id="questiondialog_inner">
                    <div id="question">確定要結束回合嗎？</div>
                    <div id="questionbtn">
                        <div id="yes">確定</div>
                        <div id="no">取消</div>
                    </div>
                </div>
            `;
            Dom.InfoDialog.appendChild(questionwindow);
            questionwindow.showModal();

            document.getElementById("yes").onclick = function () {
                questionwindow.close();
                Dom.InfoDialog.removeChild(questionwindow);
                runOBJ["當前行動方"] = "Enemys";
                resetRunOBJ();
                EnemysAction()
            }

            document.getElementById("no").onclick = function () {
                questionwindow.close();
                Dom.InfoDialog.removeChild(questionwindow);
            }
        };
    }

    // 舊的按鈕事件處理器已移除，現在由 setupButtonEventHandler 函數處理

    // 設置攻擊按鈕事件處理器
    const atkBtn = document.getElementById("atkbtn");
    if (atkBtn) {
        atkBtn.onclick = function () {
            console.log(`玩家 ${player.name} 點擊攻擊按鈕`);

            if (!booleancanatkenemy(player, gameenemys)) {
                alert("攻擊範圍內沒有敵人");
                return;
            }

            let playerPosition = player.Position;
            let playerATKRange = player.ATKRange;

            console.log(`玩家位置: ${playerPosition}, 攻擊範圍: ${playerATKRange}`);

            // 移除操作選單
            const existingOptions = document.getElementById('options');
            if (existingOptions) {
                existingOptions.remove();
                console.log("移除操作選單");
            }

            // 清除所有高亮
            clearAllHighlights();

            // 設置攻擊模式
            setTimeout(() => {
                runOBJ["當前操作"] = "選擇攻擊目標中";
            }, 100);

            // 顯示攻擊範圍並設置點擊事件
            showPlayerAttackRange(playerPosition, playerIndex, playerATKRange);
        };

        // 攻擊範圍顯示已移到獨立函數中

        // 攻擊點擊事件處理已移到獨立函數中

        // 攻擊執行和高亮清除已移到獨立函數中
    }
    // 背包按鈕事件處理器已移到 setupButtonEventHandler 函數中
    // 蓄力按鈕事件處理器已移到 setupButtonEventHandler 函數中

    // 設置法術按鈕事件處理器
    const magicBtn = document.getElementById("magicbtn");
    if (magicBtn) {
        magicBtn.onclick = async function () {
            let leftmove = getleftmove(player.Position, player.OldPosition, player.Move);
            magicfun(player, leftmove);
        };
    }

}


// 設置按鈕事件處理器的函數
function setupButtonEventHandler(buttonElement, buttonType, player, playerIndex, playerPosition, ischangePos) {
    switch (buttonType) {
        case 'awaitbtn':
            buttonElement.onclick = async function () {
                console.log(`玩家 ${player.name} 點擊等待按鈕`);

                // 重置選取狀態
                runOBJ["當前選取"] = null;
                runOBJ["第二次選取"] = null;
                runOBJ["當前物品操作"] = null;
                runOBJ["當前選取物品"] = null;

                // 清除攻擊相關的高亮和事件監聽器
                if (window.currentAttackClickHandler) {
                    canvas.removeEventListener('click', window.currentAttackClickHandler);
                    window.currentAttackClickHandler = null;
                }
                clearAllHighlights();
                clearAttackTargetHighlights();
                clearAttackRangeDisplay();

                // 移除操作選單
                const existingOptions = document.getElementById('options');
                if (existingOptions) {
                    existingOptions.remove();
                    console.log("移除操作選單");
                }

                /* 檢查並拾取寶箱 */
                let hasgetitem = false;
                for (const [index, treasure] of gametreasures.entries()) {
                    if (treasure["位置"] === player.Position) {
                        console.log(`玩家 ${player.name} 拾取寶物: ${treasure["寶物"].name}`);

                        player.Inventory.push(treasure["寶物"]);

                        // 顯示獲得物品訊息
                        await showyouget(player, treasure["寶物"]);

                        // 記錄寶箱已被拿取
                        if (typeof window.takenTreasures === 'undefined') {
                            window.takenTreasures = new Set();
                        }
                        window.takenTreasures.add(treasure["位置"]);
                        console.log(`記錄寶箱已拿取: 位置 ${treasure["位置"]}`);

                        // 從遊戲中移除寶物
                        gametreasures.splice(index, 1);
                        hasgetitem = true;
                        break;
                    }
                }

                // 完成等待操作的共同邏輯
                await completePlayerWaitAction(player, playerIndex, hasgetitem);
            };
            break;

        case 'atkbtn':
            buttonElement.onclick = function () {
                console.log(`玩家 ${player.name} 點擊攻擊按鈕`);

                if (!booleancanatkenemy(player, gameenemys)) {
                    alert("攻擊範圍內沒有敵人");
                    return;
                }

                let playerATKRange = player.ATKRange;

                console.log(`玩家位置: ${playerPosition}, 攻擊範圍: ${playerATKRange}`);

                // 移除操作選單
                const existingOptions = document.getElementById('options');
                if (existingOptions) {
                    existingOptions.remove();
                    console.log("移除操作選單");
                }

                // 清除所有高亮
                clearAllHighlights();

                // 設置攻擊模式
                setTimeout(() => {
                    runOBJ["當前操作"] = "選擇攻擊目標中";
                }, 100);

                // 顯示攻擊範圍並設置點擊事件
                showPlayerAttackRange(playerPosition, playerIndex, playerATKRange);
            };
            break;

        case 'bagbtn':
            buttonElement.onclick = function () {
                console.log(`玩家 ${player.name} 點擊背包按鈕`);

                // 清除攻擊相關的高亮和事件監聽器
                if (window.currentAttackClickHandler) {
                    canvas.removeEventListener('click', window.currentAttackClickHandler);
                    window.currentAttackClickHandler = null;
                }

                // 移除操作選單
                const existingOptions = document.getElementById('options');
                if (existingOptions) {
                    existingOptions.remove();
                }

                clearAllHighlights();
                clearAllEnemyHighlights();
                clearAttackTargetHighlights();
                clearAttackRangeDisplay();

                // 創建背包操作選單:轉移、使用、丟棄
                CreatePlayerOperationWindow(player);
            };
            break;

        case 'strengthbtn':
            buttonElement.onclick = function () {
                console.log(`玩家 ${player.name} 點擊蓄力按鈕`);

                if (ischangePos) return;

                // 重置選取狀態
                runOBJ["當前選取"] = null;
                runOBJ["第二次選取"] = null;
                runOBJ["當前物品操作"] = null;
                runOBJ["當前選取物品"] = null;

                // 清除攻擊相關的高亮和事件監聽器
                if (window.currentAttackClickHandler) {
                    canvas.removeEventListener('click', window.currentAttackClickHandler);
                    window.currentAttackClickHandler = null;
                }

                // 標記玩家已完成行動
                player.AlreadyMove = true;
                player.OldPosition = player.Position;

                // 移除操作選單
                const existingOptions = document.getElementById('options');
                if (existingOptions) {
                    existingOptions.remove();
                }

                // 清除所有高亮顯示
                clearAllHighlights();
                clearAttackTargetHighlights();
                clearAttackRangeDisplay();

                // 設置蓄力狀態
                player["是否蓄力"] = true;
                player["是否釋放蓄力"] = false;
                console.log(`玩家 ${player.name} 進入蓄力狀態`);

                // 更新canvas顯示
                updateMapObjects();
                render();

                // 重要：異步延遲使重複選取角色不會觸發兩次選單
                setTimeout(() => {
                    runOBJ["當前操作"] = null;
                }, 100);

                // 檢查是否所有玩家都已經移動
                if (allPlayersMoved()) {
                    runOBJ["當前行動方"] = "Enemys";
                    setTimeout(() => {
                        EnemysAction();
                    }, 500);
                } else {
                    runOBJ["當前行動方"] = "Players";
                }
            };
            break;

        case 'magicbtn':
            buttonElement.onclick = async function () {
                console.log(`玩家 ${player.name} 點擊法術按鈕`);

                // 清除攻擊相關的高亮和事件監聽器
                if (window.currentAttackClickHandler) {
                    canvas.removeEventListener('click', window.currentAttackClickHandler);
                    window.currentAttackClickHandler = null;
                }
                clearAllHighlights();
                clearAttackTargetHighlights();
                clearAttackRangeDisplay();

                let leftmove = getleftmove(player.Position, player.OldPosition, player.Move);
                magicfun(player, leftmove);
            };
            break;

        default:
            console.warn(`未知的按鈕類型: ${buttonType}`);
    }
}

// 顯示玩家攻擊範圍並設置點擊事件的完整函數
function showPlayerAttackRange(playerPosition, playerIndex, attackRange) {
    console.log(`顯示玩家 ${playerIndex} 的攻擊範圍，範圍: ${attackRange}`);

    // 計算攻擊範圍內的敵人
    let attackableEnemies = [];
    let playerRow = Math.floor(playerPosition / controlLayer[currentLevel].size.cols);
    let playerCol = playerPosition % controlLayer[currentLevel].size.cols;

    console.log(`玩家位置: 行${playerRow}, 列${playerCol}`);

    // 計算攻擊範圍內的所有格子
    let attackRangeCells = [];
    for (let row = playerRow - attackRange; row <= playerRow + attackRange; row++) {
        for (let col = playerCol - attackRange; col <= playerCol + attackRange; col++) {
            // 檢查是否在地圖範圍內
            if (row >= 0 && row < controlLayer[currentLevel].size.rows &&
                col >= 0 && col < controlLayer[currentLevel].size.cols) {

                // 計算曼哈頓距離
                let distance = Math.abs(playerRow - row) + Math.abs(playerCol - col);

                if (distance <= attackRange && distance > 0) { // 排除玩家自己的位置
                    let cellPosition = row * controlLayer[currentLevel].size.cols + col;
                    attackRangeCells.push({
                        position: cellPosition,
                        row: row,
                        col: col,
                        distance: distance
                    });
                }
            }
        }
    }

    console.log(`攻擊範圍格子數量: ${attackRangeCells.length}`);

    // 檢查攻擊範圍內的敵人
    for (let enemy of gameenemys) {
        if (enemy.CurHP <= 0) continue; // 跳過已死亡的敵人

        let enemyRow = Math.floor(enemy.Position / controlLayer[currentLevel].size.cols);
        let enemyCol = enemy.Position % controlLayer[currentLevel].size.cols;

        // 計算距離（曼哈頓距離）
        let distance = Math.abs(playerRow - enemyRow) + Math.abs(playerCol - enemyCol);

        if (distance <= attackRange) {
            attackableEnemies.push({
                enemy: enemy,
                position: enemy.Position,
                row: enemyRow,
                col: enemyCol,
                distance: distance
            });
            console.log(`可攻擊敵人: ${enemy.name} 位置: ${enemy.Position} 距離: ${distance}`);
        }
    }

    if (attackableEnemies.length === 0) {
        alert("攻擊範圍內沒有敵人");
        runOBJ["當前操作"] = null;
        return;
    }

    // 高亮顯示攻擊範圍和可攻擊的敵人
    highlightAttackRangeAndEnemies(attackRangeCells, attackableEnemies, playerIndex);

    // 設置點擊事件處理攻擊
    setupAttackClickHandlers(attackableEnemies, playerIndex, playerPosition);
}

// 高亮顯示攻擊範圍和可攻擊的敵人
function highlightAttackRangeAndEnemies(attackRangeCells, attackableEnemies, playerIndex) {
    // 清除之前的高亮
    clearAllHighlights();
    clearAttackTargetHighlights();

    console.log(`高亮顯示 ${attackRangeCells.length} 個攻擊範圍格子和 ${attackableEnemies.length} 個敵人`);

    // 高亮攻擊範圍格子
    for (let cell of attackRangeCells) {
        // 添加到highlightCells陣列中以便canvas繪製
        highlightCells.push({
            x: cell.col,
            y: cell.row,
            color: cell.distance === 1 ? 'rgba(245, 0, 216, 0.78)' : 'rgba(245, 0, 216, 0.78)' // 近距離用紅色，遠距離用橙色
        });
    }

    // 重新渲染canvas以顯示攻擊範圍高亮
    render();

    // 高亮可攻擊的敵人
    for (let enemyData of attackableEnemies) {
        let enemyObj = mapObjects.find(obj =>
            obj.type === 'enemy' && obj.enemyIndex !== undefined &&
            gameenemys[obj.enemyIndex] && gameenemys[obj.enemyIndex].Position === enemyData.position
        );

        if (enemyObj) {
            // 添加攻擊目標標記
            enemyObj.isAttackTarget = true;
            enemyObj.attackTargetPlayerIndex = playerIndex;
            console.log(`標記敵人 ${enemyData.enemy.name} 為攻擊目標`);
        }
    }

    // 重新渲染以顯示高亮
    render();
}



// 設置攻擊點擊事件處理
function setupAttackClickHandlers(attackableEnemies, playerIndex, playerPosition) {
    console.log(`設置攻擊點擊事件，可攻擊敵人數量: ${attackableEnemies.length}`);

    // 移除之前的攻擊點擊監聽器（如果存在）
    if (window.currentAttackClickHandler) {
        canvas.removeEventListener('click', window.currentAttackClickHandler);
    }

    // 創建新的攻擊點擊處理器
    const attackClickHandler = function (event) {
        if (runOBJ["當前操作"] !== "選擇攻擊目標中") {
            console.log("當前不在攻擊目標選擇模式");
            return;
        }

        // 獲取點擊位置
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left + cameraX;
        const y = event.clientY - rect.top + cameraY;

        // 計算點擊的格子位置
        const clickedCol = Math.floor(x / cellWidth);
        const clickedRow = Math.floor(y / cellHeight);
        const clickedPosition = clickedRow * controlLayer[currentLevel].size.cols + clickedCol;

        console.log(`點擊位置: (${clickedRow}, ${clickedCol}) = ${clickedPosition}`);

        // 檢查是否點擊了可攻擊的敵人
        let targetEnemyData = attackableEnemies.find(enemyData => enemyData.position === clickedPosition);

        if (targetEnemyData) {
            console.log(`選擇攻擊目標: ${targetEnemyData.enemy.name}`);

            // 移除點擊事件監聽器
            canvas.removeEventListener('click', attackClickHandler);
            window.currentAttackClickHandler = null;

            // 執行攻擊
            executePlayerAttack(playerIndex, targetEnemyData.enemy, playerPosition);
        } else {
            console.log("點擊位置沒有可攻擊的敵人");
            // 可以在這裡添加視覺反饋，比如顯示"無效目標"的提示
        }
    };

    // 添加點擊事件監聽器
    canvas.addEventListener('click', attackClickHandler);
    window.currentAttackClickHandler = attackClickHandler;

    console.log("攻擊點擊事件監聽器已設置");
}

// 執行玩家攻擊
async function executePlayerAttack(playerIndex, targetEnemy, playerPosition) {
    let player = gameplayers[playerIndex];
    let enemyIndex = gameenemys.findIndex(enemy => enemy.Position === targetEnemy.Position);

    console.log(`執行攻擊: ${player.name} -> ${targetEnemy.name}`);

    // 清除攻擊目標高亮和攻擊範圍顯示
    clearAttackTargetHighlights();
    clearAttackRangeDisplay();

    // 設置遊戲狀態
    runOBJ["當前操作"] = "攻擊中";
    player.AlreadyMove = true;
    player.OldPosition = player.Position;

    // 重置選取狀態
    runOBJ["當前物品操作"] = null;
    runOBJ["當前選取物品"] = null;

    // 處理蓄力狀態
    if (player["是否蓄力"] === true && player["是否釋放蓄力"] === false) {
        player.Move -= 2;
        player["是否蓄力"] = false;
        player["是否釋放蓄力"] = true;
        console.log(`玩家 ${player.name} 釋放蓄力，移動力減少2`);
    }

    // 讓玩家和敵人互相轉向對方
    await turnCharactersToFaceEachOther(player, targetEnemy, playerIndex, enemyIndex);

    // 執行戰鬥動畫
    await AccessBattle(player, targetEnemy, playerIndex, enemyIndex);
}

// 清除攻擊範圍顯示
function clearAttackRangeDisplay() {
    console.log("清除攻擊範圍顯示");

    // 清除highlightCells中的攻擊範圍高亮
    // 保留其他類型的高亮（如移動範圍、可點擊角色等）
    highlightCells = highlightCells.filter(cell => {
        // 移除紅色和橙色的攻擊範圍高亮
        return !(cell.color === 'rgba(245, 0, 216, 0.78)' || cell.color === 'rgba(245, 0, 216, 0.78)');
    });

    // 重新渲染以清除攻擊範圍指示器
    render();
}

// 清除所有攻擊相關的高亮和事件監聽器
function clearAllAttackRelated() {
    console.log("清除所有攻擊相關的高亮和事件監聽器");

    // 清除攻擊點擊事件監聽器
    if (window.currentAttackClickHandler) {
        canvas.removeEventListener('click', window.currentAttackClickHandler);
        window.currentAttackClickHandler = null;
    }

    // 清除攻擊相關的高亮
    clearAttackTargetHighlights();
    clearAttackRangeDisplay();
}

// 完成玩家等待操作的共同邏輯
async function completePlayerWaitAction(player, playerIndex, hasgetitem) {
    console.log(`完成玩家 ${player.name} 等待操作，是否拾取物品: ${hasgetitem}`);

    // 標記玩家已完成行動
    player.AlreadyMove = true;

    // 更新玩家的舊位置為當前位置
    player.OldPosition = player.Position;

    // 處理蓄力狀態
    if (player["是否蓄力"] === true && player["是否釋放蓄力"] === false) {
        player.Move -= 2;
        player["是否蓄力"] = false;
        player["是否釋放蓄力"] = true;
        console.log(`玩家 ${player.name} 釋放蓄力，移動力減少2`);
    }

    // 清除高亮顯示
    clearAllHighlights();
    clearAttackTargetHighlights();

    // 更新canvas顯示
    updateMapObjects();
    render();

    // 重要：異步延遲使重複選取角色不會觸發兩次選單
    setTimeout(() => {
        runOBJ["當前操作"] = null;
    }, 100);

    // 檢查是否所有玩家都已經移動
    let allPlayersCompleted = gameplayers.every(p => p.AlreadyMove === true);
    console.log(`所有玩家是否已移動: ${allPlayersCompleted}`);

    if (allPlayersCompleted) {
        runOBJ["當前行動方"] = "Enemys";
        console.log("切換到敵人回合");

        setTimeout(() => {
            EnemysAction();
        }, 500);
    } else {
        runOBJ["當前行動方"] = "Players";
        console.log("繼續玩家回合");
    }
}

// 清除攻擊目標高亮
function clearAttackTargetHighlights() {
    console.log("清除攻擊目標高亮");

    let clearedCount = 0;
    for (let obj of mapObjects) {
        if (obj.isAttackTarget) {
            obj.isAttackTarget = false;
            obj.attackTargetPlayerIndex = undefined;
            clearedCount++;
        }
    }

    console.log(`清除了 ${clearedCount} 個攻擊目標標記`);

    // 重新渲染以移除高亮效果
    render();
}

function resetRunOBJ() {
    runOBJ["當前操作"] = null;
    runOBJ["當前選取"] = null;
    runOBJ["第二次選取"] = null;
    runOBJ["當前選取物品"] = null;
    runOBJ["當前物品操作"] = null;
    tempname = [];
}

function allPlayersMoved() {
    return gameplayers.every(player => player.AlreadyMove === true);
}


function getleftmove(newPos, oldPos, RoleMove) {
    let row = Math.floor(newPos / controlLayer[currentLevel].size.cols);
    let col = newPos % controlLayer[currentLevel].size.cols;
    let oldrow = Math.floor(oldPos / controlLayer[currentLevel].size.cols);
    let oldcol = oldPos % controlLayer[currentLevel].size.cols;
    let distance = Math.abs(row - oldrow) + Math.abs(col - oldcol)
    return RoleMove - distance
}

//由玩家進入戰鬥畫面
async function AccessBattle(player, enemy, playerIndex, enemyIndex) {
    const delay = {
        initial: 600,   // 初始延遲
    };

    // 創建戰鬥場景、資訊(玩家、敵人血條)
    let { playerCanvas, enemyCanvas } = await CreateBattleInfo(player, enemy);

    //敵人攻擊玩家的命中率
    let playerAvoidRate = (enemy.HitRate - player.AvoidRate) + Math.floor(Math.random() * 100);

    //玩家攻擊敵人的命中率
    let enemyAvoidRate = (player.HitRate - enemy.AvoidRate) + Math.floor(Math.random() * 100);

    //配對閃避的戰鬥情況用
    const getbattlesituation = (playerAvoidRate, enemyAvoidRate) => {
        const situation = {
            "player": "未閃避",
            "enemy": "未閃避"
        };
        if (playerAvoidRate < 100) {
            situation["player"] = "閃避"; // 玩家閃避掉了
        }
        if (enemyAvoidRate < 100) {
            situation["enemy"] = "閃避"; // 敵人閃避掉了
        }
        return situation;
    }

    //配對{暴擊、閃避}的戰鬥情況用
    const matchbattleprocess = async (situation) => {
        if (situation["player"] === "閃避" && situation["enemy"] === "閃避") {
            await playerAttackMiss(player, enemy, playerCanvas, enemyCanvas);
            // 如果玩家在敵人攻擊範圍內，則有反擊
            if (isPlayerInRange(player, enemy)) {
                await enemyAttackMiss(player, enemy, playerCanvas, enemyCanvas);
                await wait(1);
                await returnToMap(player)
                return;
            } else {
                // 如果玩家不在敵人攻擊範圍內，則返回地圖
                await wait(1);
                await returnToMap(player)
                return;
            }
        } else if (situation["player"] === "閃避") {
            let { isDead } = await playerAttack(player, enemy, enemyIndex, playerCanvas, enemyCanvas);

            if (isDead) {
                // 如果敵人死亡，清除戰鬥畫面
                await clearBattleCis(enemyCanvas, player, enemy, enemyIndex);
                return;
            } else {
                //如果玩家在敵人攻擊範圍內，則有反擊
                if (isPlayerInRange(player, enemy)) {
                    await enemyAttackMiss(player, enemy, playerCanvas, enemyCanvas);

                    await wait(1);

                    await BattleReturnToMap(player)
                    return;
                } else {
                    // 如果玩家不在敵人攻擊範圍內，則返回地圖
                    await wait(1);
                    await BattleReturnToMap(player)
                    return;
                }
            }
        } else if (situation["enemy"] === "閃避") {
            await playerAttackMiss(player, enemy, playerCanvas, enemyCanvas);
            if (isPlayerInRange(player, enemy)) {
                // 如果玩家在敵人攻擊範圍內，則有反擊
                let { playerisdied } = await enemyAttack(player, enemy, playerCanvas, enemyCanvas);
                if (playerisdied) {
                    // 如果玩家死亡，清除戰鬥畫面
                    await gameover(player, playerCanvas);
                    return;
                } else {
                    // 玩家沒有死亡，返回地圖
                    await returnToMap(player)
                    return;
                }
            } else {
                // 如果玩家不在敵人攻擊範圍內，則返回地圖
                await returnToMap(player)
                return;
            }
        } else {
            let { isDead } = await playerAttack(player, enemy, enemyIndex, playerCanvas, enemyCanvas);
            console.log("敵人是否死亡：", isDead);
            if (isDead) {
                // 如果敵人死亡，清除戰鬥畫面
                await clearBattleCis(enemyCanvas, player, enemy, enemyIndex);
                return;
            } else {
                //如果玩家在敵人攻擊範圍內，則有反擊
                if (isPlayerInRange(player, enemy)) {
                    let { playerisdied } = await enemyAttack(player, enemy, playerCanvas, enemyCanvas);
                    if (playerisdied) {
                        // 如果玩家死亡，清除戰鬥畫面
                        await gameover(player, playerCanvas)
                        return;
                    } else {
                        // 玩家沒有死亡，返回地圖
                        await BattleReturnToMap(player)
                        return
                    }
                } else {
                    // 如果玩家不在敵人攻擊範圍內，則返回地圖
                    await BattleReturnToMap(player)
                    return;
                }

            }

        }
    }

    setTimeout(() => {
        let situation = getbattlesituation(playerAvoidRate, enemyAvoidRate);
        matchbattleprocess(situation);
    }, delay.initial);

    return { battleisended: true };
}

//異步清理戰場環境(敵人死亡時)(經驗系統在這裡，但還未完善升級系統)**
async function clearBattleCis(enemyCanvas, player, enemy, enemyIndex) {
    await wait(2.5)
    clearEnemyAnimation(enemyCanvas)
    await wait(1);
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";
    Dom.BattleScreen.style.display = "none";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    setTimeout(() => {
        Dom.BattleScreen.innerHTML = "";
    })

    console.log(`清除敵人 ${enemy.name} (索引: ${enemyIndex}) 從遊戲中`);

    // 從gameenemys陣列中移除死亡的敵人
    if (enemyIndex >= 0 && enemyIndex < gameenemys.length) {
        gameenemys.splice(enemyIndex, 1);
        console.log(`敵人已從gameenemys陣列中移除，剩餘敵人數量: ${gameenemys.length}`);
    }

    // 清除敵人的DOM元素（如果存在）
    const enemyCell = document.getElementById(enemy.Position);
    if (enemyCell) {
        enemyCell.classList.remove("enemy");
        enemyCell.classList.remove("enemy" + enemyIndex);
        enemyCell.style.backgroundImage = "";
    }

    updateMapObjects();

    // 重新渲染canvas
    render();


    if (player["是否電腦操作"] === false) {
        player.Inventory.push(...enemy.RewardItems);

        player.Inventory = player.Inventory.flat();

        let exp = Math.floor(Math.random() * 50) + 200;
        player.CurEXP += exp;
        await sysMsg(player, exp, ...enemy.RewardItems);
    }

    resetRunOBJ();
    await wait(1);

    // 檢查是否所有敵人都已死亡（CurHP <= 0）
    const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
    console.log(`剩餘存活敵人數量: ${aliveEnemies.length}`);

    if (aliveEnemies.length === 0) {
        console.log("所有敵人已死亡，關卡通關！準備前往營地場景");

        // 顯示勝利訊息
        await showVictoryMessage(currentLevel);

        // 開始過渡到營地場景
        await transitionToCampScene(currentLevel);

        return;
    }

    // 如果還有敵人存活，繼續正常的遊戲流程
    if (allPlayersMoved()) {
        runOBJ["當前行動方"] = "Enemys";
        await wait(0.5);
        EnemysAction();
        return;
    }
}


// 顯示勝利訊息（全局函數）
window.showVictoryMessage = async function showVictoryMessage(levelIndex) {
    console.log("顯示勝利訊息");

    // 創建勝利訊息覆蓋層
    const victoryOverlay = document.createElement('div');
    victoryOverlay.id = 'victoryOverlay';
    victoryOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        color: white;
        font-family: Arial, sans-serif;
    `;

    const victoryMessage = controlLayer[levelIndex]?.["勝利訊息"] || "關卡通關！";
    const achievementMessage = controlLayer[levelIndex]?.["成就訊息"] || "獲得新成就！";

    victoryOverlay.innerHTML = `
        <div style="text-align: center; animation: fadeIn 1s ease-in;">
            <h1 style="font-size: 48px; margin: 20px 0; color: #FFD700;">🎉 勝利！</h1>
            <h2 style="font-size: 24px; margin: 20px 0;">${victoryMessage}</h2>
            <p style="font-size: 18px; margin: 20px 0; color: #87CEEB;">${achievementMessage}</p>
            <p style="font-size: 16px; margin: 20px 0; color: #DDD;">準備前往營地...</p>
        </div>
        <style>
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        </style>
    `;

    document.body.appendChild(victoryOverlay);

    // 顯示 3 秒
    await wait(3);

    // 移除勝利訊息
    if (victoryOverlay.parentNode) {
        victoryOverlay.parentNode.removeChild(victoryOverlay);
    }

    console.log("勝利訊息顯示完成");
};

// 過渡到營地場景（全局函數）
window.transitionToCampScene = async function transitionToCampScene(levelIndex) {
    console.log("開始過渡到營地場景");

    // 檢查是否有結束影片需要播放
    const currentLevelData = controlLayer[levelIndex];
    if (currentLevelData.endhasmovie && currentLevelData.endhasmovie !== null) {
        console.log("檢測到關卡結束影片，準備播放");

        // 播放結束影片，影片結束後繼續過渡流程
        return new Promise((resolve) => {
            Game.playLevelEndMovie(currentLevelData.endhasmovie, () => {
                // 影片播放完畢後繼續原來的過渡流程
                continueTransitionToCamp(levelIndex).then(resolve);
            });
        });
    } else {
        // 沒有結束影片，直接執行原來的過渡流程
        return continueTransitionToCamp(levelIndex);
    }
};

// 繼續過渡到營地的流程（分離出來以便影片播放後調用）
async function continueTransitionToCamp(levelIndex) {
    console.log("繼續過渡到營地場景流程");

    // 創建黑色過渡畫面
    const transitionOverlay = document.createElement('div');
    transitionOverlay.id = 'transitionOverlay';
    transitionOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: black;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10001;
        color: white;
        font-family: Arial, sans-serif;
    `;

    transitionOverlay.innerHTML = `
        <div style="text-align: center;">
            <div style="font-size: 24px; margin: 20px 0;">載入營地場景中...</div>
            <div style="width: 200px; height: 4px; background: #333; border-radius: 2px; margin: 20px auto;">
                <div id="loadingBar" style="width: 0%; height: 100%; background: #4CAF50; border-radius: 2px; transition: width 0.3s ease;"></div>
            </div>
            <div id="loadingText" style="font-size: 16px; color: #AAA;">正在初始化...</div>
        </div>
    `;

    document.body.appendChild(transitionOverlay);

    // 模擬載入進度
    const loadingBar = document.getElementById('loadingBar');
    const loadingText = document.getElementById('loadingText');

    const loadingSteps = [
        { progress: 20, text: "保存遊戲進度..." },
        { progress: 40, text: "載入營地資源..." },
        { progress: 60, text: "初始化角色系統..." },
        { progress: 80, text: "預載入圖片資源..." },
        { progress: 100, text: "準備完成！" }
    ];

    // 觸發關卡通關事件（保存資料）
    const levelCompleteEvent = new CustomEvent('levelComplete', {
        detail: {
            levelIndex: levelIndex,
            completionTime: Date.now(),
            victoryMessage: controlLayer[levelIndex]?.["勝利訊息"],
            achievementMessage: controlLayer[levelIndex]?.["成就訊息"]
        }
    });
    document.dispatchEvent(levelCompleteEvent);

    // 逐步更新載入進度
    for (const step of loadingSteps) {
        if (loadingBar && loadingText) {
            loadingBar.style.width = step.progress + '%';
            loadingText.textContent = step.text;
        }
        await wait(0.8); // 每步等待 0.8 秒
    }

    // 實際切換到營地場景
    console.log("開始切換到營地場景...");
    if (typeof sceneManager !== 'undefined' && typeof sceneManager.switchToCamp === 'function') {
        sceneManager.switchToCamp(levelIndex);
        console.log("營地場景切換已觸發");
    } else {
        console.error("sceneManager 或 switchToCamp 方法不可用");
    }

    // 等待營地場景完全載入
    await waitForCampSceneLoaded();

    // 移除過渡畫面
    if (transitionOverlay.parentNode) {
        transitionOverlay.style.opacity = '0';
        transitionOverlay.style.transition = 'opacity 0.5s ease';

        setTimeout(() => {
            if (transitionOverlay.parentNode) {
                transitionOverlay.parentNode.removeChild(transitionOverlay);
            }
        }, 500);
    }

    console.log("營地場景載入完成");
}

// 等待營地場景完全載入（全局函數）
window.waitForCampSceneLoaded = async function waitForCampSceneLoaded() {
    console.log("等待營地場景載入...");

    let attempts = 0;
    const maxAttempts = 15; // 最多等待 7.5 秒

    while (attempts < maxAttempts) {
        attempts++;

        // 檢查場景管理器基本狀態
        if (typeof sceneManager !== 'undefined') {
            const sceneExists = sceneManager.currentScene ? '存在' : '不存在';
            const isLoaded = sceneManager.currentScene?.isLoaded ? '已載入' : '未載入';

            console.log(`載入檢查 ${attempts}/${maxAttempts}: sceneType=${sceneManager.sceneType}, currentScene=${sceneExists}, isLoaded=${isLoaded}`);

            // 基本檢查：場景類型為營地且有當前場景
            if (sceneManager.sceneType === 'camp' && sceneManager.currentScene) {

                // 進階檢查：場景是否已載入
                if (sceneManager.currentScene.isLoaded === true) {
                    console.log("✓ 營地場景已完全載入（完整檢查通過）");
                    return;
                }

                // 中等檢查：場景存在且運行中
                if (sceneManager.currentScene.isRunning === true && attempts >= 5) {
                    console.log("✓ 營地場景運行中（中等檢查通過）");
                    return;
                }

                // 寬鬆檢查：場景存在且有角色
                if (sceneManager.currentScene.character && attempts >= 8) {
                    console.log("✓ 營地場景基本載入完成（寬鬆檢查通過）");
                    return;
                }

                // 最寬鬆檢查：場景存在
                if (attempts >= 12) {
                    console.log("✓ 營地場景存在（最寬鬆檢查通過）");
                    return;
                }
            }
        } else {
            console.log(`載入檢查 ${attempts}/${maxAttempts}: sceneManager 不存在`);
        }

        await wait(0.5); // 每 0.5 秒檢查一次
    }

    console.log("⚠ 營地場景載入超時，但繼續進行（這是正常的，不影響遊戲功能）");
    console.log("💡 提示：營地場景可能需要更多時間載入，但遊戲會正常運行");
}

//異步結束遊戲
async function gameover(player, playerCanvas) {

    clearPlayerAnimation(playerCanvas)
    await wait(2);
    // 隱藏戰鬥畫面
    Dom.BattleScreen.style.display = "none";
    Dom.BattleScreen.style.animation = "none";
    Dom.BattleScreen.innerHTML = "";
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    updateMapObjects();

    // 重新渲染canvas
    render();

    resetRunOBJ();

    await wait(1);
    // 停止背景音樂
    if (window.bgmAudio) {
        window.bgmAudio.pause();
        window.bgmAudio.currentTime = 0;
    }

    // 播放gameover音效
    const gameoverAudio = operates.playSound("./Public/gameover.wav");
    if (!gameoverAudio) {
        console.warn("播放遊戲結束音效失敗");
    }

    // 創建並顯示gameover.gif
    let gameoverImg = document.createElement("img");
    gameoverImg.src = "./Public/gameover.gif";
    gameoverImg.style.position = "fixed";
    gameoverImg.style.top = "0";
    gameoverImg.style.left = "0";
    gameoverImg.style.width = "100%";
    gameoverImg.style.height = "100%";
    gameoverImg.style.zIndex = "9999";
    gameoverImg.style.objectFit = "cover";
    document.body.appendChild(gameoverImg);

    // 10秒後重新載入頁面
    setTimeout(() => {
        window.location.reload();
    }, 10000);
    return;
}

//回到地圖(尚未戰鬥)
async function returnToMap(player) {
    await wait(4);
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";
    Dom.BattleScreen.style.display = "none";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    setTimeout(() => {
        Dom.BattleScreen.innerHTML = "";
    })
    operates.MoveComera(player.Position);

    resetRunOBJ();

    // 檢查是否有寶箱
    let hasgetitem = false;
    for (const [index, treasure] of gametreasures.entries()) {
        if (treasure["位置"] === player.Position) {
            console.log(`玩家 ${player.name} 拾取寶箱: ${treasure["寶物"].name} 在位置 ${treasure["位置"]}`);

            player.Inventory.push(treasure["寶物"])

            // 顯示獲得物品訊息
            await showyouget(player, treasure["寶物"])

            // 記錄寶箱已被拿取
            if (typeof window.takenTreasures === 'undefined') {
                window.takenTreasures = new Set();
            }
            window.takenTreasures.add(treasure["位置"]);
            console.log(`記錄寶箱已拿取: 位置 ${treasure["位置"]}`);

            // 從遊戲數據中移除寶箱
            gametreasures.splice(index, 1);
            hasgetitem = true;

            // 更新canvas中的mapObjects以移除寶箱
            console.log("更新mapObjects以移除拾取的寶箱");
            updateMapObjects();

            // 重新渲染canvas
            render();

            console.log("寶箱拾取完成，canvas已重新渲染");
            break;
        }
    }

    // 檢查是否所有玩家都已經移動
    if (allPlayersMoved()) {
        runOBJ["當前行動方"] = "Enemys";
        await wait(1);
        EnemysAction();
        return;
    }
}

//回到地圖(戰鬥結束)(經驗、升級系統)**
async function BattleReturnToMap(player) {
    await wait(4);
    Dom.GameBoard.style.pointerEvents = "auto";
    Dom.GameBoard.style.display = "grid";
    Dom.BattleScreen.style.display = "none";
    Dom.BattleScreen.style.animation = "none";

    // 確保遊戲canvas正確顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'block';
        gameCanvas.style.visibility = 'visible';
    }

    setTimeout(() => {
        Dom.BattleScreen.innerHTML = "";
    })
    operates.MoveComera(player.Position);

    resetRunOBJ();

    if (player["是否電腦操作"] === false) {
        let exp = Math.floor(Math.random() * 50) + 200;
        player.CurEXP += exp;
        await sysMsg(player, exp, null);

        // 檢查是否有寶箱
        let hasgetitem = false;
        for (const [index, treasure] of gametreasures.entries()) {
            if (treasure["位置"] === player.Position) {
                console.log(`玩家 ${player.name} 拾取寶箱: ${treasure["寶物"].name} 在位置 ${treasure["位置"]}`);

                player.Inventory.push(treasure["寶物"])

                // 追蹤物品獲得（用於成就系統）
                if (!window.achievementTracker) {
                    window.achievementTracker = {
                        magicAttackCount: 0,
                        itemsCollected: new Set()
                    };
                }
                window.achievementTracker.itemsCollected.add(treasure["寶物"].name);
                console.log(`物品獲得追蹤: ${treasure["寶物"].name}`);

                // 更新成就進度條（如果設定視窗開啟）
                if (typeof Game !== 'undefined' && Game.updateAchievementProgress) {
                    Game.updateAchievementProgress();
                }

                // 顯示獲得物品訊息
                await showyouget(player, treasure["寶物"])

                // 記錄寶箱已被拿取
                if (typeof window.takenTreasures === 'undefined') {
                    window.takenTreasures = new Set();
                }
                window.takenTreasures.add(treasure["位置"]);
                console.log(`記錄寶箱已拿取: 位置 ${treasure["位置"]}`);

                // 從遊戲數據中移除寶箱
                gametreasures.splice(index, 1);
                hasgetitem = true;

                // 更新canvas中的mapObjects以移除寶箱
                console.log("更新mapObjects以移除拾取的寶箱");
                updateMapObjects();

                // 重新渲染canvas
                render();

                console.log("寶箱拾取完成，canvas已重新渲染");
                break;
            }
        }
    }

    // 檢查是否所有玩家都已經移動
    if (allPlayersMoved()) {
        runOBJ["當前行動方"] = "Enemys";
        await wait(1);
        EnemysAction();
        return;
    }
}

//玩家獲得經驗值訊息框
async function sysMsg(player, exptext, item) {

    let itemtext = item?.name

    let msgcompleted = false;
    let sysmsg = `<dialog id="sysmsgid">
                    <div class="sysmsg-avatar" style="background-image:url(${player.avatar});"></div>
                    <div class="sysmsg-container">        
                        <div class="sysmsg-textcontent">獲得 ${exptext} 歷練值${item !== null ? ` 和獲得「` + itemtext + `」` : ""}</div>
                    </div>
                  </dialog>`;

    Dom.InfoDialog.innerHTML = sysmsg;
    document.getElementById("sysmsgid").showModal();
    await wait(4);
    document.getElementById("sysmsgid").close();
    Dom.InfoDialog.innerHTML = "";
    let addlevel = Math.floor(player.CurEXP / 500);
    let leftexp = player.CurEXP % 500;
    player.CurEXP = leftexp;

    let Position = player.Position;

    if (addlevel > 0) {
        await Lvupanimation(player);
        await wait(1)
        let { LVupcomplete } = await LVupMsg(player, addlevel)
        if (LVupcomplete) {

            await wait(1)
            //Game.changeLevel(1)
            return { msgcompleted: true }
        }
    }
    await wait(0.1)
    return { msgcompleted: true }
}

//玩家升級訊息框(後執行，控制升級各項數值)
async function LVupMsg(player, addlevel) {
    let LVupcomplete = false

    let addhp = 8;
    let addmp = 5;
    let addatk = 5;
    let adddef = 3;
    let addhit = 1;
    let addavoid = 0.5;
    let addmove = 0.05;

    let newHP = player.HP + addhp * addlevel
    let newMP = player.MP + addmp * addlevel
    let newATK = player.ATK + addatk * addlevel
    let newDEF = player.DEF + adddef * addlevel
    let newHitRate = player.HitRate + addhit * addlevel
    let newAvoidRate = Number((player.AvoidRate + addavoid * addlevel).toFixed(2))
    let newMove = Number((player.Move + addmove * addlevel).toFixed(2))
    let newlevel = player.level + addlevel

    let newpoint = player["點數"] + addlevel

    // 檢查是否可以學習新技能
    let newSkills = [];
    MagicDB.forEach(skill => {
        // 檢查等級要求
        if (skill.NeedLV <= newlevel &&
            skill.Who.includes(player.name) &&  // 檢查角色是否可以學習此技能
            !player["法術"].some(existingSkill =>
                existingSkill.name === skill.name &&
                existingSkill.type === skill.type &&
                existingSkill.Classify === skill.Classify &&
                existingSkill.NeedMove === skill.NeedMove &&
                existingSkill.Range === skill.Range &&
                existingSkill.NeedMP === skill.NeedMP &&
                JSON.stringify(existingSkill.NeedSoul) === JSON.stringify(skill.NeedSoul)
            )) {

            // 檢查五內要求
            let canLearn = true;
            for (let soul in skill.NeedSoul) {
                if (player["五內"][soul] < skill.NeedSoul[soul]) {
                    canLearn = false;
                    break;
                }
            }

            if (canLearn) {
                newSkills.push(skill);
            }
        }
    });

    let skillListHTML = '';
    if (newSkills.length > 0) {
        skillListHTML = `
            <div class="skill-list">
                ${newSkills.map(skill => `
                    <div class="skill-item">
                        <div class="skill-name">技、${skill.name}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    let sysmsg = `<dialog id="lvupmsgid">
                        <div class="LVup-container">   
                            <div class="LVup-ability"> 
                                <div id="lvuptit" style="font-size:28px;margin-bottom: 15px;">位階：${player.level} ➞ ${newlevel}</div>  
                                <div class="LVup-ability-item">🩸 生命： ${player.HP} ➞ ${newHP}</div> 
                                <div class="LVup-ability-item">💧 法力： ${player.MP} ➞ ${newMP}</div> 
                                <div class="LVup-ability-item">🗡️攻擊： ${player.ATK} ➞ ${newATK}</div> 
                                <div class="LVup-ability-item">🔰 防禦： ${player.DEF} ➞ ${newDEF}</div> 
                                <div class="LVup-ability-item">🎯命中：${player.HitRate} ➞ ${newHitRate}</div> 
                                <div class="LVup-ability-item">⚡閃避： ${player.AvoidRate} ➞ ${newAvoidRate}</div> 
                                <div class="LVup-ability-item">👣移動： ${player.Move} ➞ ${newMove}</div>
                            </div> 
                            <div class="LVup-skills">   
                                ${skillListHTML}
                            </div>
                        </div>     
                  </dialog>`;

    Dom.InfoDialog.innerHTML = sysmsg;
    document.getElementById("lvupmsgid").showModal();

    LVupcomplete = true

    await wait(5);
    document.getElementById("lvupmsgid").close();
    Dom.InfoDialog.innerHTML = "";

    // 更新玩家數值
    if (LVupcomplete === true) {
        player.HP = newHP;
        player.MP = newMP;
        player.ATK = newATK;
        player.DEF = newDEF;
        player.HitRate = newHitRate;
        player.AvoidRate = newAvoidRate;
        player.Move = newMove;
        player.level = newlevel;
        player.點數 = newpoint;

        // 學習新技能
        if (newSkills.length > 0) {
            newSkills.forEach(skill => {
                player["法術"].push(skill);
            });
        }

        // 恢復玩家站立動作
        await restorePlayerStandAnimation(player);
    }
    return { LVupcomplete }
}

// 恢復玩家站立動作的函數
async function restorePlayerStandAnimation(player) {
    try {
        console.log(`恢復玩家 ${player.name} 的站立動作`);

        const playerIndex = gameplayers.findIndex(p => p === player);
        if (playerIndex === -1) {
            console.error("找不到玩家索引");
            return;
        }

        // 找到玩家物件
        const playerObj = mapObjects.find(obj =>
            obj.type === 'player' && obj.playerIndex === playerIndex
        );

        if (!playerObj) {
            console.error(`找不到玩家 ${player.name} 的canvas物件`);
            return;
        }

        // 確定玩家面向方向
        const direction = player.lastdirect || 'down';
        console.log(`玩家 ${player.name} 面向方向: ${direction}`);

        // 獲取站立動畫圖片
        let standImages = null;
        if (player.Stand && player.Stand[direction]) {
            standImages = Array.isArray(player.Stand[direction]) ?
                player.Stand[direction] : [player.Stand[direction]];
        } else {
            console.warn(`玩家 ${player.name} 沒有 ${direction} 方向的站立動畫，使用down方向`);
            if (player.Stand && player.Stand.down) {
                standImages = Array.isArray(player.Stand.down) ?
                    player.Stand.down : [player.Stand.down];
            }
        }

        if (!standImages || standImages.length === 0) {
            console.error(`玩家 ${player.name} 沒有可用的站立動畫圖片`);
            return;
        }

        // 載入第一幀站立圖片
        const firstStandImg = await preloadImage(standImages[0]);

        // 停止任何現有的動畫
        if (playerObj.isStandAnimating) {
            playerObj.isStandAnimating = false;
            if (playerObj.animationId) {
                cancelAnimationFrame(playerObj.animationId);
                playerObj.animationId = null;
            }
        }

        // 更新玩家物件
        playerObj.img = firstStandImg;
        playerObj.standImages = standImages;
        playerObj.currentFrameIndex = 0;
        playerObj.lastFrameTime = performance.now();

        // 如果有多幀動畫，啟動站立動畫
        if (standImages.length > 1) {
            console.log(`啟動玩家 ${player.name} 的站立動畫，共 ${standImages.length} 幀`);
            startStandAnimation(playerObj);
        } else {
            console.log(`玩家 ${player.name} 只有一幀站立圖片，不需要動畫`);
        }

        // 重新渲染
        render();

        console.log(`玩家 ${player.name} 站立動作恢復完成`);

    } catch (error) {
        console.error(`恢復玩家 ${player.name} 站立動作時發生錯誤:`, error);
    }
}

//玩家升級動畫(先執行) - Canvas版本
async function Lvupanimation(player) {
    console.log(`開始播放玩家 ${player.name} 的升級動畫`);

    const Position = player.Position;
    const playerIndex = gameplayers.findIndex(p => p === player);

    if (playerIndex === -1) {
        console.error("找不到玩家索引");
        return;
    }

    // 播放升級特效音效
    const lvupeffsound = operates.playSound("./Public/lvup.mp3");
    if (!lvupeffsound) {
        console.warn("播放升級特效音效失敗");
    }

    // 播放升級音效
    if (player.lvup.sound) {
        const manlvupaudio = operates.playSound(player.lvup.sound);
        if (!manlvupaudio) {
            console.warn("播放升級音效失敗:", player.lvup.sound);
        }
    }

    // 同時播放玩家升級動畫和特效動畫
    const [playerAnimationPromise, effectAnimationPromise] = await Promise.allSettled([
        playPlayerLvupAnimation(player, playerIndex),
        playLvupEffectAnimation(Position)
    ]);

    if (playerAnimationPromise.status === 'rejected') {
        console.error("玩家升級動畫播放失敗:", playerAnimationPromise.reason);
    }

    if (effectAnimationPromise.status === 'rejected') {
        console.error("升級特效動畫播放失敗:", effectAnimationPromise.reason);
    }

    console.log(`玩家 ${player.name} 升級動畫播放完成`);
}

// 播放玩家升級動畫
async function playPlayerLvupAnimation(player, playerIndex) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log(`播放玩家 ${player.name} 升級動畫`);

            // 檢查是否有升級動畫圖片
            if (!player.lvup.animates || !Array.isArray(player.lvup.animates) || player.lvup.animates.length === 0) {
                console.warn(`玩家 ${player.name} 沒有升級動畫圖片`);
                resolve();
                return;
            }

            // 找到玩家物件
            const playerObj = mapObjects.find(obj =>
                obj.type === 'player' && obj.playerIndex === playerIndex
            );

            if (!playerObj) {
                console.error(`找不到玩家 ${player.name} 的canvas物件`);
                reject(new Error("找不到玩家canvas物件"));
                return;
            }

            // 停止站立動畫
            if (playerObj.isStandAnimating) {
                playerObj.isStandAnimating = false;
                if (playerObj.animationId) {
                    cancelAnimationFrame(playerObj.animationId);
                    playerObj.animationId = null;
                }
            }

            // 預載入所有升級動畫圖片
            const lvupImages = [];
            for (const imgSrc of player.lvup.animates) {
                try {
                    const img = await preloadImage(imgSrc);
                    lvupImages.push(img);
                } catch (error) {
                    console.warn(`載入升級動畫圖片失敗: ${imgSrc}`, error);
                }
            }

            if (lvupImages.length === 0) {
                console.warn("沒有成功載入任何升級動畫圖片");
                resolve();
                return;
            }

            // 播放升級動畫
            let currentFrame = 0;
            const frameInterval = 150; // 每幀150ms
            let lastFrameTime = performance.now();

            function animateLvupFrame() {
                const currentTime = performance.now();

                if (currentTime - lastFrameTime >= frameInterval) {
                    if (currentFrame < lvupImages.length) {
                        // 更新玩家圖片為升級動畫幀
                        playerObj.img = lvupImages[currentFrame];
                        render();

                        currentFrame++;
                        lastFrameTime = currentTime;

                    } else {
                        resolve();
                        return;
                    }
                }

                // 繼續動畫循環
                requestAnimationFrame(animateLvupFrame);
            }

            // 開始播放動畫
            requestAnimationFrame(animateLvupFrame);

        } catch (error) {
            console.error("播放玩家升級動畫時發生錯誤:", error);
            reject(error);
        }
    });
}

// 播放升級特效動畫
async function playLvupEffectAnimation(position) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log(`播放升級特效動畫在位置 ${position}`);

            // 載入升級特效圖片陣列
            const effectImages = [];
            const effectPath = './Public/lvupeff/';

            // 嘗試載入特效圖片 (假設有0.png到9.png)
            for (let i = 0; i < 10; i++) {
                try {
                    const imgSrc = `${effectPath}${i}.png`;
                    const img = await preloadImage(imgSrc);
                    effectImages.push(img);
                } catch (error) {
                    // 如果載入失敗，停止嘗試載入更多圖片
                    console.log(`載入特效圖片 ${i}.png 失敗，停止載入更多特效圖片`);
                    break;
                }
            }

            if (effectImages.length === 0) {
                console.warn("沒有成功載入任何升級特效圖片");
                resolve();
                return;
            }

            console.log(`成功載入 ${effectImages.length} 張升級特效圖片`);

            // 計算特效顯示位置
            const gridX = position % controlLayer[currentLevel].size.cols;
            const gridY = Math.floor(position / controlLayer[currentLevel].size.cols);

            // 創建特效物件
            const effectObj = {
                type: 'lvupEffect',
                gridX: gridX,
                gridY: gridY + 1,
                img: effectImages[0],
                width: 320,
                height: 250,
                name: '升級特效',
                zIndex: gridY + 0.5, // 在角色上方
                // 特效動畫屬性
                effectImages: effectImages,
                currentFrameIndex: 0,
                frameInterval: 120, // 每幀120ms
                lastFrameTime: performance.now(),
                isEffectAnimating: true,
                animationId: null
            };

            // 添加到mapObjects
            mapObjects.push(effectObj);

            // 播放特效動畫
            let currentFrame = 0;
            const frameInterval = 120; // 每幀120ms
            let lastFrameTime = performance.now();

            function animateEffectFrame() {
                const currentTime = performance.now();

                if (currentTime - lastFrameTime >= frameInterval) {
                    if (currentFrame < effectImages.length) {
                        // 更新特效圖片
                        effectObj.img = effectImages[currentFrame];
                        render();

                        currentFrame++;
                        lastFrameTime = currentTime;
                    } else {
                        // 特效動畫播放完成，移除特效物件
                        console.log("升級特效動畫播放完成");

                        const effectIndex = mapObjects.findIndex(obj => obj === effectObj);
                        if (effectIndex !== -1) {
                            mapObjects.splice(effectIndex, 1);
                            render();
                        }

                        resolve();
                        return;
                    }
                }

                // 繼續動畫循環
                requestAnimationFrame(animateEffectFrame);
            }

            // 開始播放特效動畫
            requestAnimationFrame(animateEffectFrame);

        } catch (error) {
            console.error("播放升級特效動畫時發生錯誤:", error);
            reject(error);
        }
    });
}

async function showyouget(player, treasure) {
    let itemtext = treasure?.name


    let sysmsg = `<dialog id="sysmsgid">
                    <div class="sysmsg-avatar" style="background-image:url(${player.avatar});"></div>
                    <div class="sysmsg-container">        
                        <div class="sysmsg-textcontent">開啟寶箱，得到「 ${itemtext} 」</div>
                    </div>
                  </dialog>`;

    Dom.InfoDialog.innerHTML = sysmsg;
    document.getElementById("sysmsgid").showModal();
    await wait(3)
    document.getElementById("sysmsgid").close();
    Dom.InfoDialog.innerHTML = "";


    return { hasgetitem: true }
}

function getDirection(rowCurrent, colCurrent, rowNext, colNext) {
    if (rowNext < rowCurrent) {
        return 0; // up
    } else if (colNext > colCurrent) {
        return 1; // right
    } else if (rowNext > rowCurrent) {
        return 2; // down
    } else if (colNext < colCurrent) {
        return 3; // left
    }
    return -1; // invalid direction
}

// 動畫管理函數 - 從 Init.js 複製過來以確保可用性
function stopStandAnimation(characterObj) {
    if (characterObj) {
        characterObj.isStandAnimating = false;
        if (characterObj.animationId) {
            cancelAnimationFrame(characterObj.animationId);
            characterObj.animationId = null;
        }
    }
}

function resumeStandAnimation(characterObj) {
    if (characterObj && characterObj.standImages && characterObj.standImages.length > 1) {
        // 重置到第一幀
        characterObj.currentFrameIndex = 0;

        // 確保使用正確方向的站立圖片
        if (characterObj.type === 'player') {
            let player = gameplayers[characterObj.playerIndex];
            if (player && player.lastdirect) {
                let correctStandImages = player.Stand && player.Stand[player.lastdirect];
                if (correctStandImages && correctStandImages.length > 0) {
                    characterObj.standImages = correctStandImages;
                    console.log(`重新啟動玩家 ${player.name} 的 ${player.lastdirect} 方向站立動畫`);
                }
            }
        }

        startStandAnimation(characterObj);
    }
}

// 注意：startStandAnimation 函數已移至 Init.js 中，避免重複定義

// 讓玩家和敵人互相轉向對方
async function turnCharactersToFaceEachOther(player, enemy, playerIndex, enemyIndex) {
    console.log(`讓 ${player.name} 和 ${enemy.name} 互相轉向對方`);

    // 計算玩家和敵人的位置
    const playerRow = Math.floor(player.Position / controlLayer[currentLevel].size.cols);
    const playerCol = player.Position % controlLayer[currentLevel].size.cols;
    const enemyRow = Math.floor(enemy.Position / controlLayer[currentLevel].size.cols);
    const enemyCol = enemy.Position % controlLayer[currentLevel].size.cols;

    // 計算玩家應該面向敵人的方向
    const playerDirection = calculateFaceDirection(playerRow, playerCol, enemyRow, enemyCol);
    // 計算敵人應該面向玩家的方向
    const enemyDirection = calculateFaceDirection(enemyRow, enemyCol, playerRow, playerCol);

    console.log(`玩家 ${player.name} 轉向: ${playerDirection}, 敵人 ${enemy.name} 轉向: ${enemyDirection}`);

    // 更新玩家朝向
    if (playerDirection && player.Stand && player.Stand[playerDirection]) {
        player.lastdirect = playerDirection;
        await updateCharacterDirection(player, playerIndex, playerDirection, 'player');
    }

    // 更新敵人朝向
    if (enemyDirection && enemy.Stand && enemy.Stand[enemyDirection]) {
        enemy.lastdirect = enemyDirection;
        await updateCharacterDirection(enemy, enemyIndex, enemyDirection, 'enemy');
    }

    // 等待一小段時間讓轉向動畫完成
    await wait(0.3);
}

// 計算角色應該面向的方向
function calculateFaceDirection(fromRow, fromCol, toRow, toCol) {
    const rowDiff = toRow - fromRow;
    const colDiff = toCol - fromCol;

    // 優先考慮較大的差值
    if (Math.abs(rowDiff) > Math.abs(colDiff)) {
        return rowDiff > 0 ? 'down' : 'up';
    } else if (Math.abs(colDiff) > Math.abs(rowDiff)) {
        return colDiff > 0 ? 'right' : 'left';
    } else if (rowDiff !== 0) {
        // 如果差值相等，優先考慮垂直方向
        return rowDiff > 0 ? 'down' : 'up';
    } else if (colDiff !== 0) {
        return colDiff > 0 ? 'right' : 'left';
    }

    return 'down'; // 預設方向
}

// 更新角色方向和動畫
async function updateCharacterDirection(character, characterIndex, direction, characterType) {
    console.log(`更新 ${character.name} 的方向為: ${direction}`);

    // 找到對應的角色物件
    let characterObj = mapObjects.find(obj => {
        if (characterType === 'player') {
            return obj.type === 'player' && obj.playerIndex === characterIndex;
        } else {
            return obj.type === 'enemy' && obj.enemyIndex === characterIndex;
        }
    });

    if (characterObj && character.Stand && character.Stand[direction]) {
        // 停止當前的站立動畫
        stopStandAnimation(characterObj);

        // 更新站立圖片陣列為新方向
        characterObj.standImages = character.Stand[direction];
        characterObj.currentFrameIndex = 0;

        // 載入新方向的第一張圖片
        const newImageSrc = character.Stand[direction][0];
        try {
            const newImg = await preloadImage(newImageSrc);
            characterObj.img = newImg;

            // 重新渲染以顯示新方向
            render();

            // 重新啟動站立動畫
            resumeStandAnimation(characterObj);

            console.log(`${character.name} 成功轉向 ${direction}`);
        } catch (error) {
            console.warn(`載入 ${character.name} 的 ${direction} 方向圖片失敗:`, error);
        }
    } else {
        console.warn(`找不到 ${character.name} 的角色物件或 ${direction} 方向的站立圖片`);
    }
}