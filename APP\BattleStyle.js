/*====================================  戰鬥場景動畫樣式控制================================================= */

/**
 * @function GetEffPos      - 可以指定地圖位置添加特效
 * @param {Number} Position - 類型是數字，是角色所在位置
 * @param {String} imgsrc   - 類型是字串，是特效的gif圖片
 * @param {Number} w        - 類型是數字，是特效的顯示範圍
 * @param {Number} time     - 類型是數字，是特效的播放時長
 * @param {String} audiosrc - 類型是字串，是特效聲音的檔案
 * 
 */

async function GetEffPos(Position, imgsrc, w, time, audiosrc) {
    if (typeof Position !== 'number' || typeof w !== 'number' || typeof time !== 'number') {
        console.error('錯誤：Position、w 和 time 必須為數字');
    }
    if (typeof imgsrc !== 'string' || typeof audiosrc !== 'string') {
        console.error('錯誤：imgsrc 和 audiosrc 必須為字串');
    }

    let eff = document.createElement('img');
    eff.src = imgsrc + "?t=" + new Date().getTime();
    eff.style.position = "absolute";
    eff.style.zIndex = 99;
    eff.style.width = w + "px";
    eff.style.transform = "translate(-50%,-50%);"

    let totaltop = document.getElementById(Position).offsetTop - (document.getElementById(Position).offsetHeight / 2);
    let totalleft = document.getElementById(Position).offsetLeft - (document.getElementById(Position).offsetWidth / 2);

    eff.style.top = totaltop + "px";
    eff.style.left = totalleft + "px";
    await wait(0);
    Dom.GameMap.appendChild(eff);

    // 播放特效音效
    const effaudio = operates.playSound(audiosrc + "?t=" + new Date().getTime());
    if (!effaudio) {
        console.warn("播放特效音效失敗:", audiosrc);
    }

    await wait(time);
    Dom.GameMap.removeChild(eff)

}

//異步等待函數(勿刪除！勿刪除！勿刪除！勿刪除！勿刪除！勿刪除！勿刪除！勿刪除！勿刪除！勿刪除！)
function wait(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

// 敵人傷害數字（獨立canvas，層級最高）
function setenemydamagetext(enemydamage) {
    // 將數字轉成中文
    let damageText = damagetochinese(enemydamage);

    // 先移除舊的傷害數字 canvas（避免重疊）
    let oldCanvas = Dom.BattleScreen.querySelector("#enemy-damage-canvas");
    if (oldCanvas) {
        Dom.BattleScreen.removeChild(oldCanvas);
    }

    // 建立獨立的 canvas，並設置最高層級
    let canvas = document.createElement("canvas");
    canvas.id = "enemy-damage-canvas";
    canvas.width = Dom.BattleScreen.offsetWidth - 50;
    canvas.height = Dom.BattleScreen.offsetHeight;
    canvas.style.position = "absolute";
    canvas.style.left = "0";
    canvas.style.top = "0";
    canvas.style.pointerEvents = "none";
    canvas.style.zIndex = "9999"; // 層級最高
    Dom.BattleScreen.appendChild(canvas);

    let ctx = canvas.getContext("2d");

    // 設定動畫參數
    let startTime = null;
    let duration = 2000; // 2秒
    let startY = canvas.height * 0.55;
    let endY = startY - 60; // 飄移距離
    let alphaStart = 1;
    let alphaEnd = 0;

    // 字符彈跳動畫參數
    let charAnimationDuration = 180; // 每個字符彈跳持續時間
    let charDelay = 90; // 字符間的延遲
    let bounceHeight = 10; // 彈跳高度

    // 動畫階段參數
    let fadeStartProgress = 0.7; // 在70%進度後開始淡出

    function animateDamageText(timestamp) {
        if (!startTime) startTime = timestamp;
        let elapsed = timestamp - startTime;
        let progress = Math.min(elapsed / duration, 1);

        // 清除畫布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 計算Y座標
        let currentY = startY + (endY - startY) * progress;

        // 計算透明度
        let alpha = progress < fadeStartProgress
            ? alphaStart
            : alphaStart + (alphaEnd - alphaStart) * ((progress - fadeStartProgress) / (1 - fadeStartProgress));
        ctx.globalAlpha = alpha;

        ctx.font = "800 32px 圓體";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.shadowColor = "white";
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        ctx.fillStyle = "rgb(255, 89, 116)";
        ctx.lineWidth = 2;
        ctx.strokeStyle = "white";

        // 彈跳動畫：每個字元有延遲
        let totalWidth = ctx.measureText(damageText).width;
        let startX = canvas.width * 0.6 - totalWidth / 2;
        for (let i = 0; i < damageText.length; i++) {
            let char = damageText[i];
            let charWidth = ctx.measureText(char).width;
            let charX = startX + ctx.measureText(damageText.slice(0, i)).width + charWidth / 2;

            // 彈跳進度
            let charElapsed = Math.max(0, elapsed - i * charDelay);
            let charProgress = Math.min(charElapsed / charAnimationDuration, 1);
            let bounce = Math.sin(Math.PI * charProgress) * bounceHeight * (1 - progress);

            ctx.fillText(char, charX, currentY - bounce);
        }

        ctx.globalAlpha = 1;

        if (progress < 1) {
            requestAnimationFrame(animateDamageText);
        } else {
            // 動畫結束後自動移除canvas
            if (canvas.parentNode) {
                Dom.BattleScreen.removeChild(canvas);
            }
        }
    }

    requestAnimationFrame(animateDamageText);
}

// 玩家傷害數字（獨立canvas，層級最高）
function setplayerdamagetext(playerdamage) {
    // 將數字轉成中文
    let damageText = damagetochinese(playerdamage);

    // 先移除舊的傷害數字 canvas（避免重疊）
    let oldCanvas = Dom.BattleScreen.querySelector("#player-damage-canvas");
    if (oldCanvas) {
        Dom.BattleScreen.removeChild(oldCanvas);
    }

    // 建立獨立的 canvas，並設置最高層級
    let canvas = document.createElement("canvas");
    canvas.id = "player-damage-canvas";
    canvas.width = Dom.BattleScreen.offsetWidth;
    canvas.height = Dom.BattleScreen.offsetHeight;
    canvas.style.position = "absolute";
    canvas.style.left = "0";
    canvas.style.top = "0";
    canvas.style.pointerEvents = "none";
    canvas.style.zIndex = "9999"; // 層級最高
    Dom.BattleScreen.appendChild(canvas);

    let ctx = canvas.getContext("2d");

    // 設定動畫參數
    let startTime = null;
    let duration = 3000; // 增加到4秒，讓動畫更慢
    let startY = canvas.height * 0.4;
    let endY = startY - 80; // 增加飄移距離到80px
    let alphaStart = 1;
    let alphaEnd = 0;

    // 字符彈跳動畫參數
    let charAnimationDuration = 200; // 增加每個字符彈跳持續時間
    let charDelay = 120; // 增加字符間的延遲
    let bounceHeight = 12; // 彈跳高度

    // 動畫階段參數
    let fadeStartProgress = 0.7; // 在70%進度後開始淡出，讓文字顯示更久

    function animateDamageText(timestamp) {
        if (!startTime) startTime = timestamp;
        let elapsed = timestamp - startTime;
        let progress = Math.min(elapsed / duration, 1);

        // 清除畫布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 計算當前 Y 位置
        let currentY = startY + (endY - startY) * progress;

        // 計算透明度 (在fadeStartProgress之後才開始淡出)
        let currentAlpha;
        if (progress < fadeStartProgress) {
            currentAlpha = 1; // 前面保持完全不透明
        } else {
            let fadeProgress = (progress - fadeStartProgress) / (1 - fadeStartProgress);
            currentAlpha = 1 - fadeProgress;
        }

        // 設定字型與樣式
        ctx.save();
        ctx.font = "800 32px 圓體";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.shadowColor = "white";
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        ctx.fillStyle = "rgb(255, 89, 116)";
        ctx.lineWidth = 2;
        ctx.strokeStyle = "white";

        // 計算文字總寬度來居中顯示
        let totalTextWidth = ctx.measureText(damageText).width;
        let startX = (canvas.width * 0.4) - (totalTextWidth / 2);

        // 逐字符繪製，加上彈跳效果
        for (let i = 0; i < damageText.length; i++) {
            let char = damageText[i];
            let charWidth = ctx.measureText(char).width;

            // 計算此字符的彈跳動畫
            let charAnimationStart = i * charDelay;
            let charAnimationEnd = charAnimationStart + charAnimationDuration;
            let bounceOffset = 0;

            if (elapsed >= charAnimationStart && elapsed <= charAnimationEnd) {
                // 字符正在彈跳
                let charProgress = (elapsed - charAnimationStart) / charAnimationDuration;
                // 使用sin函數創造彈跳效果
                bounceOffset = -Math.sin(charProgress * Math.PI) * bounceHeight;
            }

            // 計算字符位置
            let charX = startX + (charWidth / 2);
            let charY = currentY + bounceOffset;

            // 設定透明度
            ctx.globalAlpha = currentAlpha;

            // 繪製字符
            ctx.strokeText(char, charX, charY);
            ctx.fillText(char, charX, charY);

            // 移動到下一個字符位置
            startX += charWidth;
        }

        ctx.restore();

        if (progress < 1) {
            requestAnimationFrame(animateDamageText);
        } else {
            // 動畫結束後延長清除時間
            setTimeout(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if (canvas.parentNode) {
                    canvas.parentNode.removeChild(canvas);
                }
            }, 500); // 增加到500ms讓用戶能看清楚
        }
    }

    requestAnimationFrame(animateDamageText);
}

//數字轉中文
function damagetochinese(damagenumber) {
    let chinesenumber = "";
    let number = damagenumber.toString();
    for (let i = 0; i < number.length; i++) {
        switch (number[i]) {
            case "1":
                chinesenumber += "一";
                break;
            case "2":
                chinesenumber += "二";
                break;
            case "3":
                chinesenumber += "三";
                break;
            case "4":
                chinesenumber += "四";
                break;
            case "5":
                chinesenumber += "五";
                break;
            case "6":
                chinesenumber += "六";
                break;
            case "7":
                chinesenumber += "七";
                break;
            case "8":
                chinesenumber += "八";
                break;
            case "9":
                chinesenumber += "九";
                break;
            case "0":
                chinesenumber += "〇";
                break;
        }
    }
    return chinesenumber;
}

//戰鬥場景：背景、血條
async function CreateBattleInfo(player, enemy) {
    player.AlreadyMove = true;
    Dom.GameBoard.style.pointerEvents = "none";
    Dom.GameBoard.style.display = "none";

    // 確保遊戲canvas保持顯示
    const gameCanvas = document.getElementById('gameCanvas');
    if (gameCanvas) {
        gameCanvas.style.display = 'none !important';
        gameCanvas.style.visibility = 'hidden';
    }

    Dom.BattleScreen.style.display = "block";
    Dom.BattleScreen.style.backgroundImage = `url(${Level0["戰鬥背景"]})`;

    let playerbattleinfo = `
        <div id="playerbattleinfo">
            <div id="playername">${player.name}</div>
            <div id="playerbattlehpbar">
                <div id="playerbattlehpbar_inner" style="width:${(player.CurHP / player.HP) * 100}%">
                    <div id="playerbattlehp-inner" style="width:${(player.CurHP / player.HP) * 490}px"></div>
                </div>
            </div>
            <div id="playerbattlehptext">${player.CurHP} / ${player.HP}</div>
        </div>
    `;
    Dom.BattleScreen.innerHTML += playerbattleinfo;

    let enemybattleinfo = `
        <div id="enemybattleinfo">
            <div id="enemyname">${enemy.name}</div>
            <div id="enemybattlehpbar">
                <div id="enemybattlehpbar_inner" style="width:${(enemy.CurHP / enemy.HP) * 100}%">
                <div id="enemybattlehp-inner" style="width:${(enemy.CurHP / enemy.HP) * 490}px"></div>
                </div>
            </div>
            <div id="enemybattlehptext">${enemy.CurHP} / ${enemy.HP}</div>
        </div>
    `;
    Dom.BattleScreen.innerHTML += enemybattleinfo;

    // 建立 player canvas
    var playerCanvas = document.createElement('canvas');
    playerCanvas.width = 999;
    playerCanvas.height = 663;
    playerCanvas.style.cssText = `position: absolute; left: 30%; transform: translateX(-30%); z-index: 11;`;
    Dom.BattleScreen.appendChild(playerCanvas);

    // 建立 enemy canvas
    var enemyCanvas = document.createElement('canvas');
    enemyCanvas.width = 999;
    enemyCanvas.height = 663;
    enemyCanvas.style.cssText = `position: absolute; left: 30%; transform: translateX(-30%); z-index: 12;`;
    Dom.BattleScreen.appendChild(enemyCanvas);

    return { playerCanvas, enemyCanvas }
}


// 玩家的暴擊特效（Canvas 版本）- 使用多張PNG播放動畫（獨立canvas）
function CriticalHitEffectOnCanvasForPlayer(parentDom) {
    // 建立獨立canvas
    let effectCanvas = document.createElement('canvas');
    effectCanvas.width = 999;
    effectCanvas.height = 663;
    effectCanvas.style.cssText = `position: absolute; left: 30%; transform: translateX(-30%); z-index: 99; pointer-events: none;`;
    parentDom.appendChild(effectCanvas);

    const ctx = effectCanvas.getContext('2d');
    const frameCount = 10; // 假設有10張圖片
    let frames = [];
    let loaded = 0;
    let opacity = 1;
    let duration = 800; // 動畫持續時間(ms)
    let posX = effectCanvas.width * 0.25;
    let posY = effectCanvas.height * 0.05;
    let width = 900;
    let height = 800 * (effectCanvas.height / 663);
    let startTime = null;
    // 添加濾淨效果 filter
    // 例如：ctx.filter = "brightness(1.8) drop-shadow(0 0 20px #fff700)";
    // 在繪製特效時加入濾鏡
    function drawFrame(timestamp) {
        if (!startTime) startTime = timestamp;
        let elapsed = timestamp - startTime;
        let frameIdx = Math.floor((elapsed / duration) * frameCount);
        if (frameIdx >= frameCount) frameIdx = frameCount - 1;

        ctx.save();
        ctx.globalAlpha = opacity;
        ctx.filter = "brightness(5)";
        ctx.clearRect(posX, posY, width, height);
        ctx.drawImage(frames[frameIdx], posX, posY, width, height);
        ctx.restore();

        if (elapsed < duration) {
            requestAnimationFrame(drawFrame);
        } else {
            // 清除特效並移除canvas
            ctx.clearRect(posX, posY, width, height);
            parentDom.removeChild(effectCanvas);
        }
    }

    // 播放音效
    const audio = operates.playSound("./Public/CriticalHit.mp3");
    if (!audio) {
        console.warn("播放暴擊音效失敗");
    }

    // 載入所有幀
    for (let i = 0; i < frameCount; i++) {
        let img = new Image();
        img.src = `./Public/CriticalHit/${i}.png?t=${new Date().getTime()}`;
        img.onload = () => {
            loaded++;
            if (loaded === frameCount) {
                requestAnimationFrame(drawFrame);
            }
        };
        frames.push(img);
    }

    function drawFrame(timestamp) {
        if (!startTime) startTime = timestamp;
        let elapsed = timestamp - startTime;
        let frameIdx = Math.floor((elapsed / duration) * frameCount);
        if (frameIdx >= frameCount) frameIdx = frameCount - 1;

        ctx.save();
        ctx.globalAlpha = opacity;
        ctx.clearRect(posX, posY, width, height);
        ctx.drawImage(frames[frameIdx], posX, posY, width, height);
        ctx.restore();

        if (elapsed < duration) {
            requestAnimationFrame(drawFrame);
        } else {
            // 清除特效並移除canvas
            ctx.clearRect(posX, posY, width, height);
            parentDom.removeChild(effectCanvas);
        }
    }
    Dom.BattleScreen.style.animation = "criticalhit 0.5s linear forwards";
    setTimeout(() => {
        Dom.BattleScreen.style.animation = null;
    }, 700);
}

// 觸發玩家暴擊特效（需傳入父層Dom，例如Dom.BattleScreen）
function setplayercriticalimg(playerCanvas) {
    // playerCanvas.parentNode 為父層Dom
    setTimeout(() => {
        CriticalHitEffectOnCanvasForPlayer(playerCanvas.parentNode);
    });
}

// 敵人的暴擊特效（Canvas 版本）- 使用多張PNG播放動畫（獨立canvas）
function CriticalHitEffectOnCanvasForEnemy(parentDom) {
    // 建立獨立canvas
    let effectCanvas = document.createElement('canvas');
    effectCanvas.width = 999;
    effectCanvas.height = 663;
    effectCanvas.style.cssText = `position: absolute; left: 30%; transform: translateX(-30%); z-index: 99; pointer-events: none;`;
    parentDom.appendChild(effectCanvas);

    const ctx = effectCanvas.getContext('2d');
    // 濾鏡效果（Canvas 2D context 層級，若支援）
    if (ctx.filter !== undefined) {
        ctx.filter = "brightness(5)";
    }

    const frameCount = 10; // 假設有10張圖片
    let frames = [];
    let loaded = 0;
    let opacity = 1;
    let duration = 800; // 動畫持續時間(ms)
    let posX = effectCanvas.width * 0.02;
    let posY = effectCanvas.height * 0.01 - 100;
    let width = 900;
    let height = 800 * (effectCanvas.height / 663);
    let startTime = null;

    // 播放音效
    const audio = operates.playSound("./Public/CriticalHit.mp3");
    if (!audio) {
        console.warn("播放暴擊音效失敗");
    }

    // 載入所有幀
    for (let i = 0; i < frameCount; i++) {
        let img = new Image();
        img.src = `./Public/CriticalHit/${i}.png?t=${new Date().getTime()}`;
        img.onload = () => {
            loaded++;
            if (loaded === frameCount) {
                requestAnimationFrame(drawFrame);
            }
        };
        frames.push(img);
    }

    function drawFrame(timestamp) {
        if (!startTime) startTime = timestamp;
        let elapsed = timestamp - startTime;
        let frameIdx = Math.floor((elapsed / duration) * frameCount);
        if (frameIdx >= frameCount) frameIdx = frameCount - 1;

        ctx.save();
        ctx.globalAlpha = opacity;
        // 每次繪製前都設置濾鏡，確保效果
        if (ctx.filter !== undefined) {
            //ctx.filter = "brightness(1.2) drop-shadow(0 0 16px #ff0) contrast(1.2)";
            ctx.filter = "brightness(5) ";
        }
        ctx.clearRect(posX, posY, width, height);
        ctx.drawImage(frames[frameIdx], posX, posY, width, height);
        ctx.restore();

        if (elapsed < duration) {
            requestAnimationFrame(drawFrame);
        } else {
            // 清除特效並移除canvas
            ctx.clearRect(posX, posY, width, height);
            parentDom.removeChild(effectCanvas);
        }
    }
    Dom.BattleScreen.style.animation = "criticalhit 0.5s linear forwards";
    setTimeout(() => {
        Dom.BattleScreen.style.animation = null;
    }, 5000);
}

// 觸發敵人暴擊特效（需傳入父層Dom，例如Dom.BattleScreen）
function setenemycriticalimg(enemyCanvas) {
    setTimeout(() => {
        CriticalHitEffectOnCanvasForEnemy(enemyCanvas.parentNode);
    });
}

/*================================================================================================= */
// 玩家動畫控制器
class PlayerAnimationController {
    constructor(player, canvas, enemyCanvas = null) {
        this.player = player;
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.enemyCanvas = enemyCanvas;
        this.currentAnimation = null;
        this.animationId = null;
        this.frameIndex = 0;
        this.isPlaying = false;
        this.soundPlayed = false;
        this.enemyHitTriggered = false; // 新增：確保敵人受傷只觸發一次
        this.originalZIndex = canvas.style.zIndex || '1';
    }

    // 設定敵人畫布參考
    setEnemyCanvas(enemyCanvas) {
        this.enemyCanvas = enemyCanvas;
    }

    // 停止當前動畫
    stopAnimation() {
        if (this.animationId) {
            clearTimeout(this.animationId);
            this.animationId = null;
        }
        this.isPlaying = false;
        this.enemyHitTriggered = false;
    }

    // 播放站姿動畫
    playIdleAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'idle';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.enemyHitTriggered = false;
        this.canvas.style.zIndex = this.originalZIndex;
        this.drawIdleFrame();
    }

    // 播放攻擊動畫
    playAttackAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'attack';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.enemyHitTriggered = false;
        this.canvas.style.zIndex = '9999';
        this.drawAttackFrame();
    }

    // 播放攻擊落空動畫
    playAttackMissAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'attackMiss';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.enemyHitTriggered = false;
        this.canvas.style.zIndex = '9999';
        this.drawAttackMissFrame();
    }

    // 播放受傷動畫
    playBearAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'bear';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.enemyHitTriggered = false;
        this.canvas.style.zIndex = this.originalZIndex;
        this.drawBearFrame();
    }

    // 繪製站姿動畫幀
    drawIdleFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'idle') return;

        let animates = this.player.BattleIdleRes.animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'idle') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.filter = "brightness(1.2)";
            let imgWidth = img.width * 2.9;
            let imgHeight = img.height * 2.9;
            let x = (this.canvas.width - imgWidth) / 3 + this.player.BattleIdleRes.x;
            let y = (this.canvas.height - imgHeight) / 4 + this.player.BattleIdleRes.y;
            this.ctx.drawImage(img, x, y, imgWidth, imgHeight);

            this.frameIndex = (this.frameIndex + 1) % animates.length;

            this.animationId = setTimeout(() => {
                this.drawIdleFrame();
            }, 150);
        };
    }

    // 繪製攻擊動畫幀
    drawAttackFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'attack') return;

        let animates = this.player.AtkRes.animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'attack') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.save();
            this.ctx.filter = "brightness(1.3)";
            let imgWidth = img.width * 3;
            let imgHeight = img.height * 2.9;
            let x = (this.canvas.width - imgWidth) / 3 + this.player.AtkRes.x;
            let y = (this.canvas.height - imgHeight) / 4 + this.player.AtkRes.y;
            this.ctx.drawImage(img, x, y, imgWidth, imgHeight);
            this.ctx.restore();

            if (!this.soundPlayed && this.player.AtkRes.sound) {
                const audio = operates.playSound(this.player.AtkRes.sound);
                if (audio) {
                    this.soundPlayed = true;
                } else {
                    console.warn("播放玩家攻擊音效失敗:", this.player.AtkRes.sound);
                }
            }

            // 在攻擊動畫進行到約一半時觸發敵人受傷動畫
            let hitFrame = Math.floor(animates.length * this.player.hitframe); // 攻擊動畫的中間幀
            if (this.frameIndex >= hitFrame && !this.enemyHitTriggered) {
                this.enemyHitTriggered = true;
                // 觸發敵人受傷動畫
                if (this.enemyCanvas && this.enemyCanvas.animationController) {
                    this.enemyCanvas.animationController.playBearAnimation();
                }
            }

            this.frameIndex++;

            if (this.frameIndex < animates.length) {
                this.animationId = setTimeout(() => {
                    this.drawAttackFrame();
                }, 120);
            } else {
                // 攻擊動畫結束
                this.canvas.style.zIndex = this.originalZIndex;

                // 等待一小段時間後，讓敵人恢復站姿動畫
                this.animationId = setTimeout(() => {
                    // 讓敵人停止受傷動畫，回到站姿
                    if (this.enemyCanvas && this.enemyCanvas.animationController) {
                        this.enemyCanvas.animationController.playIdleAnimation();
                    }
                    // 玩家也回到站姿
                    this.playIdleAnimation();
                }); // 延遲300ms讓受傷動畫播放一下
            }
        };
    }

    // 繪製攻擊落空動畫幀
    drawAttackMissFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'attackMiss') return;

        let animates = this.player.AtkMiss.animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'attackMiss') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.save();
            this.ctx.filter = "brightness(1.5)";
            let imgWidth = img.width * 3;
            let imgHeight = img.height * 2.9;
            let x = (this.canvas.width - imgWidth) / 3 + this.player.AtkMiss.x;
            let y = (this.canvas.height - imgHeight) / 4 + this.player.AtkMiss.y;
            this.ctx.drawImage(img, x, y, imgWidth, imgHeight);
            this.ctx.restore();

            if (!this.soundPlayed && this.player.AtkMiss.sound) {
                const audio = operates.playSound(this.player.AtkMiss.sound);
                if (audio) {
                    this.soundPlayed = true;
                } else {
                    console.warn("播放玩家攻擊失誤音效失敗:", this.player.AtkMiss.sound);
                }
            }

            this.frameIndex++;

            if (this.frameIndex < animates.length) {
                this.animationId = setTimeout(() => {
                    this.drawAttackMissFrame();
                }, 130);
            } else {
                // 攻擊落空動畫結束，敵人不受傷，直接切換到站姿動畫
                this.canvas.style.zIndex = this.originalZIndex;
                this.animationId = setTimeout(() => {
                    this.playIdleAnimation();
                }, 100);
            }
        };
    }

    // 繪製受傷動畫幀
    drawBearFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'bear') return;

        let animates = this.player["遭受攻擊"].animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'bear') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.save();
            this.ctx.filter = "brightness(1.3) hue-rotate(0deg) saturate(1.5)";
            let imgWidth = img.width * 2.9;
            let imgHeight = img.height * 2.9;
            let x = (this.canvas.width - imgWidth) / 3 + this.player["遭受攻擊"].x;
            let y = (this.canvas.height - imgHeight) / 4 + this.player["遭受攻擊"].y;

            let shakeX = this.frameIndex % 2 === 0 ? Math.random() * 4 - 2 : 0;
            let shakeY = this.frameIndex % 2 === 0 ? Math.random() * 4 - 2 : 0;

            this.ctx.drawImage(img, x + shakeX, y + shakeY, imgWidth, imgHeight);
            this.ctx.restore();

            this.frameIndex++;

            if (this.frameIndex >= animates.length) {
                this.frameIndex = 0;
            }

            this.animationId = setTimeout(() => {
                this.drawBearFrame();
            }, 100);
        };
    }

    // 清理資源
    destroy() {
        this.stopAnimation();
        this.canvas.style.zIndex = this.originalZIndex;
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
}

// 敵人動畫控制器
class EnemyAnimationController {
    constructor(enemy, canvas, playerCanvas = null) {
        this.enemy = enemy;
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.playerCanvas = playerCanvas;
        this.currentAnimation = null;
        this.animationId = null;
        this.frameIndex = 0;
        this.isPlaying = false;
        this.soundPlayed = false;
        this.playerHitTriggered = false; // 新增：確保玩家受傷只觸發一次
    }

    // 設定玩家畫布參考
    setPlayerCanvas(playerCanvas) {
        this.playerCanvas = playerCanvas;
    }

    // 停止當前動畫
    stopAnimation() {
        if (this.animationId) {
            clearTimeout(this.animationId);
            this.animationId = null;
        }
        this.isPlaying = false;
        this.playerHitTriggered = false;
    }

    // 播放站姿動畫
    playIdleAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'idle';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.playerHitTriggered = false;
        this.drawIdleFrame();
    }

    // 播放攻擊動畫
    playAttackAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'attack';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.playerHitTriggered = false;
        this.drawAttackFrame();
    }

    // 播放攻擊落空動畫
    playAttackMissAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'attackMiss';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.playerHitTriggered = false;
        this.drawAttackMissFrame();
    }

    // 播放受傷動畫
    playBearAnimation() {
        this.stopAnimation();
        this.currentAnimation = 'bear';
        this.frameIndex = 0;
        this.isPlaying = true;
        this.soundPlayed = false;
        this.playerHitTriggered = false;
        this.drawBearFrame();
    }

    // 繪製站姿動畫幀
    drawIdleFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'idle') return;

        let animates = this.enemy.BattleIdleRes.animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'idle') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.filter = "brightness(1.1)";
            let imgWidth = img.width * 2.9;
            let imgHeight = img.height * 2.9;
            let x = (this.canvas.width - imgWidth) / 4 + this.enemy.BattleIdleRes.x;
            let y = (this.canvas.height - imgHeight) / 4 + this.enemy.BattleIdleRes.y;
            this.ctx.drawImage(img, x, y, imgWidth, imgHeight);

            this.frameIndex = (this.frameIndex + 1) % animates.length;

            this.animationId = setTimeout(() => {
                this.drawIdleFrame();
            }, 150);
        };
    }

    // 繪製攻擊動畫幀
    drawAttackFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'attack') return;

        let animates = this.enemy.AtkRes.animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'attack') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.filter = "brightness(1.4)";

            let offsetX = this.enemy.AtkRes.x !== undefined ? this.enemy.AtkRes.x : (this.canvas.width - img.width * 2.9) / 4 + this.enemy.BattleIdleRes.x;
            let offsetY = this.enemy.AtkRes.y !== undefined ? this.enemy.AtkRes.y : (this.canvas.height - img.height * 2.9) / 4 + this.enemy.BattleIdleRes.y;
            let imgWidth = img.width * 2.9;
            let imgHeight = img.height * 2.9;
            this.ctx.drawImage(img, offsetX, offsetY, imgWidth, imgHeight);

            if (!this.soundPlayed && this.enemy.AtkRes.sound) {
                const audio = operates.playSound(this.enemy.AtkRes.sound);
                if (audio) {
                    this.soundPlayed = true;
                } else {
                    console.warn("播放敵人攻擊音效失敗:", this.enemy.AtkRes.sound);
                }
            }

            // 在攻擊動畫進行到約一半時觸發玩家受傷動畫
            let hitFrame = Math.floor(animates.length * this.enemy.hitframe);
            if (this.frameIndex >= hitFrame && !this.playerHitTriggered) {
                this.playerHitTriggered = true;
                // 觸發玩家受傷動畫
                if (this.playerCanvas && this.playerCanvas.playerAnimationController) {
                    this.playerCanvas.playerAnimationController.playBearAnimation();
                }
            }

            this.frameIndex++;

            if (this.frameIndex < animates.length) {
                this.animationId = setTimeout(() => {
                    this.drawAttackFrame();
                }, 150);
            } else {
                // 攻擊動畫結束
                this.animationId = setTimeout(() => {
                    // 讓玩家停止受傷動畫，回到站姿
                    if (this.playerCanvas && this.playerCanvas.playerAnimationController) {
                        this.playerCanvas.playerAnimationController.playIdleAnimation();
                    }
                    // 敵人也回到站姿
                    this.playIdleAnimation();
                });
            }
        };
    }

    // 繪製攻擊落空動畫幀
    drawAttackMissFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'attackMiss') return;

        let animates = this.enemy.AtkMiss.animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'attackMiss') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.save();
            this.ctx.filter = "brightness(1.5)";

            let offsetX = this.enemy.AtkMiss.x !== undefined ? this.enemy.AtkMiss.x : (this.canvas.width - img.width * 2.9) / 4 + this.enemy.BattleIdleRes.x;
            let offsetY = this.enemy.AtkMiss.y !== undefined ? this.enemy.AtkMiss.y : (this.canvas.height - img.height * 2.9) / 4 + this.enemy.BattleIdleRes.y;
            let imgWidth = img.width * 2.9;
            let imgHeight = img.height * 2.9;
            this.ctx.drawImage(img, offsetX, offsetY, imgWidth, imgHeight);
            this.ctx.restore();

            if (!this.soundPlayed && this.enemy.AtkMiss.sound) {
                const audio = operates.playSound(this.enemy.AtkMiss.sound);
                if (audio) {
                    this.soundPlayed = true;
                } else {
                    console.warn("播放敵人攻擊失誤音效失敗:", this.enemy.AtkMiss.sound);
                }
            }

            this.frameIndex++;

            if (this.frameIndex < animates.length) {
                this.animationId = setTimeout(() => {
                    this.drawAttackMissFrame();
                }, 160);
            } else {
                // 攻擊落空動畫結束，玩家不受傷，直接切換到站姿動畫
                this.animationId = setTimeout(() => {
                    this.playIdleAnimation();
                }, 150);
            }
        };
    }

    // 繪製受傷動畫幀
    drawBearFrame() {
        if (!this.isPlaying || this.currentAnimation !== 'bear') return;

        let animates = this.enemy["遭受傷害"].animates;
        let img = new Image();
        img.src = animates[this.frameIndex];

        img.onload = () => {
            if (!this.isPlaying || this.currentAnimation !== 'bear') return;

            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.save();
            this.ctx.filter = "brightness(1.1)";

            let offsetX = this.enemy["遭受傷害"].x !== undefined ? this.enemy["遭受傷害"].x : (this.canvas.width - img.width * 2.9) / 4 + this.enemy.BattleIdleRes.x;
            let offsetY = this.enemy["遭受傷害"].y !== undefined ? this.enemy["遭受傷害"].y : (this.canvas.height - img.height * 2.9) / 4 + this.enemy.BattleIdleRes.y;
            let imgWidth = img.width * 2.9;
            let imgHeight = img.height * 2.9;

            let shakeX = this.frameIndex % 2 === 0 ? Math.random() * 6 - 3 : 0;
            let shakeY = this.frameIndex % 2 === 0 ? Math.random() * 6 - 3 : 0;

            this.ctx.drawImage(img, offsetX + shakeX, offsetY + shakeY, imgWidth, imgHeight);
            this.ctx.restore();

            this.frameIndex++;

            if (this.frameIndex >= animates.length) {
                this.frameIndex = 0;
            }

            this.animationId = setTimeout(() => {
                this.drawBearFrame();
            }, 90);
        };
    }

    // 清理資源
    destroy() {
        this.stopAnimation();
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
}

// 交互動畫
function drawPlayerIdleAnimation(player, canvas, enemyCanvas = null) {
    if (canvas.playerAnimationController) {
        canvas.playerAnimationController.destroy();
    }

    canvas.playerAnimationController = new PlayerAnimationController(player, canvas, enemyCanvas);
    canvas.playerAnimationController.playIdleAnimation();
}

function drawPlayerATKAnimation(player, canvas, enemyCanvas = null) {
    if (!canvas.playerAnimationController) {
        canvas.playerAnimationController = new PlayerAnimationController(player, canvas, enemyCanvas);
    } else {
        canvas.playerAnimationController.setEnemyCanvas(enemyCanvas);
    }

    canvas.playerAnimationController.playAttackAnimation();
}

function drawPlayerATKMissAnimation(player, canvas, enemyCanvas = null) {
    if (!canvas.playerAnimationController) {
        canvas.playerAnimationController = new PlayerAnimationController(player, canvas, enemyCanvas);
    } else {
        canvas.playerAnimationController.setEnemyCanvas(enemyCanvas);
    }

    canvas.playerAnimationController.playAttackMissAnimation();
}

function drawPlayerBearAnimation(player, canvas, enemyCanvas = null) {
    if (!canvas.playerAnimationController) {
        canvas.playerAnimationController = new PlayerAnimationController(player, canvas, enemyCanvas);
    } else {
        canvas.playerAnimationController.setEnemyCanvas(enemyCanvas);
    }

    canvas.playerAnimationController.playBearAnimation();
}

function drawEnemyIdleAnimation(enemy, canvas, playerCanvas = null) {
    if (canvas.animationController) {
        canvas.animationController.destroy();
    }

    canvas.animationController = new EnemyAnimationController(enemy, canvas, playerCanvas);
    canvas.animationController.playIdleAnimation();
}

function drawEnemyAttackAnimation(enemy, canvas, playerCanvas = null) {
    if (!canvas.animationController) {
        canvas.animationController = new EnemyAnimationController(enemy, canvas, playerCanvas);
    } else {
        canvas.animationController.setPlayerCanvas(playerCanvas);
    }

    canvas.animationController.playAttackAnimation();
}

function drawEnemyAttackMissAnimation(enemy, canvas, playerCanvas = null) {
    if (!canvas.animationController) {
        canvas.animationController = new EnemyAnimationController(enemy, canvas, playerCanvas);
    } else {
        canvas.animationController.setPlayerCanvas(playerCanvas);
    }

    canvas.animationController.playAttackMissAnimation();
}

function drawEnemyBearAnimation(enemy, canvas, playerCanvas = null) {
    if (!canvas.animationController) {
        canvas.animationController = new EnemyAnimationController(enemy, canvas, playerCanvas);
    } else {
        canvas.animationController.setPlayerCanvas(playerCanvas);
    }

    canvas.animationController.playBearAnimation();
}

function clearPlayerAnimation(canvas) {
    if (canvas.playerAnimationController) {
        // 設置漸變消失動畫
        canvas.style.transition = 'opacity 1.5s';
        canvas.style.opacity = '0';

        // 等待動畫完成後清除控制器
        setTimeout(() => {
            canvas.playerAnimationController.destroy();
            canvas.playerAnimationController = null;
            canvas.style.opacity = '1';
            canvas.style.transition = '';
        }, 1600);
    }
}

function clearEnemyAnimation(canvas) {
    if (canvas.animationController) {
        // 設置漸變消失動畫
        canvas.style.transition = 'opacity 1.5s';
        canvas.style.opacity = '0';

        // 等待動畫完成後清除控制器
        setTimeout(() => {
            canvas.animationController.destroy();
            canvas.animationController = null;
            canvas.style.opacity = '1';
            canvas.style.transition = '';
        }, 1600);
    }
}