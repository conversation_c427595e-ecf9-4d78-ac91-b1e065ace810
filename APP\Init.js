// ===== 場景管理系統 (Scene Management System) =====

/**
 * 場景管理器 - 負責控制遊戲主流程、場景切換、關卡通關檢測和資料傳遞
 */
class SceneManager {
    constructor() {
        this.currentScene = null;
        this.sceneType = null; // 'level' | 'camp' | 'hostel'
        this.previousScene = null; // 記錄前一個場景類型
        this.gameData = {
            currentLevel: 0,
            playerData: {},
            gameProgress: {},
            completedLevels: []
        };
        this.isTransitioning = false;
    }

    // 初始化場景管理器
    init() {
        console.log("場景管理器初始化");
        this.setupEventListeners();
        this.showMainMenu();
    }

    // 設置事件監聽器
    setupEventListeners() {
        // 監聽關卡通關事件（僅用於資料保存）
        document.addEventListener('levelComplete', (event) => {
            this.handleLevelComplete(event.detail);
        });

        // 監聽營地觸發事件
        document.addEventListener('campTrigger', (event) => {
            this.handleCampTrigger(event.detail);
        });
    }

    // 切換到關卡場景
    switchToLevel(levelIndex, skipMovie = false) {
        if (this.isTransitioning) return;
        this.isTransitioning = true;

        console.log(`切換到關卡 ${levelIndex}，跳過影片: ${skipMovie}`);

        // 保存當前玩家資料
        this.savePlayerData();

        // 清理當前場景
        this.cleanupCurrentScene();

        // 恢復 sidebar 按鈕狀態（從營地切換到戰鬥場景時）
        if (typeof enableSidebarButtons === 'function') {
            enableSidebarButtons();
        }

        // 設置新場景
        this.sceneType = 'level';
        this.gameData.currentLevel = levelIndex;

        // 初始化已拿取寶箱記錄
        if (typeof window.takenTreasures === 'undefined') {
            window.takenTreasures = new Set();
        }

        // 初始化關卡場景，傳遞跳過影片參數
        this.initLevelScene(levelIndex, skipMovie);

        this.isTransitioning = false;
    }

    // 切換到營地場景
    switchToCamp(campIndex = 0) {
        if (this.isTransitioning) return;
        this.isTransitioning = true;

        console.log(`切換到營地場景 ${campIndex}`);

        // 保存當前玩家資料
        this.savePlayerData();

        // 清理當前場景
        this.cleanupCurrentScene();

        // 記錄前一個場景
        this.previousScene = this.sceneType;

        // 設置新場景
        this.sceneType = 'camp';

        // 初始化營地場景
        this.initCampScene(campIndex);

        this.isTransitioning = false;
    }

    // 切換到客棧場景
    switchToHostel(hostelIndex = 0, previousScene = null) {
        if (this.isTransitioning) return;
        this.isTransitioning = true;

        console.log(`切換到客棧場景 ${hostelIndex}，來源場景: ${previousScene || this.sceneType}`);

        // 保存當前玩家資料
        this.savePlayerData();

        // 清理當前場景
        this.cleanupCurrentScene();

        // 記錄前一個場景
        this.previousScene = previousScene || this.sceneType;

        // 設置新場景
        this.sceneType = 'hostel';

        // 初始化客棧場景
        this.initHostelScene(hostelIndex, this.previousScene);

        this.isTransitioning = false;
    }

    // 切換到遊戲場景（兼容性方法）
    switchToGameScene(skipMovie = false) {
        console.log("切換到遊戲場景（使用當前關卡）");

        // 如果有當前關卡，切換到該關卡
        if (typeof currentLevel !== 'undefined' && currentLevel >= 0) {
            console.log(`切換到關卡 ${currentLevel}，跳過影片: ${skipMovie}`);
            this.switchToLevel(currentLevel, skipMovie);
        } else {
            // 否則切換到關卡 0
            console.warn("currentLevel 未定義，切換到關卡 0");
            this.switchToLevel(0, skipMovie);
        }
    }

    // 切換到已載入的遊戲場景（不重新初始化數據）
    switchToLoadedGameScene() {
        console.log("切換到已載入的遊戲場景（保持現有數據）");

        if (this.isTransitioning) {
            console.log("場景切換中，忽略請求");
            return;
        }

        this.isTransitioning = true;
        this.sceneType = 'level';
        this.currentScene = null;

        // 隱藏其他場景
        this.hideAllScenes();

        // 顯示遊戲場景
        Dom.GameMap.style.display = 'block';

        // 設置 DOM 以適應關卡場景（不重新初始化遊戲數據）
        this.setupLevelSceneDOMOnly();

        // 確保 Canvas 事件正確綁定
        if (typeof Game !== 'undefined' && typeof Game.rebindCanvasEvents === 'function') {
            Game.rebindCanvasEvents();
        }

        // 確保遊戲狀態正確
        if (typeof Game !== 'undefined' && typeof Game.ensureGameStateInitialized === 'function') {
            Game.ensureGameStateInitialized();
        }

        // 重新渲染
        if (typeof render === 'function') {
            render();
        }

        this.isTransitioning = false;
        console.log("已載入遊戲場景切換完成");
    }

    // 設置關卡場景的 DOM（僅 DOM 設置，不重新初始化遊戲數據）
    setupLevelSceneDOMOnly() {
        console.log(`設置關卡場景 DOM（僅 DOM，不重新初始化數據）`);

        // 確保遊戲相關元素可見
        const gameScreen = document.getElementById('GameScreen');
        const gameMap = document.getElementById('GameMap');
        const gameBoard = document.getElementById('GameBoard');
        const gameCanvas = document.getElementById('gameCanvas');

        if (gameScreen) {
            gameScreen.style.display = 'block';
            gameScreen.style.visibility = 'visible';
        }

        if (gameMap) {
            gameMap.style.display = 'block';
            gameMap.style.visibility = 'visible';
        }

        if (gameBoard) {
            gameBoard.style.display = 'block';
            gameBoard.style.visibility = 'visible';

            // 設置背景和尺寸（使用當前關卡數據）
            if (typeof currentLevel !== 'undefined' && typeof controlLayer !== 'undefined') {
                const levelData = controlLayer[currentLevel];
                if (levelData) {
                    gameBoard.style.width = levelData["width"];
                    gameBoard.style.height = levelData["height"];
                    gameBoard.style.backgroundImage = `url(${levelData["地圖背景"]})`;
                    gameBoard.style.gridTemplateColumns = `repeat(${levelData.size.cols}, 1fr)`;
                    gameBoard.style.gridTemplateRows = `repeat(${levelData.size.rows}, 1fr)`;
                }
            }
        }

        if (gameCanvas) {
            gameCanvas.style.display = 'block';
            gameCanvas.style.visibility = 'visible';
        }

        // 隱藏營地相關元素
        const campScreen = document.getElementById('CampScreen');
        if (campScreen) {
            campScreen.style.display = 'none';
        }

        console.log("關卡場景 DOM 設置完成（僅 DOM）");
    }

    // 處理關卡通關（僅用於資料保存）
    handleLevelComplete(levelData) {
        console.log("關卡通關資料保存:", levelData);

        // 標記關卡為已完成
        this.markLevelCompleted(levelData.levelIndex);

        // 保存玩家資料和遊戲進度
        this.savePlayerData();
        this.saveGameProgress();

        console.log("關卡通關資料保存完成");

        // 注意：場景切換現在完全由新的過渡系統處理
        // 這裡只負責資料保存，不再處理場景切換
    }

    // 處理營地觸發事件
    handleCampTrigger(triggerData) {
        console.log("營地觸發事件:", triggerData);

        if (triggerData.type === 'nextLevel') {
            // 進入下一關卡
            const nextLevel = triggerData.levelIndex;
            this.switchToLevel(nextLevel);
        }
    }

    // 保存玩家資料
    savePlayerData() {
        console.log("保存玩家資料");

        if (this.sceneType === 'level' && typeof gameplayers !== 'undefined') {
            gameplayers.forEach(player => {
                // 保存完整的玩家資料，包括當前狀態
                this.gameData.playerData[player.id] = {
                    ...player,
                    // 確保保存重要的狀態資料
                    CurHP: player.CurHP,
                    CurMP: player.CurMP,
                    CurEXP: player.CurEXP,
                    Level: player.Level,
                    Position: player.Position,
                    OldPosition: player.OldPosition,
                    atkrange: player.atkrange,
                    "是否電腦操作": player["是否電腦操作"],
                    // 確保保存移動和行動狀態
                    AlreadyMove: player.AlreadyMove,
                    "是否蓄力": player["是否蓄力"],
                    "是否釋放蓄力": player["是否釋放蓄力"],
                    "已增加移動力": player["已增加移動力"],
                    Inventory: [...(player.Inventory || [])],
                    // 確保保存法術數據，包括 distance 和 Rmdistance
                    "法術": player["法術"] ? player["法術"].map(magic => ({
                        ...magic,
                        distance: magic.distance,
                        Rmdistance: magic.Rmdistance
                    })) : [],
                    // 保存時間戳
                    lastSaved: Date.now()
                };
            });

            console.log("玩家資料已保存:", this.gameData.playerData);
        } else if (this.sceneType === 'camp' && this.currentScene && this.currentScene.player) {
            // 從營地場景保存玩家資料
            const player = this.currentScene.player;
            this.gameData.playerData[player.id] = {
                ...player,
                lastSaved: Date.now()
            };

            console.log("營地玩家資料已保存:", this.gameData.playerData);
        }

        // 同步到舊系統
        Object.keys(this.gameData.playerData).forEach(playerId => {
            allPlayers[playerId] = { ...this.gameData.playerData[playerId] };
        });
    }

    // 載入玩家資料
    loadPlayerData() {
        console.log("載入玩家資料:", this.gameData.playerData);
        return this.gameData.playerData;
    }

    // 保存遊戲進度
    saveGameProgress() {
        console.log("保存遊戲進度");

        this.gameData.gameProgress = {
            currentLevel: this.gameData.currentLevel,
            completedLevels: [...this.gameData.completedLevels],
            currentScene: this.sceneType,
            saveTime: Date.now()
        };

        // 保存寶箱狀態（包括已拿取的寶箱記錄）
        if (typeof controlLayer !== 'undefined' && controlLayer[this.gameData.currentLevel] && controlLayer[this.gameData.currentLevel].Treasures) {
            const originalTreasures = controlLayer[this.gameData.currentLevel].Treasures;
            const takenTreasures = typeof window.takenTreasures !== 'undefined' ? window.takenTreasures : new Set();

            this.gameData.treasures = originalTreasures.map(treasure => ({
                ...treasure,
                "是否已拿取": takenTreasures.has(treasure["位置"])
            }));

            this.gameData.takenTreasures = Array.from(takenTreasures);
            console.log(`保存寶箱狀態: ${this.gameData.treasures.length} 個寶箱，其中 ${this.gameData.takenTreasures.length} 個已拿取`);
        }

        // 可以保存到 localStorage
        try {
            localStorage.setItem('tdjGameProgress', JSON.stringify(this.gameData));
            console.log("遊戲進度已保存到 localStorage");
        } catch (error) {
            console.warn("無法保存到 localStorage:", error);
        }
    }

    // 載入遊戲進度
    loadGameProgress() {
        console.log("載入遊戲進度");

        try {
            const savedData = localStorage.getItem('tdjGameProgress');
            if (savedData) {
                const parsedData = JSON.parse(savedData);

                // 合併保存的資料
                this.gameData = {
                    ...this.gameData,
                    ...parsedData
                };

                // 載入寶箱狀態和已拿取記錄
                if (parsedData.takenTreasures) {
                    window.takenTreasures = new Set(parsedData.takenTreasures);
                    console.log(`載入已拿取寶箱記錄: ${parsedData.takenTreasures.length} 個位置`);
                } else {
                    window.takenTreasures = new Set();
                }

                // 重新構建 gametreasures，過濾掉已拿取的寶箱
                if (parsedData.treasures && typeof gametreasures !== 'undefined') {
                    gametreasures = parsedData.treasures.filter(treasure => !treasure["是否已拿取"]);
                    console.log(`從 localStorage 載入寶箱: 總共 ${parsedData.treasures.length} 個，其中 ${gametreasures.length} 個未拿取`);
                }

                console.log("遊戲進度已從 localStorage 載入:", this.gameData);
                return true;
            }
        } catch (error) {
            console.warn("無法從 localStorage 載入:", error);
        }

        return false;
    }

    // 重置遊戲資料
    resetGameData() {
        console.log("重置遊戲資料");

        this.gameData = {
            currentLevel: 0,
            playerData: {},
            gameProgress: {},
            completedLevels: []
        };

        // 清除 localStorage
        try {
            localStorage.removeItem('tdjGameProgress');
            console.log("localStorage 已清除");
        } catch (error) {
            console.warn("無法清除 localStorage:", error);
        }

        // 清除舊系統資料
        allPlayers = {};
    }

    // 獲取玩家資料
    getPlayerData(playerId) {
        return this.gameData.playerData[playerId] || null;
    }

    // 設置玩家資料
    setPlayerData(playerId, playerData) {
        this.gameData.playerData[playerId] = { ...playerData };

        // 同步到舊系統
        allPlayers[playerId] = { ...playerData };

        console.log(`玩家 ${playerId} 資料已更新`);
    }

    // 檢查關卡是否已完成
    isLevelCompleted(levelIndex) {
        return this.gameData.completedLevels.includes(levelIndex);
    }

    // 標記關卡為已完成
    markLevelCompleted(levelIndex) {
        if (!this.isLevelCompleted(levelIndex)) {
            this.gameData.completedLevels.push(levelIndex);
            console.log(`關卡 ${levelIndex} 標記為已完成`);
        }
    }

    // 清理當前場景
    cleanupCurrentScene() {
        // 清理當前場景實例
        if (this.currentScene && typeof this.currentScene.cleanup === 'function') {
            this.currentScene.cleanup();
        }
        this.currentScene = null;

        // 停止背景音樂
        if (window.bgmAudio) {
            window.bgmAudio.pause();
            window.bgmAudio.currentTime = 0;
        }

        // 清理音樂相關的UI元素
        const musicPrompt = document.getElementById("music-prompt");
        if (musicPrompt && musicPrompt.parentNode) {
            musicPrompt.parentNode.removeChild(musicPrompt);
        }

        // 清理設置對話框
        const settingDialog = document.getElementById("setting-dialog");
        if (settingDialog && settingDialog.parentNode) {
            settingDialog.parentNode.removeChild(settingDialog);
        }

        // 清理DOM元素
        while (Dom.GameBoard.firstChild) {
            Dom.GameBoard.removeChild(Dom.GameBoard.firstChild);
        }

        // 清理遊戲變數
        if (typeof gameplayers !== 'undefined') {
            gameplayers.length = 0;
        }
        if (typeof gameenemys !== 'undefined') {
            gameenemys.length = 0;
        }
        if (typeof gameobstacles !== 'undefined') {
            gameobstacles.length = 0;
        }
        if (typeof gametreasures !== 'undefined') {
            gametreasures.length = 0;
        }

        // 清理Canvas
        if (typeof clearAllHighlights === 'function') {
            clearAllHighlights();
        }

        // 清理 GPS 按鈕
        const existingGpsBtn = document.getElementById('GPSplayer');
        if (existingGpsBtn) {
            existingGpsBtn.remove();
            console.log("已清理 GPS 按鈕");
        }

        // 重置鏡頭位置
        if (typeof cameraX !== 'undefined' && typeof cameraY !== 'undefined') {
            cameraX = 0;
            cameraY = 0;
            console.log("已重置鏡頭位置");
        }

        // 清理邊界滾動
        if (typeof operates !== 'undefined' && typeof operates.cleanupBoundaryScrolling === 'function') {
            operates.cleanupBoundaryScrolling();
        }
    }

    // 顯示主選單
    showMainMenu() {
        UI_content();
    }

    // 初始化關卡場景
    initLevelScene(levelIndex, skipMovie = false) {
        console.log(`初始化關卡場景 ${levelIndex}，跳過影片: ${skipMovie}`);

        // 設置當前關卡
        currentLevel = levelIndex;

        // 設置 DOM 以適應關卡場景
        this.setupLevelSceneDOM(levelIndex);

        // 創建關卡場景實例
        if (typeof LevelScene !== 'undefined') {
            this.currentScene = new LevelScene(levelIndex);
            this.currentScene.init(skipMovie);
        } else {
            // 回退到舊的初始化方式
            if (typeof Game !== 'undefined') {
                Game.init(skipMovie);
            }
        }
    }

    // 設置關卡場景的 DOM
    setupLevelSceneDOM(levelIndex) {
        console.log(`設置關卡場景 DOM: ${levelIndex}`);

        // 確保遊戲相關元素可見
        const gameScreen = document.getElementById('GameScreen');
        const gameMap = document.getElementById('GameMap');
        const gameBoard = document.getElementById('GameBoard');
        const gameCanvas = document.getElementById('gameCanvas');

        if (gameScreen) {
            gameScreen.style.display = 'block';
            gameScreen.style.visibility = 'visible';
        }

        if (gameMap) {
            gameMap.style.display = 'block';
            gameMap.style.visibility = 'visible';
        }

        if (gameBoard) {
            gameBoard.style.display = 'block';
            gameBoard.style.visibility = 'visible';
        }

        if (gameCanvas) {
            gameCanvas.style.display = 'block';
            gameCanvas.style.visibility = 'visible';
        }

        // 隱藏營地相關元素（如果存在）
        const campElements = document.querySelectorAll('[id*="camp"], [class*="camp"]');
        campElements.forEach(element => {
            if (element.id !== 'GameScreen' && element.id !== 'GameMap') {
                element.style.display = 'none';
            }
        });

        console.log("關卡場景 DOM 設置完成");
    }

    // 初始化營地場景
    initCampScene(campIndex) {
        console.log(`初始化營地場景 ${campIndex}`);

        // 設置DOM樣式以適應營地場景
        this.setupCampSceneDOM(campIndex);

        // 創建營地場景實例
        if (typeof CampScene !== 'undefined') {
            this.currentScene = new CampScene(campIndex, this.gameData.playerData);
            this.currentScene.init();
        }
    }

    // 初始化客棧場景
    initHostelScene(hostelIndex, previousScene = null) {
        console.log(`初始化客棧場景 ${hostelIndex}，來源場景: ${previousScene}`);

        // 設置DOM樣式以適應客棧場景
        this.setupHostelSceneDOM(hostelIndex);

        // 創建客棧場景實例
        this.createHostelSceneInstance(hostelIndex, previousScene);
    }

    // 創建客棧場景實例（支持延遲加載）
    createHostelSceneInstance(hostelIndex, previousScene, retryCount = 0) {
        const maxRetries = 5;

        if (typeof HostelScene !== 'undefined' || typeof window.HostelScene !== 'undefined') {
            const HostelSceneClass = HostelScene || window.HostelScene;
            this.currentScene = new HostelSceneClass(hostelIndex, this.gameData.playerData, previousScene);
            this.currentScene.init();
            console.log("客棧場景實例創建成功");
        } else if (retryCount < maxRetries) {
            console.log(`HostelScene 類別尚未加載，重試 ${retryCount + 1}/${maxRetries}`);
            setTimeout(() => {
                this.createHostelSceneInstance(hostelIndex, previousScene, retryCount + 1);
            }, 100);
        } else {
            console.error("HostelScene 類別未定義，請檢查 HostelScene.js 是否正確加載");
            console.log("可用的全局對象:", Object.keys(window).filter(key => key.includes('Scene')));

            // 備用方案：創建一個簡單的客棧場景
            this.createFallbackHostelScene(hostelIndex, previousScene);
        }
    }

    // 備用客棧場景
    createFallbackHostelScene(hostelIndex, previousScene) {
        console.log("使用備用客棧場景");

        this.currentScene = {
            isLoaded: true,
            isRunning: true,
            cleanup: () => {
                console.log("備用客棧場景清理");
                // 移除任何創建的 DOM 元素
                const hostelContainer = document.getElementById('hostelContainer');
                if (hostelContainer) {
                    hostelContainer.remove();
                }
                // 顯示 Canvas
                const canvas = document.getElementById('GameScreen');
                if (canvas) {
                    canvas.style.display = 'block';
                }
            },
            init: () => {
                console.log("初始化備用客棧場景");
                alert(`客棧功能暫時不可用。來源場景: ${previousScene || '未知'}`);
                // 自動返回營地
                setTimeout(() => {
                    if (typeof sceneManager !== 'undefined') {
                        sceneManager.switchToCamp(hostelIndex);
                    }
                }, 2000);
            }
        };

        this.currentScene.init();
    }

    // 設置營地場景的DOM樣式
    setupCampSceneDOM(campIndex) {
        console.log(`設置營地場景 DOM: ${campIndex}`);

        const campData = campdata && campdata[campIndex] ? campdata[campIndex] : {
            size: { cols: 20, rows: 30 },
            width: "150%",
            height: "200%"
        };

        // 隱藏關卡相關的 Canvas（營地有自己的 Canvas）
        const gameCanvas = document.getElementById('gameCanvas');
        if (gameCanvas) {
            gameCanvas.style.display = 'none';
            console.log("已隱藏關卡 Canvas");
        }

        // 設置GameBoard樣式
        const gameBoard = document.getElementById('GameBoard');
        if (gameBoard) {
            gameBoard.style.display = 'grid';
            gameBoard.style.width = campData.width;
            gameBoard.style.height = campData.height;
            gameBoard.style.gridTemplateColumns = `repeat(${campData.size.cols}, 1fr)`;
            gameBoard.style.gridTemplateRows = `repeat(${campData.size.rows}, 1fr)`;
            gameBoard.style.position = 'relative';
            gameBoard.style.backgroundImage = 'none';
            gameBoard.style.backgroundColor = 'transparent';

            console.log(`營地場景DOM設置: ${campData.size.cols}x${campData.size.rows} 網格`);
        }

        // 確保GameMap容器正確設置
        const gameMap = document.getElementById('GameMap');
        if (gameMap) {
            gameMap.style.overflow = 'auto';
            gameMap.style.position = 'relative';
            gameMap.style.display = 'block';
            gameMap.style.visibility = 'visible';
        }

        // 確保 GameScreen 可見
        const gameScreen = document.getElementById('GameScreen');
        if (gameScreen) {
            gameScreen.style.display = 'block';
            gameScreen.style.visibility = 'visible';
        }

        console.log("營地場景 DOM 設置完成");
    }

    // 設置客棧場景 DOM
    setupHostelSceneDOM(hostelIndex) {
        console.log(`設置客棧場景 ${hostelIndex} DOM`);

        // 隱藏主選單相關元素
        const mainMenuElements = document.querySelectorAll('[id*="menu"], [class*="menu"]');
        mainMenuElements.forEach(element => {
            if (element.id !== 'GameScreen' && element.id !== 'GameMap') {
                element.style.display = 'none';
            }
        });

        // 隱藏關卡相關元素
        const levelElements = document.querySelectorAll('[id*="level"], [class*="level"]');
        levelElements.forEach(element => {
            if (element.id !== 'GameScreen' && element.id !== 'GameMap') {
                element.style.display = 'none';
            }
        });

        // 隱藏營地相關元素
        const campElements = document.querySelectorAll('[id*="camp"], [class*="camp"]');
        campElements.forEach(element => {
            if (element.id !== 'GameScreen' && element.id !== 'GameMap') {
                element.style.display = 'none';
            }
        });

        // 確保遊戲畫面可見
        const gameScreen = document.getElementById('GameScreen');
        if (gameScreen) {
            gameScreen.style.display = 'block';
            gameScreen.style.position = 'absolute';
            gameScreen.style.top = '0';
            gameScreen.style.left = '0';
            gameScreen.style.zIndex = '1';
        }

        // 保持 sidebar 原狀（客棧有獨立的選單系統）
        // 不修改原本的 sidebar

        console.log("客棧場景 DOM 設置完成");
    }

    // 檢查勝利條件
    checkVictoryCondition() {
        if (this.sceneType !== 'level') return;

        // 檢查是否所有敵人都死亡
        if (typeof gameenemys !== 'undefined') {
            const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
            if (aliveEnemies.length === 0) {
                console.log("所有敵人死亡，關卡通關");

                // 觸發關卡通關事件
                const levelCompleteEvent = new CustomEvent('levelComplete', {
                    detail: {
                        levelIndex: currentLevel,
                        completionTime: Date.now()
                    }
                });
                document.dispatchEvent(levelCompleteEvent);
            }
        }
    }

    // 檢查失敗條件
    checkDefeatCondition() {
        if (this.sceneType !== 'level') return;

        // 檢查是否所有玩家都死亡
        if (typeof gameplayers !== 'undefined') {
            const alivePlayers = gameplayers.filter(player => player.CurHP > 0);
            if (alivePlayers.length === 0) {
                console.log("所有玩家死亡，遊戲結束");
                // 這裡可以觸發遊戲結束事件
            }
        }
    }
}

// 創建全域場景管理器實例
const sceneManager = new SceneManager();

// ===== 測試和調試功能 =====

// 測試場景切換功能（使用新的過渡系統）
function testSceneTransition() {
    console.log("=== 開始測試場景切換功能（新過渡系統）===");

    // 測試1: 切換到關卡場景
    console.log("測試1: 切換到關卡0");
    sceneManager.switchToLevel(0);

    // 測試2: 模擬關卡通關（使用新的過渡系統）
    setTimeout(async () => {
        console.log("測試2: 模擬關卡通關（新過渡系統）");

        try {
            // 直接調用 Players.js 中的 clearBattleCis 來模擬勝利
            if (typeof clearBattleCis !== 'undefined') {
                console.log("調用 clearBattleCis 模擬勝利處理");
                await clearBattleCis();
            } else {
                console.log("clearBattleCis 函數不可用，跳過測試");
            }
        } catch (error) {
            console.error("勝利處理測試失敗:", error);
        }
    }, 5000);

    // 測試3: 模擬營地觸發
    setTimeout(() => {
        console.log("測試3: 模擬營地觸發進入下一關");
        const campTriggerEvent = new CustomEvent('campTrigger', {
            detail: {
                type: 'nextLevel',
                levelIndex: 1
            }
        });
        document.dispatchEvent(campTriggerEvent);
    }, 15000); // 延長時間，給新過渡系統足夠時間完成
}

// 測試資料傳遞功能
function testDataTransfer() {
    console.log("=== 開始測試資料傳遞功能 ===");

    // 創建測試玩家資料
    const testPlayerData = {
        id: "player1",
        name: "測試角色",
        CurHP: 80,
        CurMP: 50,
        CurEXP: 1200,
        Level: 5,
        Inventory: ["測試物品1", "測試物品2"]
    };

    // 測試保存
    sceneManager.setPlayerData("player1", testPlayerData);
    sceneManager.saveGameProgress();

    // 測試載入
    const loadedData = sceneManager.getPlayerData("player1");
    console.log("保存的資料:", testPlayerData);
    console.log("載入的資料:", loadedData);

    // 驗證資料一致性
    const isDataConsistent = JSON.stringify(testPlayerData) === JSON.stringify(loadedData);
    console.log("資料一致性測試:", isDataConsistent ? "通過" : "失敗");
}

// 調試模式切換
function toggleDebugMode() {
    window.debugMode = !window.debugMode;
    console.log("調試模式:", window.debugMode ? "開啟" : "關閉");

    if (window.debugMode) {
        // 顯示調試資訊
        console.log("當前場景管理器狀態:", sceneManager);
        console.log("當前場景類型:", sceneManager.sceneType);
        console.log("當前關卡:", sceneManager.gameData.currentLevel);
        console.log("已完成關卡:", sceneManager.gameData.completedLevels);
        console.log("玩家資料:", sceneManager.gameData.playerData);
    }
}

// 測試敵人死亡檢查功能
function testEnemyDeathCheck() {
    console.log("=== 開始測試敵人死亡檢查功能 ===");

    if (typeof gameenemys === 'undefined' || gameenemys.length === 0) {
        console.log("當前沒有敵人，無法測試");
        return;
    }

    console.log(`當前敵人數量: ${gameenemys.length}`);
    gameenemys.forEach((enemy, index) => {
        console.log(`敵人 ${index}: ${enemy.name}, HP: ${enemy.CurHP}/${enemy.HP}`);
    });

    // 模擬殺死所有敵人
    console.log("模擬殺死所有敵人...");
    gameenemys.forEach(enemy => {
        enemy.CurHP = 0;
    });

    // 手動觸發檢查
    console.log("手動觸發勝利條件檢查...");
    sceneManager.checkVictoryCondition();
}

// 測試營地場景渲染
function testCampSceneRendering() {
    console.log("=== 開始測試營地場景渲染 ===");

    if (sceneManager.sceneType !== 'camp') {
        console.log("當前不在營地場景，切換到營地場景...");
        sceneManager.switchToCamp(0);
        setTimeout(testCampSceneRendering, 2000);
        return;
    }

    const campScene = sceneManager.currentScene;
    if (!campScene) {
        console.log("✗ 營地場景實例不存在");
        return;
    }

    console.log("=== 營地場景狀態檢查 ===");
    console.log(`Canvas: ${campScene.canvas ? '存在' : '不存在'}`);
    console.log(`Context: ${campScene.ctx ? '存在' : '不存在'}`);
    console.log(`玩家: ${campScene.player ? '存在' : '不存在'}`);
    console.log(`運行狀態: ${campScene.isRunning}`);

    if (campScene.canvas) {
        console.log(`Canvas 尺寸: ${campScene.canvas.width}x${campScene.canvas.height}`);
        console.log(`Canvas 樣式: display=${campScene.canvas.style.display}, visibility=${campScene.canvas.style.visibility}`);
        console.log(`Canvas zIndex: ${campScene.canvas.style.zIndex}`);
    }

    if (campScene.player) {
        console.log(`玩家位置: ${campScene.playerPosition}`);
        console.log(`玩家像素位置: (${campScene.playerCurrentPos.x}, ${campScene.playerCurrentPos.y})`);
        console.log(`玩家動畫狀態: ${JSON.stringify(campScene.playerAnimationState)}`);
    }

    // 強制重新渲染
    console.log("強制重新渲染...");
    if (campScene.render) {
        campScene.render();
        console.log("✓ 渲染完成");
    }
}

// 將測試函數暴露到全域，方便在控制台調用
window.testSceneTransition = testSceneTransition;
window.testDataTransfer = testDataTransfer;
window.toggleDebugMode = toggleDebugMode;
window.testEnemyDeathCheck = testEnemyDeathCheck;
window.testCampSceneRendering = testCampSceneRendering;
window.sceneManager = sceneManager;

// ===== 原有的DOM和變數定義 =====

const Dom = {
    SaveBtn: document.getElementById("savegamebtn"),
    LoadBtn: document.getElementById("loadgamebtn"),
    LookRoleBtn: document.getElementById("lookrolebtn"),
    CancelBtn: document.getElementById("cancelbtn"),
    EndTurnBtn: document.getElementById("endturnbtn"),
    SettingBtn: document.getElementById("settingbtn"),
    GameMap: document.getElementById("GameMap"),
    GameBoard: document.getElementById("GameBoard"),
    InfoDialog: document.getElementById("InfoDialog"),
    BattleScreen: document.getElementById("BattleScreen"),
    GameScreen: document.getElementById("GameScreen"),
}

let clicktimes = 1;
let tempname = [];
let playerrightinfo = null;

let runOBJ = {
    "回合": 0,
    "當前行動方": null,
    "當前操作": null,
    "當前選取": null,
    "第二次選取": null,
    "是否有行走": false,
    "當前選取物品": null,
    "當前物品操作": null,
    "查看角色選取欄位": "五內",
}

let currentLevel = 0;

let overlay = document.createElement("div");
overlay.style.position = "fixed";
overlay.style.top = "0";
overlay.style.left = "0";
overlay.style.width = "100%";
overlay.style.height = "100%";
overlay.style.zIndex = "9999";

let allPlayers = {}; // 存儲所有玩家的最新數據

let operates = {
    init: function () {
        runOBJ["回合"] = 1;
        runOBJ["當前行動方"] = "Players";
        runOBJ["當前操作"] = null;

        Dom.GameScreen.appendChild(overlay);
        setTimeout(() => {
            TurnTextHandle();
        })

        setTimeout(() => {
            Dom.GameScreen.removeChild(overlay);
        }, 1000);
    },
    nextRound: function () {
        runOBJ["回合"]++;
        runOBJ["當前行動方"] = runOBJ["當前行動方"] === "Players" ? "Enemys" : "Players";
        runOBJ["當前操作"] = null;
        runOBJ["當前選取"] = null;
        runOBJ["第二次選取"] = null;
        tempname = [];

        // 檢查是否需要觸發敵人增援
        this.checkEnemyReinforcements();

        TurnTextHandle();
        Dom.GameBoard.style.backgroundColor = "none";
        Dom.GameBoard.style.backgroundBlendMode = "normal";

        if (runOBJ["當前行動方"] === "Players") {
            let hasAIControlledPlayer = gameplayers.some(player => player["是否電腦操作"]);
            if (hasAIControlledPlayer) {
                setTimeout(() => {
                    PlayerAIAction();
                }, 3000);
            } else {
                setTimeout(() => {
                    operates.MoveComera(gameplayers[0].Position);
                }, 3000);
            }
        } else {
            setTimeout(() => {
                operates.MoveComera(gameplayers[0].Position);
            }, 3000);
        }
    },

    // 檢查敵人增援功能
    checkEnemyReinforcements: function () {
        const currentLevelData = controlLayer[currentLevel];
        const currentTurn = runOBJ["回合"];

        // 檢查 AdditionEnemys（支持單次和多波增援）
        if (currentLevelData.AdditionEnemys) {
            // 檢查是否為陣列格式（多波增援）
            if (Array.isArray(currentLevelData.AdditionEnemys)) {
                // 多波增援格式
                currentLevelData.AdditionEnemys.forEach((reinforcement, index) => {
                    if (currentTurn === reinforcement.turn) {
                        console.log(`第 ${currentTurn} 回合觸發第 ${index + 1} 波敵人增援`);
                        this.spawnEnemyReinforcements(reinforcement);
                    }
                });
            } else {
                // 單次增援格式（向後兼容）
                const reinforcement = currentLevelData.AdditionEnemys;
                if (currentTurn === reinforcement.turn) {
                    console.log(`第 ${currentTurn} 回合觸發敵人增援`);
                    this.spawnEnemyReinforcements(reinforcement);
                }
            }
        }

        // 檢查多波增援（舊版本格式，保持兼容）
        if (currentLevelData.MultipleReinforcements) {
            const reinforcements = currentLevelData.MultipleReinforcements;

            // 檢查每一波增援
            reinforcements.forEach((reinforcement, index) => {
                if (currentTurn === reinforcement.turn) {
                    console.log(`第 ${currentTurn} 回合觸發第 ${index + 1} 波敵人增援（MultipleReinforcements）`);
                    this.spawnEnemyReinforcements(reinforcement);
                }
            });
        }

        // 檢查條件觸發增援
        this.checkConditionalReinforcements();
    },

    // 檢查條件觸發增援
    checkConditionalReinforcements: function () {
        const currentLevelData = controlLayer[currentLevel];

        if (!currentLevelData.ConditionalReinforcements) {
            return;
        }

        const conditionalReinforcements = currentLevelData.ConditionalReinforcements;

        conditionalReinforcements.forEach((reinforcement, index) => {
            // 檢查是否已經觸發過
            if (reinforcement.triggered) {
                return;
            }

            // 檢查觸發條件
            if (this.checkReinforcementCondition(reinforcement.condition)) {
                console.log(`條件觸發增援 ${index + 1}: ${reinforcement.condition.type}`);
                reinforcement.triggered = true; // 標記為已觸發
                this.spawnEnemyReinforcements(reinforcement);
            }
        });
    },

    // 檢查增援觸發條件
    checkReinforcementCondition: function (condition) {
        switch (condition.type) {
            case 'enemyCount':
                // 當敵人數量少於指定數量時觸發
                const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
                return aliveEnemies.length <= condition.value;

            case 'playerHP':
                // 當任意玩家血量低於指定百分比時觸發
                const lowHPPlayers = gameplayers.filter(player => {
                    const hpRatio = player.CurHP / player.HP;
                    return hpRatio <= condition.value;
                });
                return lowHPPlayers.length > 0;

            case 'turn':
                // 當回合數達到指定數量時觸發
                return runOBJ["回合"] >= condition.value;

            case 'playerPosition':
                // 當玩家到達指定位置時觸發
                return gameplayers.some(player =>
                    condition.positions.includes(player.Position)
                );

            default:
                console.warn(`未知的增援觸發條件: ${condition.type}`);
                return false;
        }
    },

    // 生成敵人增援
    spawnEnemyReinforcements: function (reinforcement) {
        console.log("開始生成敵人增援", reinforcement);

        // 標記增援為待處理，在敵人回合時顯示訊息
        if (!window.pendingReinforcements) {
            window.pendingReinforcements = [];
        }
        window.pendingReinforcements.push(reinforcement);

        // 添加增援敵人到遊戲中
        if (reinforcement.enemys && reinforcement.enemys.length > 0) {
            reinforcement.enemys.forEach((enemy, index) => {
                // 創建敵人副本，避免修改原始數據
                const newEnemy = { ...enemy };

                // 確保敵人有唯一的ID（如果需要的話）
                if (!newEnemy.id) {
                    newEnemy.id = `reinforcement_${Date.now()}_${index}`;
                }

                // 添加到遊戲敵人陣列
                gameenemys.push(newEnemy);
                console.log(`增援敵人 ${newEnemy.name} 已添加到位置 ${newEnemy.Position}`);
            });

            // 重新渲染地圖以顯示新的敵人
            setTimeout(() => {
                render();
                console.log(`成功添加 ${reinforcement.enemys.length} 個增援敵人`);
            }, 1000);
        }
    },

    // 處理待顯示的增援訊息（在敵人回合調用）
    processPendingReinforcementMessages: function () {
        if (!window.pendingReinforcements || window.pendingReinforcements.length === 0) {
            return;
        }

        console.log("處理待顯示的增援訊息");

        // 顯示所有待處理的增援訊息
        window.pendingReinforcements.forEach((reinforcement, reinforcementIndex) => {
            if (reinforcement.msg && reinforcement.msg.length > 0) {
                // 為每個增援的訊息添加延遲，避免同時顯示
                setTimeout(() => {
                    this.showReinforcementMessage(reinforcement.msg);
                }, reinforcementIndex * 1500); // 每個增援間隔1.5秒
            }
        });

        // 清空待處理的增援訊息
        window.pendingReinforcements = [];
    },

    // 顯示增援訊息
    showReinforcementMessage: function (messages) {
        messages.forEach((msg, index) => {
            setTimeout(() => {
                // 創建訊息對話框
                let messageDialog = document.createElement("dialog");
                messageDialog.id = "reinforcement-message";
                messageDialog.style.cssText = `
                    background: rgba(247, 231, 173, 0.95);
                    color: rgb(168, 105, 38);
                    border: 5px solid rgb(165, 90, 24);
                    border-radius: 10px;
                    padding: 20px;
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    backdrop-filter: blur(5px);
                    max-width: 400px;
                    margin: auto;
                `;

                messageDialog.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        ${msg.context}
                    </div>
                    <button id="reinforcement-ok-${index}" style="
                        background: rgb(165, 90, 24);
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 14px;
                    ">確定</button>
                `;

                document.body.appendChild(messageDialog);
                messageDialog.showModal();

                // 綁定確定按鈕事件
                document.getElementById(`reinforcement-ok-${index}`).onclick = function () {
                    messageDialog.close();
                    document.body.removeChild(messageDialog);
                };

                // 3秒後自動關閉
                setTimeout(() => {
                    if (document.body.contains(messageDialog)) {
                        messageDialog.close();
                        document.body.removeChild(messageDialog);
                    }
                }, 3000);

            }, index * 1000); // 如果有多個訊息，間隔1秒顯示
        });
    },

    initMap: function () {
        Dom.GameBoard.style.width = controlLayer[currentLevel]["width"];
        Dom.GameBoard.style.height = controlLayer[currentLevel]["height"];
        Dom.GameBoard.style.backgroundImage = `url(${controlLayer[currentLevel]["地圖背景"]})`;
        Dom.GameBoard.style.gridTemplateColumns = `repeat(${controlLayer[currentLevel].size.cols}, 1fr)`;
        Dom.GameBoard.style.gridTemplateRows = `repeat(${controlLayer[currentLevel].size.rows}, 1fr)`;

        // 初始化Canvas
        initCanvas(controlLayer[currentLevel].size.cols, controlLayer[currentLevel].size.rows);
        updateMapObjects();
        render();

        // 初始化邊界滾動
        this.initBoundaryScrolling();
    },
    initMusic: function () {
        // 停止之前的音樂
        if (window.bgmAudio) {
            window.bgmAudio.pause();
            window.bgmAudio.currentTime = 0;
        }

        // 創建新的音頻對象
        window.bgmAudio = new Audio();
        window.bgmAudio.src = controlLayer[currentLevel]["BGM"];
        window.bgmAudio.loop = true;

        // 載入保存的音量設置
        const savedVolume = this.loadMusicVolume();
        window.bgmAudio.volume = savedVolume;

        // 嘗試自動播放音樂
        this.playBackgroundMusic();
    },

    // 播放背景音樂的函數
    playBackgroundMusic: function () {
        if (!window.bgmAudio) {
            console.warn("背景音樂未初始化");
            return;
        }

        // 嘗試播放音樂
        const playPromise = window.bgmAudio.play();

        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log("背景音樂開始播放");
            }).catch(error => {
                console.log("自動播放被瀏覽器阻止，等待用戶交互:", error);

                // 如果自動播放失敗，添加一次性點擊事件
                const playOnInteraction = () => {
                    window.bgmAudio.play().then(() => {
                        console.log("用戶交互後背景音樂開始播放");
                    }).catch(err => {
                        console.error("播放音樂失敗:", err);
                    });

                    // 移除事件監聽器
                    document.removeEventListener("click", playOnInteraction);
                    document.removeEventListener("keydown", playOnInteraction);
                    document.removeEventListener("touchstart", playOnInteraction);
                };

                // 添加多種用戶交互事件
                document.addEventListener("click", playOnInteraction, { once: true });
                document.addEventListener("keydown", playOnInteraction, { once: true });
                document.addEventListener("touchstart", playOnInteraction, { once: true });

                // 顯示提示訊息
                this.showMusicPrompt();
            });
        }
    },

    // 顯示音樂播放提示
    showMusicPrompt: function () {
        // 檢查是否已經顯示過提示
        if (document.getElementById("music-prompt")) {
            return;
        }

        const prompt = document.createElement("div");
        prompt.id = "music-prompt";
        prompt.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 10000;
            cursor: pointer;
            transition: opacity 0.3s ease;
        `;
        prompt.innerHTML = "🎵 點擊任意位置開始播放背景音樂";

        document.body.appendChild(prompt);

        // 3秒後自動淡出
        setTimeout(() => {
            if (prompt.parentNode) {
                prompt.style.opacity = "0";
                setTimeout(() => {
                    if (prompt.parentNode) {
                        document.body.removeChild(prompt);
                    }
                }, 300);
            }
        }, 3000);

        // 點擊提示框也可以播放音樂
        prompt.addEventListener("click", () => {
            if (window.bgmAudio) {
                window.bgmAudio.play().then(() => {
                    console.log("通過提示框播放音樂");
                    if (prompt.parentNode) {
                        document.body.removeChild(prompt);
                    }
                });
            }
        });
    },

    // 音樂控制功能
    toggleMusic: function () {
        if (!window.bgmAudio) {
            console.warn("背景音樂未初始化");
            return;
        }

        if (window.bgmAudio.paused) {
            window.bgmAudio.play().then(() => {
                console.log("背景音樂恢復播放");
                this.updateMusicButton(false);
            }).catch(error => {
                console.error("播放音樂失敗:", error);
            });
        } else {
            window.bgmAudio.pause();
            console.log("背景音樂暫停");
            this.updateMusicButton(true);
        }
    },

    // 更新音樂按鈕狀態
    updateMusicButton: function (isPaused) {
        const musicBtn = document.getElementById("music-control-btn");
        if (musicBtn) {
            musicBtn.innerHTML = isPaused ? "🔇" : "🎵";
            musicBtn.title = isPaused ? "點擊播放音樂" : "點擊暫停音樂";
        }
    },



    // 設置音樂音量
    setMusicVolume: function (volume) {
        if (!window.bgmAudio) {
            console.warn("背景音樂未初始化");
            return;
        }

        // 確保音量在0-1之間
        volume = Math.max(0, Math.min(1, volume));
        window.bgmAudio.volume = volume;

        console.log(`背景音樂音量設置為: ${Math.round(volume * 100)}%`);

        // 保存音量設置到本地存儲
        localStorage.setItem('gameMusic_volume', volume.toString());
    },

    // 從本地存儲載入音量設置
    loadMusicVolume: function () {
        const savedVolume = localStorage.getItem('gameMusic_volume');
        if (savedVolume !== null) {
            const volume = parseFloat(savedVolume);
            if (!isNaN(volume)) {
                this.setMusicVolume(volume);
                return volume;
            }
        }
        return 0.7; // 默認音量70%
    },

    // 獲取當前音樂狀態
    getMusicStatus: function () {
        if (!window.bgmAudio) {
            return {
                initialized: false,
                playing: false,
                volume: 0,
                currentTime: 0,
                duration: 0
            };
        }

        return {
            initialized: true,
            playing: !window.bgmAudio.paused,
            volume: window.bgmAudio.volume,
            currentTime: window.bgmAudio.currentTime,
            duration: window.bgmAudio.duration || 0,
            src: window.bgmAudio.src
        };
    },

    // ===== 音效控制功能 =====

    // 初始化音效設置
    initSoundSettings: function () {
        // 初始化全局音效設置
        if (typeof window.soundSettings === 'undefined') {
            window.soundSettings = {
                enabled: true,
                volume: 0.7,
                audioObjects: new Set() // 追蹤所有音效對象
            };
        }

        // 載入保存的設置
        this.loadSoundSettings();
    },

    // 載入音效設置
    loadSoundSettings: function () {
        const savedEnabled = localStorage.getItem('gameSound_enabled');
        const savedVolume = localStorage.getItem('gameSound_volume');

        if (savedEnabled !== null) {
            window.soundSettings.enabled = savedEnabled === 'true';
        }

        if (savedVolume !== null) {
            const volume = parseFloat(savedVolume);
            if (!isNaN(volume)) {
                window.soundSettings.volume = Math.max(0, Math.min(1, volume));
            }
        }

        console.log('音效設置已載入:', window.soundSettings);
    },

    // 保存音效設置
    saveSoundSettings: function () {
        localStorage.setItem('gameSound_enabled', window.soundSettings.enabled.toString());
        localStorage.setItem('gameSound_volume', window.soundSettings.volume.toString());
    },

    // 切換音效開關
    toggleSoundEffects: function () {
        if (!window.soundSettings) {
            this.initSoundSettings();
        }

        window.soundSettings.enabled = !window.soundSettings.enabled;
        this.saveSoundSettings();

        console.log(`音效${window.soundSettings.enabled ? '開啟' : '關閉'}`);

        // 如果關閉音效，停止所有正在播放的音效
        if (!window.soundSettings.enabled) {
            this.stopAllSounds();
        }
    },

    // 設置音效音量
    setSoundVolume: function (volume) {
        if (!window.soundSettings) {
            this.initSoundSettings();
        }

        volume = Math.max(0, Math.min(1, volume));
        window.soundSettings.volume = volume;
        this.saveSoundSettings();

        console.log(`音效音量設置為: ${Math.round(volume * 100)}%`);

        // 更新所有現有音效對象的音量
        window.soundSettings.audioObjects.forEach(audio => {
            if (audio && !audio.paused) {
                audio.volume = volume;
            }
        });
    },

    // 獲取音效音量
    getSoundVolume: function () {
        if (!window.soundSettings) {
            this.initSoundSettings();
        }
        return window.soundSettings.volume;
    },

    // 檢查音效是否開啟
    isSoundEnabled: function () {
        if (!window.soundSettings) {
            this.initSoundSettings();
        }
        return window.soundSettings.enabled;
    },

    // 播放音效（統一接口）
    playSound: function (src, options = {}) {
        if (!this.isSoundEnabled()) {
            console.log('音效已關閉，跳過播放:', src);
            return null;
        }

        try {
            const audio = new Audio(src);
            audio.volume = this.getSoundVolume();

            // 設置其他選項
            if (options.loop) audio.loop = options.loop;
            if (options.volume !== undefined) {
                audio.volume = Math.max(0, Math.min(1, options.volume)) * this.getSoundVolume();
            }

            // 追蹤音效對象
            window.soundSettings.audioObjects.add(audio);

            // 播放結束後清理
            audio.addEventListener('ended', () => {
                window.soundSettings.audioObjects.delete(audio);
            });

            // 播放音效
            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.catch(error => {
                    console.warn('播放音效失敗:', src, error);
                    window.soundSettings.audioObjects.delete(audio);
                });
            }

            return audio;
        } catch (error) {
            console.error('創建音效失敗:', src, error);
            return null;
        }
    },

    // 停止所有音效
    stopAllSounds: function () {
        if (!window.soundSettings) return;

        window.soundSettings.audioObjects.forEach(audio => {
            if (audio && !audio.paused) {
                audio.pause();
                audio.currentTime = 0;
            }
        });

        window.soundSettings.audioObjects.clear();
        console.log('所有音效已停止');
    },

    // 測試音效
    testSoundEffect: function () {
        console.log('測試音效播放...');

        // 嘗試播放一個測試音效
        const testSounds = [
            './Public/footstepsound.mp3',
            './Public/lvup.mp3',
            './Public/click.mp3'
        ];

        // 隨機選擇一個測試音效
        const randomSound = testSounds[Math.floor(Math.random() * testSounds.length)];
        const audio = this.playSound(randomSound);

        if (audio) {
            console.log('測試音效播放成功:', randomSound);
        } else {
            console.log('測試音效播放失敗');
        }
    },

    // 測試音樂
    testMusic: function () {
        console.log('測試音樂播放...');

        if (!window.bgmAudio) {
            console.log('背景音樂未初始化');
            return;
        }

        if (window.bgmAudio.paused) {
            this.playBackgroundMusic();
        } else {
            console.log('背景音樂正在播放中');
        }
    },
    MoveComera: function (position) {
        console.log(`移動鏡頭到位置: ${position}`);

        let x = position % controlLayer[currentLevel].size.cols;
        let y = Math.floor(position / controlLayer[currentLevel].size.cols);
        let cellSize = 100;
        let offsetX = Dom.GameMap.offsetWidth / 2;
        let offsetY = Dom.GameMap.offsetHeight / 2;

        // 移動 DOM 鏡頭
        Dom.GameMap.scrollTo(x * cellSize + 200 - offsetX, y * cellSize - offsetY);

        // 同時更新 Canvas 鏡頭
        if (typeof Game !== 'undefined' && typeof Game.updateCanvasCamera === 'function') {
            Game.updateCanvasCamera(position);
        } else {
            // 直接更新 Canvas 鏡頭變數
            if (typeof cameraX !== 'undefined' && typeof cameraY !== 'undefined') {
                const targetCameraX = x * cellWidth - (canvas ? canvas.width / 2 : 800);
                const targetCameraY = y * cellHeight - (canvas ? canvas.height / 2 : 600);

                const maxCameraX = Math.max(0, controlLayer[currentLevel].size.cols * cellWidth - (canvas ? canvas.width : 800));
                const maxCameraY = Math.max(0, controlLayer[currentLevel].size.rows * cellHeight - (canvas ? canvas.height : 600));

                cameraX = Math.max(0, Math.min(targetCameraX, maxCameraX));
                cameraY = Math.max(0, Math.min(targetCameraY, maxCameraY));

                // 重新渲染 Canvas
                if (typeof render === 'function') {
                    render();
                }
            }
        }
    },

    // 初始化邊界滾動
    initBoundaryScrolling: function() {
        console.log("初始化邊界滾動");

        // 邊界滾動設置
        const boundarySize = 50; // 邊界區域大小（像素）
        const scrollSpeed = 5; // 滾動速度

        // 保存 this 引用
        const self = this;

        // 滑鼠移動事件處理器
        const handleMouseMove = (event) => {
            if (!Dom.GameMap) return;

            const rect = Dom.GameMap.getBoundingClientRect();
            const mouseX = event.clientX - rect.left;
            const mouseY = event.clientY - rect.top;

            const mapWidth = rect.width;
            const mapHeight = rect.height;

            // 檢查是否在邊界區域
            let scrollDirection = null;

            if (mouseX < boundarySize) {
                scrollDirection = 'left';
                
            } else if (mouseX > mapWidth - boundarySize) {
                scrollDirection = 'right';
               
            } else if (mouseY < boundarySize) {
                scrollDirection = 'up';
           
            } else if (mouseY > mapHeight - boundarySize) {
                scrollDirection = 'down';
               
            }

            // 開始或停止滾動
            if (scrollDirection && !self.isScrolling) {
                self.startBoundaryScroll(scrollDirection, scrollSpeed);
            } else if (!scrollDirection && self.isScrolling) {
                self.stopBoundaryScroll();
            }
        };

        // 滑鼠離開事件處理器
        const handleMouseLeave = () => {
            self.stopBoundaryScroll();
        };

        // 添加事件監聽器
        Dom.GameMap.addEventListener('mousemove', handleMouseMove);
        Dom.GameMap.addEventListener('mouseleave', handleMouseLeave);

        // 保存事件處理器以便後續清理
        this.boundaryScrollHandlers = {
            mousemove: handleMouseMove,
            mouseleave: handleMouseLeave
        };

        // 確保 GameMap 可以滾動
        if (Dom.GameMap) {
            Dom.GameMap.style.overflow = 'auto';
            Dom.GameMap.style.position = 'relative';
        }
    },

    // 開始邊界滾動
    startBoundaryScroll: function(direction, speed) {
        if (this.scrollInterval) {
            clearInterval(this.scrollInterval);
        }

        this.isScrolling = true;
        const self = this;
        this.scrollInterval = setInterval(() => {
            self.performBoundaryScroll(direction, speed);
        }, 16); // 約60fps
    },

    // 停止邊界滾動
    stopBoundaryScroll: function() {
        if (this.scrollInterval) {
            clearInterval(this.scrollInterval);
            this.scrollInterval = null;
        }
        this.isScrolling = false;
    },

    // 執行邊界滾動
    performBoundaryScroll: function(direction, speed) {
        if (!Dom.GameMap) {
            return;
        }

        const currentScrollLeft = Dom.GameMap.scrollLeft;
        const currentScrollTop = Dom.GameMap.scrollTop;
        const maxScrollLeft = Dom.GameMap.scrollWidth - Dom.GameMap.clientWidth;
        const maxScrollTop = Dom.GameMap.scrollHeight - Dom.GameMap.clientHeight;

        let newScrollLeft = currentScrollLeft;
        let newScrollTop = currentScrollTop;

        // 根據方向計算新的滾動位置
        switch (direction) {
            case 'left':
                newScrollLeft = Math.max(0, currentScrollLeft - speed);
                break;
            case 'right':
                newScrollLeft = Math.min(maxScrollLeft, currentScrollLeft + speed);
                break;
            case 'up':
                newScrollTop = Math.max(0, currentScrollTop - speed);
                break;
            case 'down':
                newScrollTop = Math.min(maxScrollTop, currentScrollTop + speed);
                break;
        }

        // 檢查是否有實際的滾動變化
        if (newScrollLeft !== currentScrollLeft || newScrollTop !== currentScrollTop) {
            // 執行滾動
            Dom.GameMap.scrollTo(newScrollLeft, newScrollTop);

            // 同步更新 Canvas 鏡頭位置
            this.updateCanvasCameraFromScroll(newScrollLeft, newScrollTop);
        } else {
            console.log(`已到達 ${direction} 方向的邊界，無法繼續滾動`);
        }
    },

    // 根據滾動位置更新 Canvas 鏡頭
    updateCanvasCameraFromScroll: function(scrollLeft, scrollTop) {
        if (typeof cameraX === 'undefined' || typeof cameraY === 'undefined') return;

        // 計算 Canvas 鏡頭位置（基於 DOM 滾動位置）
        const cellSize = 100; // 與 MoveComera 中的 cellSize 保持一致

        // 將 DOM 滾動位置轉換為 Canvas 鏡頭位置
        cameraX = (scrollLeft - 200) * (cellWidth / cellSize);
        cameraY = scrollTop * (cellHeight / cellSize);

        // 確保鏡頭不會超出地圖邊界
        const maxCameraX = Math.max(0, controlLayer[currentLevel].size.cols * cellWidth - (canvas ? canvas.width : 800));
        const maxCameraY = Math.max(0, controlLayer[currentLevel].size.rows * cellHeight - (canvas ? canvas.height : 600));

        cameraX = Math.max(0, Math.min(cameraX, maxCameraX));
        cameraY = Math.max(0, Math.min(cameraY, maxCameraY));

        // 重新渲染 Canvas
        if (typeof render === 'function') {
            render();
        }
    },

    // 清理邊界滾動
    cleanupBoundaryScrolling: function() {
        console.log("清理邊界滾動");

        // 停止滾動
        this.stopBoundaryScroll();

        // 移除事件監聽器
        if (this.boundaryScrollHandlers && Dom.GameMap) {
            Dom.GameMap.removeEventListener('mousemove', this.boundaryScrollHandlers.mousemove);
            Dom.GameMap.removeEventListener('mouseleave', this.boundaryScrollHandlers.mouseleave);
            this.boundaryScrollHandlers = null;
        }

        console.log("邊界滾動清理完成");
    }
}

let gameplayers = [];
let gameenemys = [];
let gameobstacles = [];
let gametreasures = [];

let Game = {
    init: function (skipMovie = false) {
        console.log("初始化關卡遊戲，跳過影片:", skipMovie);

        // 初始化Canvas
        initCanvas(controlLayer[currentLevel].size.cols, controlLayer[currentLevel].size.rows);

        // 檢查是否有開始影片需要播放且不跳過影片
        const currentLevelData = controlLayer[currentLevel];
        if (!skipMovie && currentLevelData.startmovie && currentLevelData.startmovie !== null) {
            console.log("檢測到關卡開始影片，準備播放");

            // 播放開始影片，影片結束後初始化遊戲
            this.playLevelStartMovie(currentLevelData.startmovie, () => {
                this.initializeGameAfterMovie();
            });
        } else {
            // 沒有開始影片或跳過影片，直接初始化遊戲
            console.log(skipMovie ? "跳過關卡開始影片" : "沒有關卡開始影片");
            this.initializeGameAfterMovie();
        }
    },

    // 影片播放後的遊戲初始化
    initializeGameAfterMovie: function () {
        console.log("開始初始化遊戲...");

        // 初始化遊戲操作
        operates.init();
        operates.initMap();
        operates.initMusic();
        operates.initSoundSettings();

        // 初始化設置按鈕事件
        this.initSettingButton();


        // 初始化遊戲元素
        this.initPlayers();
        this.initEnemys();
        this.initObstacles();
        this.initTreasure();
        this.initRender();
        this.initGpsBtn();

        // 設置勝利條件檢查
        this.setupVictoryCheck();

        // 初始化鏡頭位置
        console.log("初始化鏡頭位置...");

        // 重置 Canvas 鏡頭位置
        if (typeof cameraX !== 'undefined' && typeof cameraY !== 'undefined') {
            cameraX = 0;
            cameraY = 0;
        }

        // 設置初始鏡頭位置到第一個玩家
        if (gameplayers.length > 0) {
            let hasAIControlledPlayer = gameplayers.some(player => player["是否電腦操作"]);
            if (hasAIControlledPlayer) {
                let aiPlayer = gameplayers.find(player => player["是否電腦操作"]);
                console.log(`設置鏡頭到 AI 玩家位置: ${aiPlayer.Position}`);
                operates.MoveComera(aiPlayer.Position);

                // 同時更新 Canvas 鏡頭
                this.updateCanvasCamera(aiPlayer.Position);

                setTimeout(() => {
                    PlayerAIAction();
                }, 1000);
            } else {
                console.log(`設置鏡頭到第一個玩家位置: ${gameplayers[0].Position}`);
                setTimeout(() => {
                    operates.MoveComera(gameplayers[0].Position);
                });

                // 同時更新 Canvas 鏡頭
                this.updateCanvasCamera(gameplayers[0].Position);
            }
        } else {
            console.warn("沒有找到玩家，無法設置鏡頭位置");
        }
    },

    // 設置勝利條件檢查
    setupVictoryCheck: function () {
        // 定期檢查勝利條件
        this.victoryCheckInterval = setInterval(() => {
            sceneManager.checkVictoryCondition();
            sceneManager.checkDefeatCondition();
        }, 1000);
    },

    // 清理勝利條件檢查
    cleanup: function () {
        if (this.victoryCheckInterval) {
            clearInterval(this.victoryCheckInterval);
            this.victoryCheckInterval = null;
        }
    },

    initPlayers: function () {
        gameplayers = [];
        this.initPlayersWithSavedData();
    },
    initPlayersWithSavedData: function () {
        const playerDefaults = {
            "player0": Player0,
            "player1": Player1,
            "player2": Player2
        };

        // 從場景管理器獲取保存的玩家資料
        const savedPlayerData = sceneManager.loadPlayerData();

        controlLayer[currentLevel]["Players"].forEach((playerTemplate) => {
            // 優先使用場景管理器的資料，然後是舊的 allPlayers 資料
            const savedPlayer = savedPlayerData[playerTemplate.id] || allPlayers[playerTemplate.id];

            if (savedPlayer) {
                const basePlayer = playerDefaults[playerTemplate.id];
                const updatedPlayer = {
                    ...basePlayer,  // 先使用基礎玩家數據
                    ...savedPlayer, // 然後覆蓋保存的數據
                    Position: playerTemplate.Position,  // 使用關卡指定的位置
                    OldPosition: playerTemplate.Position,
                    AlreadyMove: false,
                    "是否蓄力": false,
                    "是否釋放蓄力": false,
                    "已增加移動力": false,
                    // 進入新關卡時恢復滿血滿藍
                    CurHP: savedPlayer.HP || basePlayer.HP,
                    CurMP: savedPlayer.MP || basePlayer.MP
                };
                gameplayers.push(updatedPlayer);

                // 同步更新到舊系統
                allPlayers[playerTemplate.id] = { ...updatedPlayer };
                console.log(`玩家 ${updatedPlayer.name} 進入關卡，HP: ${updatedPlayer.CurHP}/${updatedPlayer.HP}, MP: ${updatedPlayer.CurMP}/${updatedPlayer.MP}`);
            } else {
                const basePlayer = playerDefaults[playerTemplate.id];
                if (!basePlayer) {
                    console.error(`未找到 ID 為 ${playerTemplate.id} 的預設玩家數據`);
                    return;
                }
                const newPlayer = {
                    ...basePlayer,
                    id: playerTemplate.id,
                    Position: playerTemplate.Position,
                    OldPosition: playerTemplate.OldPosition,
                    AlreadyMove: false,
                    "是否蓄力": false,
                    "是否釋放蓄力": false,
                    "已增加移動力": false,
                    // 新玩家默認滿血滿藍
                    CurHP: basePlayer.HP,
                    CurMP: basePlayer.MP
                };
                gameplayers.push(newPlayer);
                allPlayers[playerTemplate.id] = { ...newPlayer };
                console.log(`新玩家 ${newPlayer.name} 進入關卡，HP: ${newPlayer.CurHP}/${newPlayer.HP}, MP: ${newPlayer.CurMP}/${newPlayer.MP}`);
            }
        });
        console.log("gameplayers", gameplayers);
        console.log("initPlayersWithSavedData", gameplayers);
    },
    initEnemys: function () {
        controlLayer[currentLevel]["Enemys"].forEach((enemy) => {
            gameenemys.push(enemy);
        });
    },
    initObstacles: function () {
        controlLayer[currentLevel]["Obstacles"].forEach((obstacle) => {
            gameobstacles.push(obstacle);
        });
    },
    initTreasure: function () {
        // 檢查是否有已拿取寶箱記錄
        const takenTreasures = typeof window.takenTreasures !== 'undefined' ? window.takenTreasures : new Set();

        if (takenTreasures.size > 0) {
            console.log(`初始化寶箱時過濾已拿取的寶箱: ${takenTreasures.size} 個位置`);
            // 只添加未拿取的寶箱
            controlLayer[currentLevel]["Treasures"].forEach((treasure) => {
                if (!takenTreasures.has(treasure["位置"])) {
                    gametreasures.push(treasure);
                } else {
                    console.log(`跳過已拿取的寶箱: 位置 ${treasure["位置"]}`);
                }
            });
        } else {
            // 沒有已拿取記錄，添加所有寶箱
            controlLayer[currentLevel]["Treasures"].forEach((treasure) => {
                gametreasures.push(treasure);
            });
        }

        console.log(`寶箱初始化完成: ${gametreasures.length} 個可拿取寶箱`);
    },
    initGpsBtn: function () {
        // 先清理已存在的 GPS 按鈕
        const existingGpsBtn = document.getElementById('GPSplayer');
        if (existingGpsBtn) {
            existingGpsBtn.remove();
            console.log("清理舊的 GPS 按鈕");
        }

        // 創建新的 GPS 按鈕
        let gpsbtn = document.createElement("div");
        gpsbtn.id = "GPSplayer";
        gpsbtn.textContent = "⛳";
        gpsbtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 100px;
            width: 70px;
            height: 70px;
            background: rgb(247, 231, 173);
            color: white;
            border: 5px solid #999;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 28px;
            z-index: 1000;
            user-select: none;
        `;

        document.body.appendChild(gpsbtn);
        console.log("GPS 按鈕已創建");

        let i = 0;
        gpsbtn.addEventListener('click', () => {
            let notmoveplayer = gameplayers.filter(player => player.AlreadyMove === false && player["是否電腦操作"] === false);
            if (notmoveplayer.length === 0 || runOBJ["當前操作"] !== null) return;

            if (i >= notmoveplayer.length - 1) {
                i = 0;
            } else {
                i++;
            }

            console.log(`GPS 按鈕點擊，切換到玩家 ${i}: ${notmoveplayer[i].name}`);
            operates.MoveComera(notmoveplayer[i].Position);
        });

        // 添加懸停效果
        gpsbtn.addEventListener('mouseenter', () => {
            gpsbtn.style.color = 'black';
        });

        gpsbtn.addEventListener('mouseleave', () => {
            gpsbtn.style.color = 'white';
        });
    },

    // 初始化設置按鈕
    initSettingButton: function () {
        if (Dom.SettingBtn) {
            Dom.SettingBtn.onclick = () => {
                // 檢查當前場景
                if (typeof sceneManager !== 'undefined') {
                    if (sceneManager.sceneType === 'camp') {
                        console.log("在營地場景中打開設置");
                    } else if (sceneManager.sceneType === 'level') {
                        console.log("在戰鬥場景中打開設置");
                    } else {
                        console.log("在其他場景中打開設置");
                    }
                }
                this.showSettingDialog();
            };
            console.log("設置按鈕事件已綁定");
        } else {
            console.warn("找不到設置按鈕元素");
        }
    },

    // 顯示設置對話框
    showSettingDialog: function () {
        // 檢查是否已經存在設置對話框
        const existingDialog = document.getElementById("setting-dialog");
        if (existingDialog) {
            existingDialog.showModal();
            return;
        }

        // 創建設置對話框
        const settingDialog = document.createElement("dialog");
        settingDialog.id = "setting-dialog";
        settingDialog.style.cssText = `
            background: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 10px;
            padding: 0;
            width: 700px;
            max-width: 90vw;
            backdrop-filter: blur(5px);
            margin: auto;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        `;

        settingDialog.innerHTML = `
            <div style="padding: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid rgb(165, 90, 24); padding-bottom: 10px;">
                    <h2 style="margin: 0; font-size: 24px;">遊戲設定</h2>
                    <button id="setting-close-btn" style="
                        background: rgb(165, 90, 24);
                        color: white;
                        border: none;
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        cursor: pointer;
                        font-size: 16px;
                    ">×</button>
                </div>

                <!-- 左右布局容器 -->
                <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <!-- 左側：遊戲設定 -->
                    <div style="flex: 1; min-width: 0;">
                        <h3 style="margin: 0 0 15px 0; font-size: 18px; color: rgb(165, 90, 24); border-bottom: 1px solid rgb(165, 90, 24); padding-bottom: 5px;">遊戲設定</h3>

                        <!-- 音樂控制區域 -->
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px; color: rgb(139, 69, 19);">🎵 背景音樂</h4>
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <button id="music-toggle-btn" style="
                                    background: rgb(165, 90, 24);
                                    color: white;
                                    border: none;
                                    padding: 8px 14px;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                    min-width: 80px;
                                ">🎵 暫停</button>
                                <span style="font-size: 14px; color: rgb(139, 69, 19);">播放控制</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label style="font-size: 14px; min-width: 45px; color: rgb(139, 69, 19);">音量:</label>
                                <input type="range" id="music-volume-slider" min="0" max="100" value="70" style="flex: 1;">
                                <span id="music-volume-display" style="font-size: 14px; min-width: 40px; color: rgb(139, 69, 19);">70%</span>
                            </div>
                        </div>

                        <!-- 音效控制區域 -->
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px; color: rgb(139, 69, 19);">🔊 遊戲音效</h4>
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                <button id="sound-toggle-btn" style="
                                    background: rgb(165, 90, 24);
                                    color: white;
                                    border: none;
                                    padding: 8px 14px;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                    min-width: 80px;
                                ">🔊 開啟</button>
                                <span style="font-size: 14px; color: rgb(139, 69, 19);">音效開關</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label style="font-size: 14px; min-width: 45px; color: rgb(139, 69, 19);">音量:</label>
                                <input type="range" id="sound-volume-slider" min="0" max="100" value="70" style="flex: 1;">
                                <span id="sound-volume-display" style="font-size: 14px; min-width: 40px; color: rgb(139, 69, 19);">70%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 右側：關卡條件 -->
                    <div style="flex: 1; min-width: 0;">
                        <h3 style="margin: 0 0 15px 0; font-size: 18px; color: rgb(165, 90, 24); border-bottom: 1px solid rgb(165, 90, 24); padding-bottom: 5px;">關卡條件</h3>

                        <!-- 勝利條件 -->
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                <span style="
                                    background: rgb(34, 139, 34);
                                    color: white;
                                    padding: 4px 10px;
                                    border-radius: 4px;
                                    font-size: 13px;
                                    font-weight: bold;
                                    margin-right: 10px;
                                    min-width: 45px;
                                    text-align: center;
                                ">勝利</span>
                                <span style="font-size: 15px; font-weight: 500; color: rgb(139, 69, 19);" id="victory-condition-text">載入中...</span>
                            </div>
                        </div>

                        <!-- 失敗條件 -->
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                <span style="
                                    background: rgb(220, 20, 60);
                                    color: white;
                                    padding: 4px 10px;
                                    border-radius: 4px;
                                    font-size: 13px;
                                    font-weight: bold;
                                    margin-right: 10px;
                                    min-width: 45px;
                                    text-align: center;
                                ">失敗</span>
                                <span style="font-size: 15px; font-weight: 500; color: rgb(139, 69, 19);" id="defeat-condition-text">載入中...</span>
                            </div>
                        </div>

                        <!-- 成就條件 -->
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; align-items: flex-start; margin-bottom: 8px;">
                                <span style="
                                    background: rgb(255, 165, 0);
                                    color: white;
                                    padding: 4px 10px;
                                    border-radius: 4px;
                                    font-size: 13px;
                                    font-weight: bold;
                                    margin-right: 10px;
                                    min-width: 45px;
                                    text-align: center;
                                    margin-top: 2px;
                                ">成就</span>
                                <div style="flex: 1;">
                                    <div style="font-size: 15px; font-weight: 500; line-height: 1.4; color: rgb(139, 69, 19); margin-bottom: 8px;" id="achievement-condition-text">載入中...</div>
                                    <!-- 成就進度條容器 -->
                                    <div id="achievement-progress-container" style="display: none;">
                                        <div style="margin-bottom: 6px;">
                                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                                <span style="font-size: 13px; color: rgb(139, 69, 19);">總體進度</span>
                                                <span style="font-size: 13px; color: rgb(139, 69, 19);" id="achievement-progress-text">0/0</span>
                                            </div>
                                            <div style="
                                                background: rgb(247, 231, 173);
                                                border: 1px solid rgb(165, 90, 24);
                                                border-radius: 8px;
                                                height: 12px;
                                                overflow: hidden;
                                            ">
                                                <div id="achievement-progress-bar" style="
                                                    background: linear-gradient(90deg, rgb(255, 165, 0), rgb(255, 140, 0));
                                                    height: 100%;
                                                    width: 0%;
                                                    transition: width 0.3s ease;
                                                    border-radius: 7px;
                                                "></div>
                                            </div>
                                        </div>
                                        <!-- 個別成就進度 -->
                                        <div id="individual-achievements" style="font-size: 13px; color: rgb(139, 69, 19);"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 關閉按鈕 -->
                <div style="text-align: center;">
                    <button id="setting-confirm-btn" style="
                        background: rgb(165, 90, 24);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 16px;
                    ">確定</button>
                </div>
            </div>
        `;

        document.body.appendChild(settingDialog);

        // 綁定事件
        this.bindSettingEvents(settingDialog);

        // 顯示對話框
        settingDialog.showModal();

        // 載入當前設置
        this.loadCurrentSettings();
    },

    // 綁定設置對話框事件
    bindSettingEvents: function (dialog) {
        // 關閉按鈕事件
        const closeBtn = dialog.querySelector("#setting-close-btn");
        const confirmBtn = dialog.querySelector("#setting-confirm-btn");

        closeBtn.onclick = () => {
            dialog.close();
        };

        confirmBtn.onclick = () => {
            dialog.close();
        };

        // 音樂控制事件
        const musicToggleBtn = dialog.querySelector("#music-toggle-btn");
        const musicVolumeSlider = dialog.querySelector("#music-volume-slider");
        const musicVolumeDisplay = dialog.querySelector("#music-volume-display");

        musicToggleBtn.onclick = () => {
            operates.toggleMusic();
            this.updateMusicToggleButton(musicToggleBtn);
        };

        musicVolumeSlider.oninput = () => {
            const volume = parseInt(musicVolumeSlider.value);
            operates.setMusicVolume(volume / 100);
            musicVolumeDisplay.textContent = volume + "%";
        };

        // 音效控制事件
        const soundToggleBtn = dialog.querySelector("#sound-toggle-btn");
        const soundVolumeSlider = dialog.querySelector("#sound-volume-slider");
        const soundVolumeDisplay = dialog.querySelector("#sound-volume-display");

        soundToggleBtn.onclick = () => {
            operates.toggleSoundEffects();
            this.updateSoundToggleButton(soundToggleBtn);
        };

        soundVolumeSlider.oninput = () => {
            const volume = parseInt(soundVolumeSlider.value);
            operates.setSoundVolume(volume / 100);
            soundVolumeDisplay.textContent = volume + "%";
        };

        // 測試按鈕已移除，不需要事件綁定

        // ESC 鍵關閉
        dialog.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                dialog.close();
            }
        });
    },

    // 載入當前設置
    loadCurrentSettings: function () {
        const dialog = document.getElementById("setting-dialog");
        if (!dialog) return;

        // 載入音樂設置
        const musicVolumeSlider = dialog.querySelector("#music-volume-slider");
        const musicVolumeDisplay = dialog.querySelector("#music-volume-display");
        const musicToggleBtn = dialog.querySelector("#music-toggle-btn");

        if (window.bgmAudio) {
            const volume = Math.round(window.bgmAudio.volume * 100);
            musicVolumeSlider.value = volume;
            musicVolumeDisplay.textContent = volume + "%";
        }

        this.updateMusicToggleButton(musicToggleBtn);

        // 載入音效設置
        const soundVolumeSlider = dialog.querySelector("#sound-volume-slider");
        const soundVolumeDisplay = dialog.querySelector("#sound-volume-display");
        const soundToggleBtn = dialog.querySelector("#sound-toggle-btn");

        const soundVolume = operates.getSoundVolume();
        soundVolumeSlider.value = Math.round(soundVolume * 100);
        soundVolumeDisplay.textContent = Math.round(soundVolume * 100) + "%";

        this.updateSoundToggleButton(soundToggleBtn);

        // 載入關卡條件
        this.loadLevelConditions();
    },

    // 載入關卡條件
    loadLevelConditions: function () {
        const dialog = document.getElementById("setting-dialog");
        if (!dialog) return;

        // 獲取當前關卡數據
        const currentLevelData = controlLayer[currentLevel];
        if (!currentLevelData) {
            console.warn("無法獲取當前關卡數據");
            return;
        }

        // 更新勝利條件
        const victoryText = dialog.querySelector("#victory-condition-text");
        if (victoryText) {
            victoryText.textContent = currentLevelData["勝利訊息"] || "無特定勝利條件";
        }

        // 更新失敗條件
        const defeatText = dialog.querySelector("#defeat-condition-text");
        if (defeatText) {
            let defeatMessage = currentLevelData["失敗訊息"] || "無特定失敗條件";
            // 移除末尾的逗號
            if (defeatMessage.endsWith(',')) {
                defeatMessage = defeatMessage.slice(0, -1);
            }
            defeatText.textContent = defeatMessage;
        }

        // 更新成就條件
        const achievementText = dialog.querySelector("#achievement-condition-text");
        const progressContainer = dialog.querySelector("#achievement-progress-container");

        if (achievementText) {
            const achievementMessage = currentLevelData["成就訊息"] || "無特定成就條件";

            if (achievementMessage === "無特定成就條件") {
                achievementText.textContent = achievementMessage;
                if (progressContainer) {
                    progressContainer.style.display = "none";
                }
            } else {
                // 將成就條件按逗號分割並格式化顯示
                if (achievementMessage.includes('，')) {
                    const achievements = achievementMessage.split('，');
                    let formattedText = '';
                    achievements.forEach((achievement, index) => {
                        if (achievement.trim()) {
                            formattedText += `• ${achievement.trim()}`;
                            if (index < achievements.length - 1) {
                                formattedText += '<br>';
                            }
                        }
                    });
                    achievementText.innerHTML = formattedText;

                    // 顯示進度條並初始化
                    if (progressContainer) {
                        console.log("準備初始化成就進度條，成就數量:", achievements.length);
                        this.initializeAchievementProgress(achievements);
                    } else {
                        console.warn("進度條容器未找到");
                    }
                } else {
                    achievementText.textContent = achievementMessage;
                    if (progressContainer) {
                        progressContainer.style.display = "none";
                    }
                }
            }
        }

        console.log("關卡條件已載入");
    },

    // 初始化成就進度條
    initializeAchievementProgress: function (achievements) {
        const dialog = document.getElementById("setting-dialog");
        if (!dialog) return;

        const progressContainer = dialog.querySelector("#achievement-progress-container");
        const progressBar = dialog.querySelector("#achievement-progress-bar");
        const progressText = dialog.querySelector("#achievement-progress-text");
        const individualContainer = dialog.querySelector("#individual-achievements");

        console.log("進度條元素檢查:");
        console.log("progressContainer:", progressContainer);
        console.log("progressBar:", progressBar);
        console.log("progressText:", progressText);
        console.log("individualContainer:", individualContainer);

        if (!progressContainer || !progressBar || !progressText || !individualContainer) {
            console.warn("部分進度條元素未找到，無法初始化進度條");
            return;
        }

        // 顯示進度容器
        progressContainer.style.display = "block";

        // 解析成就條件並檢查實際完成狀態
        const achievementList = [];
        achievements.forEach(achievement => {
            const trimmed = achievement.trim();
            if (trimmed) {
                // 提取成就名稱和分數
                const match = trimmed.match(/^(.+?)\(([+\-]?\d+)分\)$/);
                if (match) {
                    const achievementName = match[1];
                    const score = parseInt(match[2]);

                    // 檢查實際完成狀態
                    const completed = this.checkAchievementStatus(achievementName);

                    achievementList.push({
                        name: achievementName,
                        score: score,
                        completed: completed
                    });
                } else {
                    achievementList.push({
                        name: trimmed,
                        score: 0,
                        completed: false
                    });
                }
            }
        });

        // 更新總體進度
        const totalAchievements = achievementList.length;
        const completedAchievements = achievementList.filter(a => a.completed).length;
        const progressPercentage = totalAchievements > 0 ? (completedAchievements / totalAchievements) * 100 : 0;

        progressBar.style.width = `${progressPercentage}%`;
        progressText.textContent = `${completedAchievements}/${totalAchievements}`;

        // 更新個別成就狀態
        let individualHTML = '';
        achievementList.forEach((achievement) => {
            const statusIcon = achievement.completed ? '✅' : '⭕';
            const statusColor = achievement.completed ? 'rgb(34, 139, 34)' : 'rgb(139, 69, 19)';
            const scoreText = achievement.score !== 0 ? ` (${achievement.score > 0 ? '+' : ''}${achievement.score}分)` : '';

            individualHTML += `
                <div style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    padding: 3px 6px;
                    border-radius: 3px;
                    background: ${achievement.completed ? 'rgba(34, 139, 34, 0.1)' : 'transparent'};
                ">
                    <span style="margin-right: 6px; font-size: 12px;">${statusIcon}</span>
                    <span style="color: ${statusColor}; font-size: 12px; flex: 1;">${achievement.name}${scoreText}</span>
                </div>
            `;
        });

        individualContainer.innerHTML = individualHTML;

        console.log(`成就進度初始化完成: ${completedAchievements}/${totalAchievements}`);
    },

    // 檢查成就完成狀態
    checkAchievementStatus: function (achievementName) {
        console.log("檢查成就狀態:", achievementName);

        // 初始化成就追蹤系統（如果還沒有）
        if (!window.achievementTracker) {
            window.achievementTracker = {
                magicAttackCount: 0, // 受到敵人法術攻擊次數
                itemsCollected: new Set() // 收集到的物品
            };
        }

        // 檢查不同類型的成就
        if (achievementName.includes("紫蘊精玉")) {
            return this.checkItemInInventory("紫蘊精玉");
        } else if (achievementName.includes("受到敵人") && achievementName.includes("法術攻擊")) {
            // 提取需要的攻擊次數
            const match = achievementName.match(/受到敵人(\d+)次法術攻擊/);
            if (match) {
                const requiredCount = parseInt(match[1]);
                return window.achievementTracker.magicAttackCount >= requiredCount;
            }
            return false;
        } else if (achievementName.includes("燧石")) {
            // 檢查燧石數量
            const match = achievementName.match(/拿到(\d+)個燧石/);
            if (match) {
                const requiredCount = parseInt(match[1]);
                const actualCount = this.countItemInInventory("燧石");
                return actualCount >= requiredCount;
            }
            return false;
        } else if (achievementName.includes("位階")) {
            // 檢查角色等級
            const match = achievementName.match(/達到位階(\d+)以上/);
            if (match) {
                const requiredLevel = parseInt(match[1]);
                return this.checkPlayerLevels(requiredLevel);
            }
            return false;
        }

        return false;
    },

    // 檢查背包中是否有特定物品
    checkItemInInventory: function (itemName) {
        // 檢查所有玩家的背包
        if (typeof gameplayers !== 'undefined') {
            for (const player of gameplayers) {
                if (player.Inventory) {
                    for (const item of player.Inventory) {
                        if (item.name === itemName) {
                            console.log(`在 ${player.name} 的背包中找到 ${itemName}`);
                            return true;
                        }
                    }
                }
            }
        }

        // 檢查公共背包
        if (typeof mybag !== 'undefined') {
            for (const item of mybag) {
                if (item.name === itemName) {
                    console.log(`在公共背包中找到 ${itemName}`);
                    return true;
                }
            }
        }

        console.log(`未找到物品: ${itemName}`);
        return false;
    },

    // 計算背包中特定物品的數量
    countItemInInventory: function (itemName) {
        let count = 0;

        // 檢查所有玩家的背包
        if (typeof gameplayers !== 'undefined') {
            for (const player of gameplayers) {
                if (player.Inventory) {
                    for (const item of player.Inventory) {
                        if (item.name === itemName) {
                            count++;
                        }
                    }
                }
            }
        }

        // 檢查公共背包
        if (typeof mybag !== 'undefined') {
            for (const item of mybag) {
                if (item.name === itemName) {
                    count++;
                }
            }
        }

        console.log(`${itemName} 數量: ${count}`);
        return count;
    },

    // 檢查玩家等級
    checkPlayerLevels: function (requiredLevel) {
        if (typeof gameplayers !== 'undefined') {
            for (const player of gameplayers) {
                if (player.name === "殷劍平" || player.name === "封寒月") {
                    if (player.level >= requiredLevel) {
                        console.log(`${player.name} 等級 ${player.level} 達到要求 ${requiredLevel}`);
                        return true;
                    }
                }
            }
        }
        return false;
    },

    // 更新成就進度（供遊戲邏輯調用）
    updateAchievementProgress: function () {
        const dialog = document.getElementById("setting-dialog");
        if (!dialog) return;

        const progressContainer = dialog.querySelector("#achievement-progress-container");
        if (!progressContainer || progressContainer.style.display === "none") {
            // 如果進度條不可見，不需要更新
            return;
        }

        console.log("更新成就進度...");

        // 重新載入進度
        const currentLevelData = controlLayer[currentLevel];
        if (currentLevelData && currentLevelData["成就訊息"]) {
            const achievements = currentLevelData["成就訊息"].split('，');
            this.initializeAchievementProgress(achievements);
        }
    },

    // 載入遊戲存檔 - 支援多種存檔格式
    loadGame: function (slotIndex) {
        console.log(`載入遊戲存檔，槽位: ${slotIndex}`);

        try {
            // 首先嘗試載入 saveSlot 格式（Main.js saveGameFunc 使用的格式）
            const saveSlotKey = `saveSlot${slotIndex}`;
            const saveSlotData = localStorage.getItem(saveSlotKey);

            if (saveSlotData) {
                console.log("找到 saveSlot 格式存檔，開始載入...");
                const success = this.loadSaveSlotFormat(slotIndex, JSON.parse(saveSlotData));
                if (success) {
                    this.reloadBattleField();
                    console.log("saveSlot 格式存檔載入成功");
                    return true;
                }
            }

            // 如果沒有 saveSlot 格式，嘗試戰場存取系統格式
            if (typeof battleSaveManager !== 'undefined') {
                const battleState = battleSaveManager.loadBattleState(slotIndex);
                if (battleState) {
                    const success = battleSaveManager.applyBattleState(battleState);
                    if (success) {
                        this.reloadBattleField();
                        console.log("battleSave 格式存檔載入成功");
                        return true;
                    }
                }
            }

            console.warn(`槽位 ${slotIndex} 沒有可用的存檔數據`);
            return false;

        } catch (error) {
            console.error("載入遊戲存檔失敗:", error);
            return false;
        }
    },

    // 清理所有動畫和物件
    clearAllAnimations: function() {
        console.log("清理所有動畫和物件...");

        try {
            // 清理 mapObjects 中的所有動畫
            if (typeof mapObjects !== 'undefined' && Array.isArray(mapObjects)) {
                console.log(`清理 ${mapObjects.length} 個 mapObjects 中的動畫`);
                mapObjects.forEach((obj, index) => {
                    if (obj.isStandAnimating) {
                        console.log(`停止 ${obj.type} ${obj.name || index} 的站立動畫`);
                        obj.isStandAnimating = false;
                        if (obj.animationId) {
                            cancelAnimationFrame(obj.animationId);
                            obj.animationId = null;
                        }
                    }

                    // 清理其他動畫相關屬性
                    if (obj.isCasting) {
                        obj.isCasting = false;
                    }
                    if (obj.isMoving) {
                        obj.isMoving = false;
                    }
                });

                // 清空 mapObjects 陣列
                mapObjects.length = 0;
                console.log("mapObjects 已清空");
            }

            // 清理高亮格子
            if (typeof highlightCells !== 'undefined' && Array.isArray(highlightCells)) {
                highlightCells.length = 0;
                console.log("highlightCells 已清空");
            }

            // 清理移動目標格子
            if (typeof moveTargetCells !== 'undefined') {
                if (moveTargetCells.clear) {
                    moveTargetCells.clear();
                } else if (Array.isArray(moveTargetCells)) {
                    moveTargetCells.length = 0;
                }
                console.log("moveTargetCells 已清空");
            }

            // 重置當前移動玩家索引
            if (typeof currentMovePlayerIdx !== 'undefined') {
                currentMovePlayerIdx = null;
                console.log("currentMovePlayerIdx 已重置");
            }

            // 注意：不要過度清理 requestAnimationFrame，因為可能會影響其他動畫
            console.log("動畫清理完成，保留系統動畫");

            console.log("動畫和物件清理完成");
        } catch (error) {
            console.error("清理動畫和物件時出錯:", error);
        }
    },

    // 載入 saveSlot 格式的存檔
    loadSaveSlotFormat: function(slotIndex, saveData) {
        console.log("載入 saveSlot 格式存檔:", saveData);

        try {
            // 切換到對應關卡
            if (typeof saveData.currentLevel !== 'undefined') {
                currentLevel = saveData.currentLevel;
                console.log(`切換到關卡 ${currentLevel}`);
            }

            // 載入玩家數據
            if (saveData.allPlayers) {
                // 清空現有玩家數據
                gameplayers.length = 0;
                console.log("清空現有玩家陣列");

                // 玩家預設數據
                const playerDefaults = {
                    "player0": Player0,
                    "player1": Player1,
                    "player2": Player2
                };

                // 將存檔中的玩家物件轉換為陣列格式
                Object.keys(saveData.allPlayers).forEach(playerId => {
                    const savedPlayerData = saveData.allPlayers[playerId];
                    const defaultPlayerData = playerDefaults[playerId];

                    // 合併預設數據和存檔數據，確保包含 Stand 動畫數據
                    const completePlayerData = {
                        ...defaultPlayerData,  // 先使用預設數據（包含 Stand 屬性）
                        ...savedPlayerData,    // 然後覆蓋存檔數據
                        id: playerId,
                        // 保持存檔中的移動狀態，不強制重置
                        AlreadyMove: savedPlayerData.AlreadyMove !== undefined ?
                            savedPlayerData.AlreadyMove : false,
                        "是否蓄力": savedPlayerData["是否蓄力"] !== undefined ?
                            savedPlayerData["是否蓄力"] : false,
                        "是否釋放蓄力": savedPlayerData["是否釋放蓄力"] !== undefined ?
                            savedPlayerData["是否釋放蓄力"] : false,
                        "已增加移動力": savedPlayerData["已增加移動力"] !== undefined ?
                            savedPlayerData["已增加移動力"] : false,
                        // 確保正確載入重要屬性
                        "是否電腦操作": savedPlayerData["是否電腦操作"] !== undefined ?
                            savedPlayerData["是否電腦操作"] :
                            (defaultPlayerData["是否電腦操作"] || false),
                        Position: savedPlayerData.Position !== undefined ?
                            savedPlayerData.Position :
                            (defaultPlayerData.Position || 0),
                        OldPosition: savedPlayerData.OldPosition !== undefined ?
                            savedPlayerData.OldPosition :
                            (defaultPlayerData.OldPosition || savedPlayerData.Position || defaultPlayerData.Position || 0),
                        atkrange: savedPlayerData.atkrange !== undefined ?
                            savedPlayerData.atkrange :
                            (defaultPlayerData.atkrange || 1),
                        // 確保正確載入法術數據，包括 distance 和 Rmdistance
                        "法術": savedPlayerData["法術"] ?
                            savedPlayerData["法術"].map(savedMagic => {
                                // 從預設數據中找到對應的法術
                                const defaultMagic = defaultPlayerData["法術"]?.find(m => m.name === savedMagic.name);
                                return {
                                    ...defaultMagic, // 先使用預設數據
                                    ...savedMagic,   // 然後覆蓋存檔數據
                                    // 確保關鍵屬性被正確載入
                                    distance: savedMagic.distance !== undefined ?
                                        savedMagic.distance :
                                        (defaultMagic?.distance || 0),
                                    Rmdistance: savedMagic.Rmdistance !== undefined ?
                                        savedMagic.Rmdistance :
                                        (defaultMagic?.Rmdistance || 0),
                                    Range: savedMagic.Range !== undefined ?
                                        savedMagic.Range :
                                        (defaultMagic?.Range || 0)
                                };
                            }) :
                            (defaultPlayerData["法術"] || [])
                    };

                    allPlayers[playerId] = { ...completePlayerData };
                    gameplayers.push(completePlayerData);
                    console.log(`載入玩家 ${playerId}:`, completePlayerData.name);
                    console.log(`  Stand 動畫: ${completePlayerData.Stand ? '有' : '無'}`);
                });

                // 同步到場景管理器
                if (typeof sceneManager !== 'undefined') {
                    sceneManager.gameData.playerData = { ...allPlayers };
                }
                console.log(`總共載入 ${gameplayers.length} 個玩家`);
            }

            // 載入敵人數據
            if (saveData.enemy) {
                // 清空現有敵人陣列
                gameenemys.length = 0;
                console.log("清空現有敵人陣列");

                // 敵人預設數據
                const enemyDefaults = {
                    "Enemy0": Enemy0,
                    "Enemy1": Enemy1
                };

                Object.keys(saveData.enemy).forEach(enemyId => {
                    const savedEnemyData = saveData.enemy[enemyId];

                    // 嘗試根據敵人名稱找到對應的預設數據
                    let defaultEnemyData = null;
                    if (savedEnemyData.name === "夜魑") {
                        defaultEnemyData = Enemy0;
                    } else if (savedEnemyData.name === "四象門術") {
                        defaultEnemyData = Enemy1;
                    }

                    // 合併預設數據和存檔數據，確保包含 Stand 動畫數據
                    const completeEnemyData = {
                        ...(defaultEnemyData || {}),  // 先使用預設數據（包含 Stand 屬性）
                        ...savedEnemyData,            // 然後覆蓋存檔數據
                        AlreadyMove: false,
                        "是否蓄力": false,
                        "是否釋放蓄力": false
                    };

                    gameenemys.push(completeEnemyData);
                    console.log(`載入敵人 ${enemyId}:`, completeEnemyData.name);
                    console.log(`  Stand 動畫: ${completeEnemyData.Stand ? '有' : '無'}`);
                });
                console.log(`總共載入 ${gameenemys.length} 個敵人`);
            }

            // 載入已拿取寶箱記錄
            if (saveData.takenTreasures) {
                window.takenTreasures = new Set(saveData.takenTreasures);
                console.log(`載入已拿取寶箱記錄: ${saveData.takenTreasures.length} 個位置`);
            } else {
                window.takenTreasures = new Set();
            }

            // 載入寶箱數據，過濾掉已拿取的寶箱
            if (saveData.treasures) {
                // 只載入未拿取的寶箱
                gametreasures = saveData.treasures.filter(treasure => !treasure["是否已拿取"]);
                console.log(`載入寶箱數據: 總共 ${saveData.treasures.length} 個寶箱，其中 ${gametreasures.length} 個未拿取`);

                // 記錄已拿取的寶箱數量
                const takenTreasures = saveData.treasures.filter(treasure => treasure["是否已拿取"]);
                if (takenTreasures.length > 0) {
                    console.log(`已拿取的寶箱: ${takenTreasures.length} 個`);
                    takenTreasures.forEach(treasure => {
                        console.log(`- 位置 ${treasure["位置"]}: ${treasure["寶物"].name}`);
                    });
                }
            }

            // 載入遊戲狀態
            if (saveData.currentRound) {
                runOBJ["回合"] = saveData.currentRound;
                console.log(`載入回合數: ${saveData.currentRound}`);
            }

            // 重置遊戲狀態
            runOBJ["當前行動方"] = "Players";
            runOBJ["當前操作"] = null;
            runOBJ["當前選取"] = null;
            runOBJ["第二次選取"] = null;
            runOBJ["當前物品操作"] = null;
            runOBJ["當前選取物品"] = null;
            runOBJ["是否有行走"] = false;

            console.log("saveSlot 格式存檔載入完成");
            return true;

        } catch (error) {
            console.error("載入 saveSlot 格式存檔失敗:", error);
            return false;
        }
    },

    // 重新載入戰場 - 在讀取存檔後重新初始化遊戲畫面
    reloadBattleField: function() {
        console.log("重新載入戰場");

        try {
            // 清理現有的遊戲狀態
            if (this.victoryCheckInterval) {
                clearInterval(this.victoryCheckInterval);
                this.victoryCheckInterval = null;
            }

            // 重新初始化遊戲
            console.log("重新初始化遊戲...");

            // 清理現有的動畫和物件
            console.log("清理現有的動畫和物件...");
            this.clearAllAnimations();

            // 重新初始化Canvas
            if (typeof initCanvas === 'function' && typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined') {
                initCanvas(controlLayer[currentLevel].size.cols, controlLayer[currentLevel].size.rows);
            }

            // 重新初始化遊戲操作（不包括 initMap，避免重複調用 updateMapObjects）
            if (typeof operates !== 'undefined') {
                console.log("重新初始化遊戲操作和音樂...");
                operates.init();
                operates.initMusic();
                operates.initSoundSettings();

                // 手動設置背景，避免調用 operates.initMap()
                console.log("設置背景圖片和地圖尺寸...");
                Dom.GameBoard.style.width = controlLayer[currentLevel]["width"];
                Dom.GameBoard.style.height = controlLayer[currentLevel]["height"];
                Dom.GameBoard.style.backgroundImage = `url(${controlLayer[currentLevel]["地圖背景"]})`;
                Dom.GameBoard.style.gridTemplateColumns = `repeat(${controlLayer[currentLevel].size.cols}, 1fr)`;
                Dom.GameBoard.style.gridTemplateRows = `repeat(${controlLayer[currentLevel].size.rows}, 1fr)`;
            }

            // 確保遊戲狀態正確初始化
            console.log("確保遊戲狀態正確初始化...");
            this.ensureGameStateInitialized();

            // 重新初始化遊戲元素（存檔數據已經載入到 gameplayers 和 gameenemys）
            console.log("重新初始化遊戲元素...");
            this.initObstacles();
            // 注意：不要重新初始化寶箱，因為已經在載入存檔時過濾了已拿取的寶箱
            console.log("跳過寶箱重新初始化，保持載入的寶箱狀態");
            this.initGpsBtn();

            // 設置勝利條件檢查
            this.setupVictoryCheck();

            // 重新初始化鏡頭位置
            console.log("重新初始化鏡頭位置...");
            if (typeof cameraX !== 'undefined' && typeof cameraY !== 'undefined') {
                cameraX = 0;
                cameraY = 0;
            }

            // 只調用一次 updateMapObjects 來創建角色動畫
            console.log("更新地圖物件和角色動畫...");
            if (typeof updateMapObjects === 'function') {
                updateMapObjects();
            }

            // 設置初始鏡頭位置到第一個玩家
            if (gameplayers.length > 0) {
                console.log(`設置鏡頭到第一個玩家位置: ${gameplayers[0].Position}`);
                setTimeout(() => {
                    operates.MoveComera(gameplayers[0].Position);
                }, 100);
            }

            // 重新綁定 Canvas 點擊事件
            console.log("重新綁定 Canvas 點擊事件...");
            this.rebindCanvasEvents();

            //重新初始化滑鼠邊界滾動
            console.log("重新初始化滑鼠邊界滾動...");
            if (typeof operates !== 'undefined' && typeof operates.initBoundaryScrolling === 'function') {
                operates.initBoundaryScrolling();
            }

            // 最後進行一次渲染
            console.log("執行最終渲染...");
            if (typeof render === 'function') {
                render();
            }

            console.log("戰場重新載入完成");
        } catch (error) {
            console.error("重新載入戰場失敗:", error);
        }
    },

    // 重新綁定 Canvas 事件
    rebindCanvasEvents: function() {
        console.log("重新綁定 Canvas 事件");

        try {
            // 確保 canvas 元素存在
            if (typeof canvas === 'undefined' || !canvas) {
                console.error("Canvas 元素不存在，無法綁定事件");
                return;
            }

            // 清理所有現有的事件監聽器
            console.log("清理現有的事件監聽器...");
            this.clearAllEventListeners();

            // 重新綁定主要的點擊事件
            canvas.addEventListener('click', handleClick);
            console.log("Canvas 點擊事件已重新綁定");

            // 確保 handleClick 函數存在
            if (typeof handleClick !== 'function') {
                console.error("handleClick 函數不存在");
                return;
            }

            // 檢查遊戲狀態是否正確
            console.log("檢查遊戲狀態:");
            console.log(`  當前行動方: ${runOBJ["當前行動方"]}`);
            console.log(`  當前操作: ${runOBJ["當前操作"]}`);
            console.log(`  當前選取: ${runOBJ["當前選取"]}`);
            console.log(`  玩家數量: ${gameplayers ? gameplayers.length : 0}`);
            console.log(`  敵人數量: ${gameenemys ? gameenemys.length : 0}`);

            // 驗證玩家數據
            if (gameplayers && gameplayers.length > 0) {
                console.log("玩家數據驗證:");
                gameplayers.forEach((player, index) => {
                    console.log(`  玩家 ${index}: ${player.name}, 位置: ${player.Position}, HP: ${player.CurHP}/${player.HP}`);
                });
            }

        } catch (error) {
            console.error("重新綁定 Canvas 事件失敗:", error);
        }
    },

    // 清理所有事件監聽器
    clearAllEventListeners: function() {
        console.log("清理所有事件監聽器");

        try {
            // 移除主要的點擊事件
            canvas.removeEventListener('click', handleClick);

            // 清理攻擊相關的事件監聽器
            if (window.currentAttackClickHandler) {
                canvas.removeEventListener('click', window.currentAttackClickHandler);
                window.currentAttackClickHandler = null;
                console.log("已清除攻擊點擊事件處理器");
            }

            // 清理物品使用相關的事件監聽器
            if (window.currentItemUseClickHandler) {
                canvas.removeEventListener('click', window.currentItemUseClickHandler);
                window.currentItemUseClickHandler = null;
                console.log("已清除物品使用點擊事件處理器");
            }

            if (window.currentItemUseMouseMoveHandler) {
                canvas.removeEventListener('mousemove', window.currentItemUseMouseMoveHandler);
                window.currentItemUseMouseMoveHandler = null;
                console.log("已清除物品使用滑鼠移動事件處理器");
            }

            // 清理法術相關的事件監聽器
            if (window.currentMagicUseClickHandler) {
                canvas.removeEventListener('click', window.currentMagicUseClickHandler);
                window.currentMagicUseClickHandler = null;
                console.log("已清除法術使用點擊事件處理器");
            }

            if (window.currentMagicUseMouseMoveHandler) {
                canvas.removeEventListener('mousemove', window.currentMagicUseMouseMoveHandler);
                window.currentMagicUseMouseMoveHandler = null;
                console.log("已清除法術使用滑鼠移動事件處理器");
            }

            // 清理法術範圍高亮
            if (typeof clearAllMagicRangeHighlights === 'function') {
                clearAllMagicRangeHighlights();
                console.log("已清除法術範圍高亮");
            }

            console.log("事件監聽器清理完成");
        } catch (error) {
            console.error("清理事件監聽器失敗:", error);
        }
    },

    // 確保遊戲狀態正確初始化
    ensureGameStateInitialized: function() {
        console.log("確保遊戲狀態正確初始化");

        try {
            // 確保 runOBJ 存在且有正確的初始狀態
            if (typeof runOBJ === 'undefined') {
                console.error("runOBJ 未定義，無法初始化遊戲狀態");
                return;
            }

            // 確保基本狀態正確
            if (!runOBJ["當前行動方"]) {
                runOBJ["當前行動方"] = "Players";
                console.log("設置當前行動方為 Players");
            }

            if (runOBJ["當前操作"] === undefined) {
                runOBJ["當前操作"] = null;
                console.log("重置當前操作為 null");
            }

            if (runOBJ["當前選取"] === undefined) {
                runOBJ["當前選取"] = null;
                console.log("重置當前選取為 null");
            }

            if (runOBJ["第二次選取"] === undefined) {
                runOBJ["第二次選取"] = null;
                console.log("重置第二次選取為 null");
            }

            // 清理可能存在的臨時狀態
            runOBJ["當前物品操作"] = null;
            runOBJ["當前選取物品"] = null;

            // 確保回合數存在
            if (!runOBJ["回合"]) {
                runOBJ["回合"] = 1;
                console.log("設置回合數為 1");
            }

            // 檢查是否為讀檔後的初始化（不重置移動狀態）
            const isLoadGameInitialization = gameplayers && gameplayers.some(player =>
                player.AlreadyMove === true ||
                player.Position !== player.OldPosition ||
                player["是否蓄力"] === true ||
                player["是否釋放蓄力"] === true ||
                player["已增加移動力"] === true
            );

            if (isLoadGameInitialization) {
                console.log("檢測到讀檔後初始化，保持玩家的移動狀態不變");
                // 只確保必要的屬性存在，不重置狀態
                if (gameplayers && gameplayers.length > 0) {
                    gameplayers.forEach(player => {
                        // 確保屬性存在但不重置值
                        if (player.AlreadyMove === undefined) player.AlreadyMove = false;
                        if (player["是否蓄力"] === undefined) player["是否蓄力"] = false;
                        if (player["是否釋放蓄力"] === undefined) player["是否釋放蓄力"] = false;
                        if (player["已增加移動力"] === undefined) player["已增加移動力"] = false;
                    });
                    console.log("確保玩家屬性完整性，保持原有狀態");
                }

                if (gameenemys && gameenemys.length > 0) {
                    gameenemys.forEach(enemy => {
                        // 確保屬性存在但不重置值
                        if (enemy.AlreadyMove === undefined) enemy.AlreadyMove = false;
                        if (enemy["是否蓄力"] === undefined) enemy["是否蓄力"] = false;
                        if (enemy["是否釋放蓄力"] === undefined) enemy["是否釋放蓄力"] = false;
                    });
                    console.log("確保敵人屬性完整性，保持原有狀態");
                }
            } else {
                console.log("新遊戲初始化，重置所有移動狀態");
                // 只有在新遊戲時才重置移動狀態
                if (gameplayers && gameplayers.length > 0) {
                    gameplayers.forEach(player => {
                        player.AlreadyMove = false;
                        player["是否蓄力"] = false;
                        player["是否釋放蓄力"] = false;
                        player["已增加移動力"] = false;
                    });
                    console.log("重置所有玩家的移動狀態");
                }

                if (gameenemys && gameenemys.length > 0) {
                    gameenemys.forEach(enemy => {
                        enemy.AlreadyMove = false;
                        enemy["是否蓄力"] = false;
                        enemy["是否釋放蓄力"] = false;
                    });
                    console.log("重置所有敵人的移動狀態");
                }
            }

            // 清理高亮狀態和法術相關狀態
            if (typeof clearAllHighlights === 'function') {
                clearAllHighlights();
            }

            // 清理法術相關的事件監聽器和高亮
            if (typeof clearAllMagicEventHandlers === 'function') {
                clearAllMagicEventHandlers();
                console.log("已清理法術事件監聽器");
            }

            if (typeof clearAllMagicRangeHighlights === 'function') {
                clearAllMagicRangeHighlights();
                console.log("已清理法術範圍高亮");
            }

            console.log("遊戲狀態初始化完成");
            console.log(`最終狀態 - 行動方: ${runOBJ["當前行動方"]}, 操作: ${runOBJ["當前操作"]}, 選取: ${runOBJ["當前選取"]}`);

        } catch (error) {
            console.error("確保遊戲狀態初始化失敗:", error);
        }
    },

    // 手動觸發成就檢查（用於測試）
    checkAllAchievements: function () {
        console.log("=== 手動檢查所有成就狀態 ===");

        if (!window.achievementTracker) {
            window.achievementTracker = {
                magicAttackCount: 0,
                itemsCollected: new Set()
            };
        }

        console.log("當前成就追蹤狀態:");
        console.log("- 法術攻擊次數:", window.achievementTracker.magicAttackCount);
        console.log("- 收集物品:", Array.from(window.achievementTracker.itemsCollected));

        // 檢查背包狀態
        console.log("背包檢查:");
        console.log("- 紫蘊精玉:", this.checkItemInInventory("紫蘊精玉") ? "已獲得" : "未獲得");
        console.log("- 燧石數量:", this.countItemInInventory("燧石"));

        // 檢查玩家等級
        if (typeof gameplayers !== 'undefined') {
            gameplayers.forEach(player => {
                if (player.name === "殷劍平" || player.name === "封寒月") {
                    console.log(`- ${player.name} 等級: ${player.level || 1}`);
                }
            });
        }

        // 更新進度條
        this.updateAchievementProgress();

        console.log("=== 成就檢查完成 ===");
    },

    // 更新音樂切換按鈕
    updateMusicToggleButton: function (button) {
        if (!window.bgmAudio) {
            button.innerHTML = "🔇 未載入";
            button.disabled = true;
            return;
        }

        if (window.bgmAudio.paused) {
            button.innerHTML = "▶️ 播放";
        } else {
            button.innerHTML = "⏸️ 暫停";
        }
        button.disabled = false;
    },

    // 更新音效切換按鈕
    updateSoundToggleButton: function (button) {
        const isEnabled = operates.isSoundEnabled();
        if (isEnabled) {
            button.innerHTML = "🔊 開啟";
        } else {
            button.innerHTML = "🔇 關閉";
        }
    },

    // ===== 關卡影片播放功能 =====

    // 播放關卡開始影片
    playLevelStartMovie: function (moviePath, callback) {
        if (!moviePath || moviePath === null) {
            console.log("沒有開始影片，直接進入關卡");
            if (callback) callback();
            return;
        }

        console.log("播放關卡開始影片:", moviePath);

        // 停止背景音樂（如果有的話）
        if (window.bgmAudio) {
            window.bgmAudio.pause();
            console.log("背景音樂已停止");
        }

        // 開始影片也使用無控制的播放器
        this.createStartMoviePlayer(moviePath, callback);
    },

    // 創建開始影片播放器（無控制，只能跳過）
    createStartMoviePlayer: function (moviePath, callback) {
        // 創建全螢幕容器
        const movieContainer = document.createElement("div");
        movieContainer.id = "start-movie-container";
        movieContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: black;
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        // 創建影片元素
        const video = document.createElement("video");
        video.id = "start-level-movie";
        video.src = moviePath;
        video.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
        `;
        video.autoplay = true;
        video.controls = false; // 隱藏預設控制項

        // 創建跳過按鈕
        const skipButton = document.createElement("button");
        skipButton.id = "skip-start-movie-btn";
        skipButton.innerHTML = "跳過";
        skipButton.style.cssText = `
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 2px solid white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 10001;
            transition: background 0.3s ease;
        `;

        // 跳過按鈕懸停效果
        skipButton.addEventListener("mouseenter", () => {
            skipButton.style.background = "rgba(255, 255, 255, 0.2)";
        });

        skipButton.addEventListener("mouseleave", () => {
            skipButton.style.background = "rgba(0, 0, 0, 0.7)";
        });

        // 組裝元素（不添加標題）
        movieContainer.appendChild(video);
        movieContainer.appendChild(skipButton);
        document.body.appendChild(movieContainer);

        // 影片結束處理函數
        const endMovie = () => {
            console.log("開始影片播放完成");

            // 移除影片容器
            if (movieContainer.parentNode) {
                movieContainer.parentNode.removeChild(movieContainer);
            }

            // 執行回調
            if (callback) {
                callback();
            }
        };

        // 綁定事件
        video.addEventListener("ended", endMovie);
        video.addEventListener("error", (e) => {
            console.error("開始影片播放錯誤:", e);
            alert(`開始影片播放失敗: ${moviePath}`);
            endMovie();
        });

        // 跳過按鈕點擊事件
        skipButton.addEventListener("click", () => {
            console.log("用戶點擊跳過開始影片");
            video.pause();
            endMovie();
        });

        // ESC 鍵跳過
        const escHandler = (e) => {
            if (e.key === "Escape") {
                console.log("用戶按 ESC 跳過開始影片");
                video.pause();
                endMovie();
                document.removeEventListener("keydown", escHandler);
            }
        };
        document.addEventListener("keydown", escHandler);

        // 移除點擊影片暫停/播放功能（開始影片也不允許控制）
        // video.addEventListener("click", () => { ... }); // 不添加這個事件

        // 嘗試播放影片
        const playPromise = video.play();
        if (playPromise !== undefined) {
            playPromise.catch(error => {
                console.error("自動播放開始影片失敗:", error);
                // 顯示播放提示
                this.showStartMoviePlayPrompt(video);
            });
        }
    },

    // 顯示開始影片播放提示
    showStartMoviePlayPrompt: function (video) {
        const prompt = document.createElement("div");
        prompt.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10002;
            cursor: pointer;
        `;
        prompt.innerHTML = `
            <div style="margin-bottom: 10px;">🎬</div>
            <div>點擊播放開始影片</div>
        `;

        const movieContainer = document.getElementById("start-movie-container");
        if (movieContainer) {
            movieContainer.appendChild(prompt);

            prompt.addEventListener("click", () => {
                video.play().then(() => {
                    if (prompt.parentNode) {
                        prompt.parentNode.removeChild(prompt);
                    }
                });
            });
        }
    },

    // 播放關卡結束影片
    playLevelEndMovie: function (moviePath, callback) {
        if (!moviePath || moviePath === null) {
            console.log("沒有結束影片，直接執行回調");
            if (callback) callback();
            return;
        }

        console.log("播放關卡結束影片:", moviePath);

        // 停止關卡背景音樂
        if (window.bgmAudio) {
            window.bgmAudio.pause();
            console.log("關卡音樂已停止");
        }

        // 創建結束影片播放器（無控制，只能跳過，無標題）
        this.createEndMoviePlayer(moviePath, callback);
    },

    // 創建結束影片播放器（無控制，只能跳過）
    createEndMoviePlayer: function (moviePath, callback) {
        // 創建全螢幕容器
        const movieContainer = document.createElement("div");
        movieContainer.id = "end-movie-container";
        movieContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: black;
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        // 創建影片元素
        const video = document.createElement("video");
        video.id = "end-level-movie";
        video.src = moviePath;
        video.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
        `;
        video.autoplay = true;
        video.controls = false; // 隱藏預設控制項

        // 創建跳過按鈕
        const skipButton = document.createElement("button");
        skipButton.id = "skip-end-movie-btn";
        skipButton.innerHTML = "跳過";
        skipButton.style.cssText = `
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 2px solid white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 10001;
            transition: background 0.3s ease;
        `;

        // 跳過按鈕懸停效果
        skipButton.addEventListener("mouseenter", () => {
            skipButton.style.background = "rgba(255, 255, 255, 0.2)";
        });

        skipButton.addEventListener("mouseleave", () => {
            skipButton.style.background = "rgba(0, 0, 0, 0.7)";
        });

        // 組裝元素（不添加標題）
        movieContainer.appendChild(video);
        movieContainer.appendChild(skipButton);
        document.body.appendChild(movieContainer);

        // 影片結束處理函數
        const endMovie = () => {
            console.log("結束影片播放完成");

            // 移除影片容器
            if (movieContainer.parentNode) {
                movieContainer.parentNode.removeChild(movieContainer);
            }

            // 執行回調
            if (callback) {
                callback();
            }
        };

        // 綁定事件
        video.addEventListener("ended", endMovie);
        video.addEventListener("error", (e) => {
            console.error("結束影片播放錯誤:", e);
            alert(`結束影片播放失敗: ${moviePath}`);
            endMovie();
        });

        // 跳過按鈕點擊事件
        skipButton.addEventListener("click", () => {
            console.log("用戶點擊跳過結束影片");
            video.pause();
            endMovie();
        });

        // ESC 鍵跳過
        const escHandler = (e) => {
            if (e.key === "Escape") {
                console.log("用戶按 ESC 跳過結束影片");
                video.pause();
                endMovie();
                document.removeEventListener("keydown", escHandler);
            }
        };
        document.addEventListener("keydown", escHandler);

        // 移除點擊影片暫停/播放功能（結束影片不允許控制）
        // video.addEventListener("click", () => { ... }); // 不添加這個事件

        // 嘗試播放影片
        const playPromise = video.play();
        if (playPromise !== undefined) {
            playPromise.catch(error => {
                console.error("自動播放結束影片失敗:", error);
                // 顯示播放提示
                this.showEndMoviePlayPrompt(video);
            });
        }
    },

    // 顯示結束影片播放提示
    showEndMoviePlayPrompt: function (video) {
        const prompt = document.createElement("div");
        prompt.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10002;
            cursor: pointer;
        `;
        prompt.innerHTML = `
            <div style="margin-bottom: 10px;">🎬</div>
            <div>點擊播放結束影片</div>
        `;

        const movieContainer = document.getElementById("end-movie-container");
        if (movieContainer) {
            movieContainer.appendChild(prompt);

            prompt.addEventListener("click", () => {
                video.play().then(() => {
                    if (prompt.parentNode) {
                        prompt.parentNode.removeChild(prompt);
                    }
                });
            });
        }
    },

    // 創建影片播放器
    createMoviePlayer: function (moviePath, callback, title = "影片播放") {
        // 創建全螢幕容器
        const movieContainer = document.createElement("div");
        movieContainer.id = "movie-container";
        movieContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: black;
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        // 創建影片元素
        const video = document.createElement("video");
        video.id = "level-movie";
        video.src = moviePath;
        video.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
        `;
        video.autoplay = true;
        video.controls = false; // 隱藏預設控制項

        // 創建跳過按鈕
        const skipButton = document.createElement("button");
        skipButton.id = "skip-movie-btn";
        skipButton.innerHTML = "跳過";
        skipButton.style.cssText = `
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 2px solid white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 10001;
            transition: background 0.3s ease;
        `;

        // 跳過按鈕懸停效果
        skipButton.addEventListener("mouseenter", () => {
            skipButton.style.background = "rgba(255, 255, 255, 0.2)";
        });

        skipButton.addEventListener("mouseleave", () => {
            skipButton.style.background = "rgba(0, 0, 0, 0.7)";
        });

        // 組裝元素
        movieContainer.appendChild(video);
        movieContainer.appendChild(skipButton);

        // 只有開始影片才顯示標題
        if (title && title.includes("開始")) {
            const movieTitle = document.createElement("div");
            movieTitle.style.cssText = `
                position: absolute;
                bottom: 30px;
                left: 50%;
                transform: translateX(-50%);
                color: white;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                z-index: 10001;
            `;
            movieTitle.textContent = title;
            movieContainer.appendChild(movieTitle);
        }
        document.body.appendChild(movieContainer);

        // 影片結束處理函數
        const endMovie = () => {
            console.log("影片播放結束");

            // 移除影片容器
            if (movieContainer.parentNode) {
                movieContainer.parentNode.removeChild(movieContainer);
            }

            // 執行回調
            if (callback) {
                callback();
            }
        };

        // 綁定事件
        video.addEventListener("ended", endMovie);
        video.addEventListener("error", (e) => {
            console.error("影片播放錯誤:", e);
            alert(`影片播放失敗: ${moviePath}`);
            endMovie();
        });

        // 跳過按鈕點擊事件
        skipButton.addEventListener("click", () => {
            console.log("用戶點擊跳過影片");
            video.pause();
            endMovie();
        });

        // ESC 鍵跳過
        const escHandler = (e) => {
            if (e.key === "Escape") {
                console.log("用戶按 ESC 跳過影片");
                video.pause();
                endMovie();
                document.removeEventListener("keydown", escHandler);
            }
        };
        document.addEventListener("keydown", escHandler);

        // 點擊影片區域暫停/播放
        video.addEventListener("click", () => {
            if (video.paused) {
                video.play();
            } else {
                video.pause();
            }
        });

        // 嘗試播放影片
        const playPromise = video.play();
        if (playPromise !== undefined) {
            playPromise.catch(error => {
                console.error("自動播放影片失敗:", error);
                // 顯示播放提示
                this.showMoviePlayPrompt(video);
            });
        }
    },

    // 顯示影片播放提示
    showMoviePlayPrompt: function (video) {
        const prompt = document.createElement("div");
        prompt.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10002;
            cursor: pointer;
        `;
        prompt.innerHTML = `
            <div style="margin-bottom: 10px;">🎬</div>
            <div>點擊播放影片</div>
        `;

        const movieContainer = document.getElementById("movie-container");
        if (movieContainer) {
            movieContainer.appendChild(prompt);

            prompt.addEventListener("click", () => {
                video.play().then(() => {
                    if (prompt.parentNode) {
                        prompt.parentNode.removeChild(prompt);
                    }
                });
            });
        }
    },

    initRender: function () {
        updateMapObjects();
        render();
    },
    changeLevel: function (levelIndex) {
        console.log(`Game.changeLevel 被調用，切換到關卡 ${levelIndex}`);

        // 使用場景管理器進行關卡切換
        sceneManager.switchToLevel(levelIndex);
    },

    // 更新 Canvas 鏡頭位置
    updateCanvasCamera: function (position) {
        if (typeof cameraX === 'undefined' || typeof cameraY === 'undefined') {
            console.warn("Canvas 鏡頭變數未定義");
            return;
        }

        const cols = controlLayer[currentLevel].size.cols;
        const gridX = position % cols;
        const gridY = Math.floor(position / cols);

        // 計算鏡頭位置（讓目標位置在螢幕中央）
        const targetCameraX = gridX * cellWidth - (canvas ? canvas.width / 2 : 800);
        const targetCameraY = gridY * cellHeight - (canvas ? canvas.height / 2 : 600);

        // 確保鏡頭不會超出地圖邊界
        const maxCameraX = Math.max(0, cols * cellWidth - (canvas ? canvas.width : 800));
        const maxCameraY = Math.max(0, controlLayer[currentLevel].size.rows * cellHeight - (canvas ? canvas.height : 600));

        cameraX = Math.max(0, Math.min(targetCameraX, maxCameraX));
        cameraY = Math.max(0, Math.min(targetCameraY, maxCameraY));

        console.log(`Canvas 鏡頭更新到位置 ${position}: 格子(${gridX}, ${gridY}), 鏡頭(${cameraX}, ${cameraY})`);

        // 重新渲染
        if (typeof render === 'function') {
            render();
        }
    },
}

function UI_content() {
    let dialog = document.createElement("dialog");
    dialog.id = "uidialog";
    let menu = document.createElement("div");
    menu.id = "uimenu";
    let menuItems = ["新章初始", "前歷再續", "戰陣重現", "回返太虛"];
    menuItems.forEach(text => {
        let item = document.createElement("div");
        item.className = "uiitem";
        item.textContent = text;
        menu.appendChild(item);
    });
    dialog.appendChild(menu);
    Dom.GameMap.appendChild(dialog);
    dialog.showModal();

    document.querySelectorAll('.uiitem').forEach((item) => {
        item.addEventListener('click', async () => {
            let itemtxt = item.textContent;
            if (itemtxt === "新章初始") {
                dialog.close();
                Dom.GameMap.removeChild(dialog);
                setTimeout(() => {
                    // 重置遊戲資料並開始新遊戲
                    allPlayers = {}; // 重置所有玩家數據
                    sceneManager.gameData = {
                        currentLevel: 0,
                        playerData: {},
                        gameProgress: {},
                        completedLevels: []
                    };
                    sceneManager.switchToLevel(0); // 從第0關開始
                }, 500);
            } else if (itemtxt === "前歷再續") {
                // 繼續遊戲功能 - 顯示選項選單
                dialog.close();
                Dom.GameMap.removeChild(dialog);

                // 顯示前歷再續選項選單
                this.showContinueGameMenu();
            } else if (itemtxt === "戰陣重現") {
                // 使用與 sidebar 相同的讀檔功能
                dialog.close();
                Dom.GameMap.removeChild(dialog);

                if (typeof loadGameFunc === 'function') {
                    console.log("啟動戰陣重現功能...");
                    const result = await loadGameFunc();
                    if (result.loadgame) {
                        console.log("戰陣重現成功，直接切換到遊戲場景（不重新初始化）");
                        // 直接切換場景顯示，不重新初始化遊戲數據
                        this.switchToLoadedGameScene();
                    } else {
                        console.log("戰陣重現取消或失敗");
                        // 如果讀檔失敗或取消，重新顯示主選單
                        setTimeout(() => {
                            sceneManager.showMainMenu();
                        }, 100);
                    }
                } else {
                    console.error("讀檔功能不可用");
                    alert("讀檔功能不可用，請確認相關文件已載入");
                    // 重新顯示主選單
                    setTimeout(() => {
                        sceneManager.showMainMenu();
                    }, 100);
                }
            } else {
                window.close();
            }
        });
    });
}

// 顯示前歷再續選項選單
function showContinueGameMenu() {
    console.log("顯示前歷再續選項選單");

    // 創建選單對話框
    const continueDialog = document.createElement("dialog");
    continueDialog.id = "continue-game-dialog";
    continueDialog.style.cssText = `
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 94.5%;
        height: 100%;
        background-image: url(./Public/savegamebg.png);
        background-size: cover;
        background-position: center;
        border: none;
        border-radius: 10px;
        padding: 20px;
        box-sizing: border-box;
        z-index: 10000;
    `;

    // 創建內容容器
    const continueContent = document.createElement("div");
    continueContent.className = "save-content";
    continueContent.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        position: relative;
        width: 100%;
        height: 100%;
    `;

    // 添加標題
    const title = document.createElement("div");
    title.className = "save-title";
    title.textContent = "前歷再續";
    title.style.cssText = `
        font-size: 32px;
        color: rgb(168, 105, 38);
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        margin-bottom: 20px;
        background-image: url(./Public/showskillbg.png);
        background-size: 100%;
        background-repeat: no-repeat;
        width: 300px;
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: auto;
    `;
    continueContent.appendChild(title);

    // 添加關閉按鈕
    const closeButton = document.createElement("button");
    closeButton.className = "save-close-btn";
    closeButton.textContent = "↩";
    closeButton.style.cssText = `
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 5px 15px;
        font-size: 35px;
        font-weight: 600;
        background-color: rgb(247, 231, 173);
        color: rgb(168, 105, 38);
        border: 5px solid rgb(165, 90, 24);
        border-radius: 5px;
        cursor: pointer;
    `;
    closeButton.addEventListener("click", () => {
        continueDialog.close();
        Dom.GameMap.removeChild(continueDialog);
        // 返回主選單
        sceneManager.showMainMenu();
    });
    continueContent.appendChild(closeButton);

    // 創建選項容器
    const optionsContainer = document.createElement("div");
    optionsContainer.style.cssText = `
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 100%;
        flex-grow: 1;
        justify-content: center;
        align-items: center;
        padding: 50px;
    `;

    // 選項配置
    const options = [
        {
            text: "繼續遊戲進度",
            description: "載入保存的遊戲進度",
            action: () => {
                continueDialog.close();
                Dom.GameMap.removeChild(continueDialog);

                // 原來的前歷再續邏輯
                const hasProgress = sceneManager.loadGameProgress();
                if (hasProgress) {
                    console.log("載入保存的進度");
                    if (sceneManager.gameData.gameProgress.currentScene === 'camp') {
                        sceneManager.switchToCamp(sceneManager.gameData.currentLevel);
                    } else {
                        sceneManager.switchToLevel(sceneManager.gameData.currentLevel);
                    }
                } else {
                    console.log("沒有保存的進度，從營地開始");
                    sceneManager.switchToCamp(0);
                }
            }
        },
        {
            text: "讀取客棧場景",
            description: "載入保存的客棧場景",
            action: () => {
                continueDialog.close();
                Dom.GameMap.removeChild(continueDialog);

                // 調用客棧讀檔功能
                loadHostelFunc().then(result => {
                    if (!result.loadHostel) {
                        // 如果取消或失敗，返回主選單
                        setTimeout(() => {
                            sceneManager.showMainMenu();
                        }, 100);
                    }
                });
            }
        }
    ];

    // 創建選項按鈕
    options.forEach((option, index) => {
        const optionButton = document.createElement("button");
        optionButton.style.cssText = `
            width: 400px;
            height: 80px;
            background-color: rgb(247, 231, 173);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 10px;
            color: rgb(168, 105, 38);
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            animation: slideIn 0.5s ease-out ${index * 0.2}s forwards;
        `;

        optionButton.onmouseover = () => {
            optionButton.style.backgroundColor = "rgb(255, 241, 183)";
            optionButton.style.transform = "scale(1.05)";
        };

        optionButton.onmouseout = () => {
            optionButton.style.backgroundColor = "rgb(247, 231, 173)";
            optionButton.style.transform = "scale(1)";
        };

        optionButton.onclick = option.action;

        // 按鈕文字
        const buttonText = document.createElement("div");
        buttonText.textContent = option.text;
        buttonText.style.fontSize = "24px";
        optionButton.appendChild(buttonText);

        // 按鈕描述
        const buttonDesc = document.createElement("div");
        buttonDesc.textContent = option.description;
        buttonDesc.style.cssText = `
            font-size: 16px;
            font-weight: normal;
            margin-top: 5px;
            opacity: 0.8;
        `;
        optionButton.appendChild(buttonDesc);

        optionsContainer.appendChild(optionButton);
    });

    continueContent.appendChild(optionsContainer);
    continueDialog.appendChild(continueContent);
    Dom.GameMap.appendChild(continueDialog);
    continueDialog.showModal();
}

// 客棧讀檔功能 - 參考 loadGameFunc 樣式
function loadHostelFunc() {
    return new Promise((resolve) => {
        console.log("loadHostelFunc 開始執行");

        // 創建讀檔對話框
        const loadDialog = document.createElement("dialog");
        loadDialog.id = "load-hostel-dialog";
        loadDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建內容容器
        const loadContent = document.createElement("div");
        loadContent.className = "save-content";
        loadContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        loadContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            Dom.GameMap.removeChild(loadDialog);
            resolve({ loadHostel: false });
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            createHostelLoadSlot(slotsContainer, i, loadDialog, resolve);
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        Dom.GameMap.appendChild(loadDialog);
        loadDialog.showModal();
    });
}

// 創建客棧讀檔槽位
function createHostelLoadSlot(container, slotIndex, loadDialog, resolve) {
    const slot = document.createElement("div");
    slot.className = "save-slot";
    slot.style.cssText = `
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 110px;
        background-color: rgb(247, 231, 173);
        border: 5px solid rgb(165, 90, 24);
        border-radius: 5px;
        padding: 5px;
        cursor: pointer;
        box-sizing: border-box;
        opacity: 0;
    `;

    // 左欄：圖騰
    const totem = document.createElement("div");
    totem.className = "save-slot-totem";
    totem.style.cssText = `
        width: 30%;
        text-align: center;
    `;
    const totemImage = document.createElement("img");
    totemImage.draggable = false;
    totemImage.src = "./Public/saveicon.png";
    totemImage.style.cssText = `
        width: 360px;
        height: 95px;
    `;
    totem.appendChild(totemImage);
    slot.appendChild(totem);

    // 中間欄：客棧信息和存檔時間
    const middle = document.createElement("div");
    middle.className = "save-slot-middle";
    middle.style.cssText = `
        width: 30%;
        text-align: center;
    `;
    const hostelInfo = document.createElement("div");
    hostelInfo.className = "save-slot-level";
    hostelInfo.style.cssText = `
        color: rgb(168, 105, 38);
        font-size: 25px;
    `;
    const saveTime = document.createElement("div");
    saveTime.className = "save-slot-time";
    saveTime.style.cssText = `
        color: rgb(168, 105, 38);
        font-size: 20px;
    `;

    // 獲取存檔信息
    const slotInfo = getHostelSlotInfo(slotIndex);
    if (slotInfo) {
        hostelInfo.style.display = "block";
        hostelInfo.textContent = `客棧 ${slotInfo.hostelIndex}`;
        saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } else {
        hostelInfo.style.display = "none";
        saveTime.textContent = "空的客棧";
    }

    middle.appendChild(hostelInfo);
    middle.appendChild(saveTime);
    slot.appendChild(middle);

    // 右欄：關卡標題
    const levelTitle = document.createElement("div");
    levelTitle.className = "save-slot-title";
    levelTitle.style.cssText = `
        width: 30%;
        text-align: center;
        font-weight: 600;
        color: rgb(168, 105, 38);
        font-size: 28px;
    `;

    // 顯示關卡標題，如果沒有存檔則顯示槽位編號
    if (slotInfo && slotInfo.levelTitle) {
        levelTitle.textContent = slotInfo.levelTitle;
    } else {
        levelTitle.textContent = `第 ${slotIndex + 1} 格`;
    }
    slot.appendChild(levelTitle);

    // 存檔槽點擊事件
    slot.addEventListener("click", () => {
        if (!slotInfo) {
            return;
        }

        const confirmLoad = confirm(`是否要讀取第 ${slotIndex + 1} 格的客棧存檔？\n客棧：${slotInfo.hostelIndex}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
        if (confirmLoad) {
            const hostelState = loadHostelState(slotIndex);
            if (hostelState) {
                loadDialog.close();
                Dom.GameMap.removeChild(loadDialog);

                // 載入客棧場景
                loadHostelScene(hostelState);
                alert("客棧讀取成功！");
                resolve({ loadHostel: true });
            } else {
                alert("客棧讀取失敗！");
                resolve({ loadHostel: false });
            }
        }
    });

    // 添加動畫
    const delay = slotIndex * 0.2;
    slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

    container.appendChild(slot);
}

// 獲取客棧存檔槽位信息
function getHostelSlotInfo(slotIndex) {
    if (slotIndex < 0 || slotIndex >= 5) {
        return null;
    }

    try {
        const storageKey = `hostelSave_${slotIndex}`;
        const savedData = localStorage.getItem(storageKey);

        if (!savedData) {
            return null;
        }

        const hostelState = JSON.parse(savedData);
        return {
            timestamp: hostelState.timestamp,
            hostelIndex: hostelState.hostelIndex,
            previousScene: hostelState.previousScene,
            levelTitle: hostelState.levelTitle || "未知關卡",
            currentLevel: hostelState.currentLevel || 0,
            hasPlayerData: !!hostelState.playerData
        };
    } catch (error) {
        console.error("獲取客棧存檔槽位信息失敗:", error);
        return null;
    }
}

// 從指定槽位載入客棧狀態
function loadHostelState(slotIndex) {
    console.log(`從槽位 ${slotIndex} 載入客棧狀態`);

    if (slotIndex < 0 || slotIndex >= 5) {
        console.error("無效的存檔槽位:", slotIndex);
        return null;
    }

    try {
        const storageKey = `hostelSave_${slotIndex}`;
        const savedData = localStorage.getItem(storageKey);

        if (!savedData) {
            console.warn(`槽位 ${slotIndex} 沒有保存的客棧狀態`);
            return null;
        }

        const hostelState = JSON.parse(savedData);
        console.log(`客棧狀態已從槽位 ${slotIndex} 載入`);
        return hostelState;
    } catch (error) {
        console.error("載入客棧狀態失敗:", error);
        return null;
    }
}

// 載入客棧場景
function loadHostelScene(hostelState) {
    console.log("載入客棧場景狀態:", hostelState);

    try {
        // 使用場景管理器切換到客棧場景
        if (typeof sceneManager !== 'undefined') {
            sceneManager.switchToHostel(hostelState.hostelIndex, hostelState.previousScene);
            console.log("客棧場景載入成功");
        } else {
            console.error("場景管理器不可用");
            alert("無法載入客棧場景");
        }
    } catch (error) {
        console.error("載入客棧場景失敗:", error);
        alert("載入客棧場景失敗");
    }
}

// 恢復 sidebar 按鈕狀態（全局函數）
function enableSidebarButtons() {
    console.log("恢復所有 sidebar 按鈕狀態");

    // 按鈕配置
    const buttons = [
        { id: 'savegamebtn', title: '存取進度' },
        { id: 'loadgamebtn', title: '讀取進度' },
        { id: 'lookrolebtn', title: '查看角色' },
        { id: 'cancelbtn', title: '取消動作' },
        { id: 'endturnbtn', title: '結束回合' },
        { id: 'settingbtn', title: '遊戲設定' }
    ];

    // 恢復所有按鈕
    buttons.forEach(buttonConfig => {
        const button = document.getElementById(buttonConfig.id);
        if (button) {
            button.style.opacity = '1';
            button.style.pointerEvents = 'auto';
            button.style.cursor = 'pointer';
            button.title = buttonConfig.title;
        }
    });

    console.log("所有 Sidebar 按鈕狀態已恢復");
}

// 將函數設為全局可用
window.enableSidebarButtons = enableSidebarButtons;

window.onload = function () {
    // 初始化場景管理器
    sceneManager.init();
}

// ===== Canvas 過渡方案開始 =====

// Canvas 相關變數
let canvas, ctx;
let cellWidth = 110; // 格子寬
let cellHeight = 88; // 格子高
let mapCols = 22, mapRows = 13; // 預設，可依 controlLayer 設定
let cameraX = 0, cameraY = 0;
let mapObjects = [];
let highlightCells = [];
let moveTargetCells = new Set(); // <-- 新增：存目前可移動的格子索引
let currentMovePlayerIdx = null; // <-- 新增：目前進入移動模式的玩家索引

// 圖片預載（示範用，實際應根據玩家/敵人資料載入）
let playerImg = new Image();
playerImg.src = './Public/Players/0/avatar.png';
let enemyImg = new Image();
enemyImg.src = './Public/Enemys/0/man.gif';

function initCanvas(cols, rows) {
    console.log("初始化 Canvas...");

    // 確保 DOM 元素存在
    canvas = document.getElementById('gameCanvas');
    if (!canvas) {
        console.error("找不到 gameCanvas 元素！");
        // 嘗試創建 Canvas 元素
        canvas = document.createElement('canvas');
        canvas.id = 'gameCanvas';

        // 找到 GameMap 容器並添加 Canvas
        const gameMap = document.getElementById('GameMap');
        if (gameMap) {
            gameMap.appendChild(canvas);
            console.log("已創建並添加 gameCanvas 元素");
        } else {
            console.error("找不到 GameMap 容器！");
            return;
        }
    }

    // 確保 Canvas 可見
    canvas.style.display = 'block';
    canvas.style.visibility = 'visible';

    try {
        ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error("無法獲取 Canvas 2D 上下文！");
            return;
        }

        mapCols = cols;
        mapRows = rows;
        canvas.width = cols * cellWidth;
        canvas.height = rows * cellHeight;
        canvas.style.position = 'absolute';
        canvas.style.left = '0';
        canvas.style.top = '0';
        canvas.style.zIndex = '10'; // 確保 Canvas 在適當的層級

        // 移除舊的事件監聽器（如果存在）
        canvas.removeEventListener('click', handleClick);
        // 添加新的事件監聽器
        canvas.addEventListener('click', handleClick);

        console.log(`Canvas 初始化成功: ${cols}x${rows}, 尺寸: ${canvas.width}x${canvas.height}`);
    } catch (error) {
        console.error("Canvas 初始化失敗:", error);
    }
}

function drawGrid() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.01)';
    ctx.font = '20px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    for (let x = 0; x <= mapCols; x++) {
        ctx.beginPath();
        ctx.moveTo(x * cellWidth - cameraX, 0 - cameraY);
        ctx.lineTo(x * cellWidth - cameraX, mapRows * cellHeight - cameraY);
        ctx.stroke();
    }
    for (let y = 0; y <= mapRows; y++) {
        ctx.beginPath();
        ctx.moveTo(0 - cameraX, y * cellHeight - cameraY);
        ctx.lineTo(mapCols * cellWidth - cameraX, y * cellHeight - cameraY);
        ctx.stroke();
    }
    // 在每個格子中央繪製格子編號
    /*
    ctx.fillStyle = '#fff';
    for (let row = 0; row < mapRows; row++) {
        for (let col = 0; col < mapCols; col++) {
            let idx = row * mapCols + col;
            ctx.fillText(
                idx,
                col * cellWidth + cellWidth / 2 - cameraX,
                row * cellHeight + cellHeight / 2 - cameraY
            );
        }
    }*/
}

function drawObjects() {
    for (const obj of mapObjects) {
        ctx.drawImage(
            obj.img,
            obj.x * cellWidth - cameraX,
            obj.y * cellHeight - cameraY,
            obj.width,
            obj.height
        );
    }
}

function drawHighlights() {
    ctx.save();
    const currentTime = Date.now();

    for (const cell of highlightCells) {
        const drawX = cell.x * cellWidth - cameraX;
        const drawY = cell.y * cellHeight - cameraY;

        if (cell.type === 'rangeImage' && cell.images && cell.images.length > 0) {
            // 繪製範圍圖片動畫高亮

            // 更新動畫幀
            if (currentTime - cell.lastFrameTime >= cell.frameInterval) {
                cell.currentFrame = (cell.currentFrame + 1) % cell.images.length;
                cell.lastFrameTime = currentTime;
            }

            // 繪製當前幀的圖片，使用指定的尺寸
            const currentImage = cell.images[cell.currentFrame];
            if (currentImage) {
                ctx.globalAlpha = 0.8; // 圖片透明度

                // 計算圖片的中心位置
                const centerX = drawX + cellWidth / 2;
                const centerY = drawY + cellHeight / 2;

                // 使用指定的圖片尺寸
                const imgWidth = cell.imageWidth || cellWidth;
                const imgHeight = cell.imageHeight || cellHeight;

                ctx.drawImage(
                    currentImage,
                    centerX - imgWidth / 2,  // 以格子中心為基準
                    centerY - imgHeight / 2,
                    imgWidth,
                    imgHeight
                );
            }
        } else if (cell.type === 'image' && cell.images && cell.images.length > 0) {
            // 繪製普通圖片動畫高亮（保留原有邏輯）

            // 更新動畫幀
            if (currentTime - cell.lastFrameTime >= cell.frameInterval) {
                cell.currentFrame = (cell.currentFrame + 1) % cell.images.length;
                cell.lastFrameTime = currentTime;
            }

            // 繪製當前幀的圖片
            const currentImage = cell.images[cell.currentFrame];
            if (currentImage) {
                ctx.globalAlpha = 0.8; // 圖片透明度
                ctx.drawImage(
                    currentImage,
                    drawX,
                    drawY,
                    cellWidth,
                    cellHeight
                );
            }
        } else {
            // 繪製顏色高亮（原有邏輯）
            ctx.globalAlpha = 0.4;
            ctx.fillStyle = cell.color || 'green';
            ctx.fillRect(
                drawX,
                drawY,
                cellWidth,
                cellHeight
            );
        }
    }

    ctx.restore();
}

function render() {
    if (!ctx) return;
    drawGrid();
    drawHighlights();
    drawObjects();
}

// 啟動動畫循環（僅在需要時）
let animationRunning = false;

function startAnimationLoop() {
    if (animationRunning) return;
    animationRunning = true;

    function animate() {
        const hasImageHighlights = highlightCells.some(cell =>
            cell.type === 'image' || cell.type === 'rangeImage'
        );
        const hasRecoveryNumbers = mapObjects.some(obj => obj.type === 'recoveryNumber' || obj.type === 'damageNumber');

        if (hasImageHighlights || hasRecoveryNumbers) {
            render();
            requestAnimationFrame(animate);
        } else {
            animationRunning = false;
        }
    }

    requestAnimationFrame(animate);
}

function getCellFromMouse(mouseX, mouseY) {
    const rect = canvas.getBoundingClientRect();
    const x = mouseX - rect.left + cameraX;
    const y = mouseY - rect.top + cameraY;
    const col = Math.floor(x / cellWidth);
    const row = Math.floor(y / cellHeight);
    return { row, col };
}



let imageCache = {}; // 圖片快取

function preloadImage(src) {
    return new Promise((resolve, reject) => {
        if (imageCache[src]) {
            resolve(imageCache[src]);
            return;
        }

        const img = new Image();
        img.onload = () => {
            imageCache[src] = img;
            resolve(img);
        };
        img.onerror = () => {
            console.error(`圖片載入失敗: ${src}`);
            reject(new Error(`Failed to load image: ${src}`));
        };
        img.src = src;
    });
}

// 修正版本：角色定位和層級處理
async function updateMapObjects() {
    console.log("開始更新mapObjects，清除所有現有物件");

    // 完全清空mapObjects陣列，確保沒有殘留物件
    mapObjects.length = 0;

    // 處理玩家 - 使用 Stand.down 圖片陣列動畫
    for (let i = 0; i < gameplayers.length; i++) {
        const player = gameplayers[i];
        // 只放置存活的玩家 (CurHP > 0)
        if (player.CurHP <= 0) {
            console.log(`玩家 ${player.name} 已死亡 (CurHP: ${player.CurHP})，跳過放置`);
            continue;
        }

        try {
            console.log(`放置存活玩家: ${player.name} (CurHP: ${player.CurHP}/${player.HP})`);

            // 使用玩家的 lastdirect 屬性決定初始站立方向
            let initialDirection = player.lastdirect || 'down';
            let imgSrc;
            let standImages = player.Stand && player.Stand[initialDirection];

            if (standImages && Array.isArray(standImages) && standImages.length > 0) {
                // 使用對應方向的第一張圖片作為初始圖片
                imgSrc = standImages[0];
                console.log(`玩家 ${player.name} 初始化使用 ${initialDirection} 方向站立動畫`);
            } else {
                // 如果沒有對應方向，回退到 down 方向
                standImages = player.Stand && player.Stand.down;
                if (standImages && Array.isArray(standImages) && standImages.length > 0) {
                    imgSrc = standImages[0];
                    console.log(`玩家 ${player.name} 回退使用 down 方向站立動畫`);
                } else {
                    // 最後回退到預設圖片
                    imgSrc = player["圖片"] || './Public/Players/default.png';
                    console.log(`玩家 ${player.name} 使用預設圖片`);
                }
            }

            const img = await preloadImage(imgSrc);

            let width = player.width ? parseInt(player.width.toString().replace('px', '')) : cellWidth;
            let height = player.height ? parseInt(player.height.toString().replace('px', '')) : cellHeight;

            if (isNaN(width)) width = cellWidth;
            if (isNaN(height)) height = cellHeight;

            const gridX = player.Position % mapCols;
            const gridY = Math.floor(player.Position / mapCols);

            const playerObj = {
                type: 'player',
                playerIndex: i, // 添加玩家索引
                gridX: gridX,
                gridY: gridY,
                img: img,
                width: width,
                height: height,
                name: player.name,
                zIndex: gridY, // Y座標越大，層級越高
                // 動畫相關屬性
                standImages: standImages,
                currentFrameIndex: 0,
                lastFrameTime: 0,
                frameInterval: 200, // 每200ms切換一幀
                // 動畫狀態管理
                isStandAnimating: false,
                animationId: null
            };

            mapObjects.push(playerObj);

            // 如果有 Stand 動畫，啟動動畫循環
            if (standImages && standImages.length > 1) {
                console.log(`啟動玩家 ${player.name} 的站立動畫，共 ${standImages.length} 幀`);
                startStandAnimation(playerObj);
            } else {
                console.log(`玩家 ${player.name} 沒有多幀站立動畫 (standImages: ${standImages ? standImages.length : 'null'})`);
            }

        } catch (error) {
            console.error(`載入玩家 ${player.name} 圖片失敗:`, error);
        }
    }

    // 處理敵人 - 使用 Stand.down 圖片陣列動畫（如果有的話），只放置存活的敵人
    console.log(`檢查 ${gameenemys.length} 個敵人的狀態`);
    for (let i = 0; i < gameenemys.length; i++) {
        const enemy = gameenemys[i];
        console.log(`敵人 ${i}: ${enemy.name}, CurHP: ${enemy.CurHP}, Position: ${enemy.Position}`);

        // 只放置存活的敵人 (CurHP > 0)
        if (enemy.CurHP <= 0) {
            console.log(`敵人 ${enemy.name} 已死亡 (CurHP: ${enemy.CurHP})，跳過放置`);
            continue;
        }

        try {
            console.log(`放置存活敵人: ${enemy.name} (CurHP: ${enemy.CurHP}/${enemy.HP})`);

            // 使用敵人的 lastdirect 屬性決定初始站立方向
            let initialDirection = enemy.lastdirect || 'down';
            let imgSrc;
            let standImages = enemy.Stand && enemy.Stand[initialDirection];

            if (standImages && Array.isArray(standImages) && standImages.length > 0) {
                // 使用對應方向的第一張圖片作為初始圖片
                imgSrc = standImages[0];
                console.log(`敵人 ${enemy.name} 初始化使用 ${initialDirection} 方向站立動畫`);
            } else {
                // 如果沒有對應方向，回退到 down 方向
                standImages = enemy.Stand && enemy.Stand.down;
                if (standImages && Array.isArray(standImages) && standImages.length > 0) {
                    imgSrc = standImages[0];
                    console.log(`敵人 ${enemy.name} 回退使用 down 方向站立動畫`);
                } else {
                    // 最後回退到預設圖片
                    imgSrc = enemy["圖片"] || './Public/Enemys/default.png';
                    console.log(`敵人 ${enemy.name} 使用預設圖片`);
                }
            }

            const img = await preloadImage(imgSrc);

            let width = enemy.width ? parseInt(enemy.width.toString().replace('px', '')) : cellWidth * 0.9;
            let height = enemy.height ? parseInt(enemy.height.toString().replace('px', '')) : cellHeight * 1.2;

            if (isNaN(width)) width = cellWidth * 0.9;
            if (isNaN(height)) height = cellHeight * 1.2;

            const gridX = enemy.Position % mapCols;
            const gridY = Math.floor(enemy.Position / mapCols);

            const enemyObj = {
                type: 'enemy',
                enemyIndex: i, // 添加敵人索引
                gridX: gridX,
                gridY: gridY,
                img: img,
                width: width,
                height: height,
                name: enemy.name,
                zIndex: gridY, // Y座標越大，層級越高
                // 動畫相關屬性
                standImages: standImages,
                currentFrameIndex: 0,
                lastFrameTime: 0,
                frameInterval: 200, // 敵人動畫稍慢一些，每200ms切換一幀
                // 動畫狀態管理
                isStandAnimating: false,
                animationId: null
            };

            mapObjects.push(enemyObj);

            // 如果有 Stand 動畫，啟動動畫循環
            if (standImages && standImages.length > 1) {
                console.log(`啟動敵人 ${enemy.name} 的站立動畫，共 ${standImages.length} 幀`);
                startStandAnimation(enemyObj);
            } else {
                console.log(`敵人 ${enemy.name} 沒有多幀站立動畫 (standImages: ${standImages ? standImages.length : 'null'})`);
            }

        } catch (error) {
            console.error(`載入敵人 ${enemy.name} 圖片失敗:`, error);
        }
    }

    // 處理寶箱 - 添加寶箱到mapObjects
    for (let i = 0; i < gametreasures.length; i++) {
        const treasure = gametreasures[i];
        try {
            console.log('Treasure data:', treasure);

            // 載入寶箱圖片
            const img = await preloadImage('./Public/treasure.png');

            // 計算寶箱的格子座標
            const gridX = treasure["位置"] % controlLayer[currentLevel].size.cols;
            const gridY = Math.floor(treasure["位置"] / controlLayer[currentLevel].size.cols);

            // 寶箱尺寸
            const width = cellWidth;
            const height = cellHeight;

            const treasureObj = {
                type: 'treasure',
                treasureIndex: i, // 添加寶箱索引
                gridX: gridX,
                gridY: gridY,
                img: img,
                width: width,
                height: height,
                name: treasure["寶物"].name || '寶箱',
                position: treasure["位置"],
                zIndex: gridY - 0.1, // 寶箱在同一格子的角色下方
                // 寶箱不需要動畫
                isStandAnimating: false,
                animationId: null
            };

            mapObjects.push(treasureObj);

            console.log(`寶箱 ${treasureObj.name} 已添加到位置 (${gridX}, ${gridY})`);

        } catch (error) {
            console.error(`載入寶箱圖片失敗:`, error);
        }
    }

    // 根據層級排序：zIndex 小的先繪製（在底層）
    mapObjects.sort((a, b) => a.zIndex - b.zIndex);

    // 驗證：確保沒有死亡的敵人殘留在mapObjects中
    console.log("驗證mapObjects中的敵人狀態");
    const enemyObjects = mapObjects.filter(obj => obj.type === 'enemy');
    for (const enemyObj of enemyObjects) {
        const enemy = gameenemys[enemyObj.enemyIndex];
        if (enemy && enemy.CurHP <= 0) {
            console.error(`發現死亡敵人殘留在mapObjects中: ${enemy.name} (CurHP: ${enemy.CurHP})`);
            // 強制移除死亡敵人
            const index = mapObjects.indexOf(enemyObj);
            if (index !== -1) {
                mapObjects.splice(index, 1);
                console.log(`已強制移除死亡敵人: ${enemy.name}`);
            }
        }
    }

    console.log(`mapObjects更新完成，共有 ${mapObjects.length} 個物件`);
    console.log("mapObjects內容:", mapObjects.map(obj => `${obj.type}: ${obj.name || 'unnamed'}`));

    // 清除所有點擊事件處理器，確保點擊事件與當前地圖狀態同步
    clearAllClickHandlers();

    render();
}

// 強制清除死亡敵人的函數
function forceRemoveDeadEnemies() {
    console.log("強制清除死亡敵人");

    let removedEnemies = 0;

    // 從mapObjects中移除所有死亡敵人
    for (let i = mapObjects.length - 1; i >= 0; i--) {
        const obj = mapObjects[i];
        if (obj.type === 'enemy') {
            const enemy = gameenemys[obj.enemyIndex];
            if (!enemy || enemy.CurHP <= 0) {
                console.log(`強制移除死亡敵人: ${enemy ? enemy.name : 'unknown'} (索引: ${obj.enemyIndex})`);
                mapObjects.splice(i, 1);
                removedEnemies++;
            }
        }
    }

    // 重新渲染
    render();

    // 如果移除了敵人，檢查是否所有敵人都已死亡
    if (removedEnemies > 0) {
        const aliveEnemies = gameenemys.filter(enemy => enemy.CurHP > 0);
        console.log(`強制清除後剩餘存活敵人數量: ${aliveEnemies.length}`);

        if (aliveEnemies.length === 0) {
            console.log("強制清除後所有敵人已死亡，關卡通關！");

            // 注意：勝利處理現在完全由 Players.js 中的 clearBattleCis 函數處理
            // 這裡不再處理勝利邏輯，避免重複觸發
            console.log("勝利檢測完成，等待 clearBattleCis 處理後續流程");
        }
    }
}

// 清除所有點擊事件處理器的函數
function clearAllClickHandlers() {
    console.log("清除所有點擊事件處理器");

    // 清除攻擊點擊事件處理器
    if (window.currentAttackClickHandler) {
        canvas.removeEventListener('click', window.currentAttackClickHandler);
        window.currentAttackClickHandler = null;
        console.log("已清除攻擊點擊事件處理器");
    }

    // 清除物品使用點擊事件處理器
    if (window.currentItemUseClickHandler) {
        canvas.removeEventListener('click', window.currentItemUseClickHandler);
        window.currentItemUseClickHandler = null;
        console.log("已清除物品使用點擊事件處理器");
    }

    // 清除物品使用滑鼠移動事件處理器
    if (window.currentItemUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentItemUseMouseMoveHandler);
        window.currentItemUseMouseMoveHandler = null;
        console.log("已清除物品使用滑鼠移動事件處理器");
    }

    // 清除攻擊相關的高亮和狀態
    if (typeof clearAllAttackRelated === 'function') {
        clearAllAttackRelated();
    }
}

// 啟動站立動畫的函數
function startStandAnimation(characterObj) {
    if (!characterObj.standImages || characterObj.standImages.length <= 1) {
        console.log(`${characterObj.type} ${characterObj.name} 沒有多幀動畫，跳過動畫啟動`);
        return; // 沒有動畫幀或只有一幀，不需要動畫
    }

    console.log(`開始啟動 ${characterObj.type} ${characterObj.name} 的站立動畫`);

    // 設置動畫狀態
    characterObj.isStandAnimating = true;
    characterObj.animationId = null;

    function animateFrame() {
        // 檢查動畫是否被停止
        if (!characterObj.isStandAnimating) {
            return; // 停止動畫循環
        }

        const currentTime = performance.now();

        // 檢查是否該切換到下一幀
        if (currentTime - characterObj.lastFrameTime >= characterObj.frameInterval) {
            characterObj.currentFrameIndex = (characterObj.currentFrameIndex + 1) % characterObj.standImages.length;
            characterObj.lastFrameTime = currentTime;

            // 載入新的圖片
            const newImgSrc = characterObj.standImages[characterObj.currentFrameIndex];
            preloadImage(newImgSrc).then(newImg => {
                // 再次檢查動畫是否仍在運行
                if (characterObj.isStandAnimating) {
                    characterObj.img = newImg;
                    render(); // 重新渲染以顯示新圖片
                }
            }).catch(error => {
                console.warn(`載入動畫幀失敗: ${newImgSrc}`, error);
            });
        }

        // 繼續動畫循環
        if (characterObj.isStandAnimating) {
            characterObj.animationId = requestAnimationFrame(animateFrame);
        }
    }

    // 初始化時間戳
    characterObj.lastFrameTime = performance.now();

    // 開始動畫循環
    characterObj.animationId = requestAnimationFrame(animateFrame);
}

// 停止站立動畫的函數
function stopStandAnimation(characterObj) {
    if (characterObj) {
        characterObj.isStandAnimating = false;
        if (characterObj.animationId) {
            cancelAnimationFrame(characterObj.animationId);
            characterObj.animationId = null;
        }
    }
}

// 重新啟動站立動畫的函數
function resumeStandAnimation(characterObj) {
    if (characterObj && characterObj.standImages && characterObj.standImages.length > 1) {
        // 重置到第一幀
        characterObj.currentFrameIndex = 0;
        startStandAnimation(characterObj);
    }
}

// 修正版本：繪製物件函數 - 支援平滑移動和效果動畫
function drawObjects() {
    // 確保物件按照 zIndex 排序，讓效果動畫能正確顯示在角色上方
    mapObjects.sort((a, b) => a.zIndex - b.zIndex);

    // 物件已經按照 zIndex 排序，所以按順序繪製即可
    for (const obj of mapObjects) {
        try {
            let drawX, drawY;

            // 檢查是否使用自定義位置（平滑移動中）
            if (obj.useCustomPosition && obj.customX !== undefined && obj.customY !== undefined) {
                // 使用自定義像素位置（平滑移動）
                const cellCenterX = obj.customX + cellWidth / 2;
                const cellBottomY = obj.customY + cellHeight;

                drawX = cellCenterX - obj.width / 2 - cameraX;
                drawY = cellBottomY - obj.height - cameraY;
            } else {
                // 使用格子位置（正常情況）
                const cellCenterX = obj.gridX * cellWidth + cellWidth / 2; // 格子中心X
                const cellBottomY = (obj.gridY + 1) * cellHeight; // 格子底部Y

                if (obj.type === 'treasure') {
                    // 寶箱特殊渲染：置中顯示
                    drawX = cellCenterX - obj.width / 2 - cameraX;
                    drawY = obj.gridY * cellHeight + (cellHeight - obj.height) / 2 - cameraY;
                } else if (obj.type === 'effect') {
                    // 效果動畫特殊渲染：置中顯示，與角色重疊
                    drawX = cellCenterX - obj.width / 2 - cameraX;
                    drawY = cellBottomY - obj.height - cameraY;
                } else if (obj.type === 'recoveryNumber' || obj.type === 'damageNumber') {
                    // 恢復數值和傷害數值不需要計算drawX和drawY，直接在後面處理
                    drawX = 0;
                    drawY = 0;
                } else {
                    // 計算角色繪製位置
                    // 左右置中：角色中心對齊格子中心
                    drawX = cellCenterX - obj.width / 2 - cameraX;
                    // 上下對齊底線：角色底部對齊格子底部
                    drawY = cellBottomY - obj.height - cameraY;
                }
            }

            //console.log(`Drawing ${obj.type} ${obj.name} at grid(${obj.gridX}, ${obj.gridY}), screen(${drawX}, ${drawY}), zIndex: ${obj.zIndex}`);

            // 對於有圖片的物件，檢查圖片是否已載入
            if (obj.img && !obj.img.complete) {
                console.warn(`圖片尚未載入完成: ${obj.name}`);
                continue;
            }

            // 對於有尺寸的物件，檢查繪製位置是否在可視範圍內
            if (obj.width && obj.height) {
                const buffer = Math.max(obj.width, obj.height); // 緩衝區域
                if (drawX + obj.width < -buffer || drawY + obj.height < -buffer ||
                    drawX > canvas.width + buffer || drawY > canvas.height + buffer) {
                    continue;
                }
            }

            // 繪製物件（角色、效果動畫、恢復數值等）
            if (obj.type === 'effect' || obj.type === 'magicEffect' || obj.type === 'useMagicEffect' ||
                obj.type === 'spellEffect' || obj.type === 'targetEffect') {
                // 效果動畫使用特殊渲染
                ctx.save();
                ctx.globalAlpha = 0.9; // 稍微透明，讓底下的角色可見

                // 如果有濾鏡效果，應用濾鏡
                if (obj.useFilter) {
                    ctx.filter = `brightness(${obj.brightness || 1.5}) contrast(${obj.contrast || 1.2}) saturate(${obj.saturate || 1.3})`;
                }

                ctx.drawImage(
                    obj.img,
                    Math.round(drawX),
                    Math.round(drawY),
                    obj.width,
                    obj.height
                );
                ctx.restore();
            } else if (obj.type === 'recoveryNumber') {
                // 恢復數值特殊渲染
                drawRecoveryNumber(obj);
            } else if (obj.type === 'damageNumber') {
                // 傷害數值特殊渲染（使用相同的函數，但顏色不同）
                drawRecoveryNumber(obj);
            } else if (obj.img) {
                // 普通物件渲染（有圖片的物件）
                ctx.drawImage(
                    obj.img,
                    Math.round(drawX), // 使用整數像素避免模糊
                    Math.round(drawY),
                    obj.width,
                    obj.height
                );
            }

            // 可選：繪製除錯資訊（格子邊框和層級）
            if (window.debugMode) {
                drawDebugInfo(obj, drawX, drawY);
            }

        } catch (error) {
            console.error(`繪製 ${obj.type} ${obj.name} 時發生錯誤:`, error);
        }
    }
}

// 除錯資訊繪製函數
function drawDebugInfo(obj, drawX, drawY) {
    ctx.save();

    // 繪製格子邊框
    ctx.strokeStyle = obj.type === 'player' ? 'blue' :
        obj.type === 'treasure' ? 'gold' : 'red';
    ctx.lineWidth = 2;
    const gridScreenX = obj.gridX * cellWidth - cameraX;
    const gridScreenY = obj.gridY * cellHeight - cameraY;
    ctx.strokeRect(gridScreenX, gridScreenY, cellWidth, cellHeight);

    // 繪製角色邊框
    ctx.strokeStyle = 'green';
    ctx.lineWidth = 1;
    ctx.strokeRect(drawX, drawY, obj.width, obj.height);

    // 繪製層級資訊
    ctx.fillStyle = 'yellow';
    ctx.font = '12px Arial';
    ctx.fillText(
        `Z:${obj.zIndex}`,
        drawX,
        drawY - 5
    );

    // 繪製格子中心點
    ctx.fillStyle = 'red';
    ctx.beginPath();
    ctx.arc(
        obj.gridX * cellWidth + cellWidth / 2 - cameraX,
        obj.gridY * cellHeight + cellHeight / 2 - cameraY,
        3, 0, 2 * Math.PI
    );
    ctx.fill();

    ctx.restore();
}

// 繪製恢復數值 - 類似 setplayerdamagetext 的動畫效果
function drawRecoveryNumber(obj) {
    const currentTime = performance.now();
    const elapsed = currentTime - obj.startTime;
    const progress = Math.min(elapsed / obj.duration, 1); // 0 到 1 的進度

    // 計算動畫位置
    const cellCenterX = obj.gridX * cellWidth + cellWidth / 2;
    const cellCenterY = obj.gridY * cellHeight + cellHeight / 2;

    // 向上浮動的動畫效果（類似 setplayerdamagetext）
    const floatDistance = 80; // 向上浮動80像素，與 setplayerdamagetext 一致
    const currentY = cellCenterY - (progress * floatDistance);

    // 計算透明度（在 fadeStartProgress 之後才開始淡出）
    let currentAlpha;
    if (progress < obj.fadeStartProgress) {
        currentAlpha = 1; // 前面保持完全不透明
    } else {
        let fadeProgress = (progress - obj.fadeStartProgress) / (1 - obj.fadeStartProgress);
        currentAlpha = 1 - fadeProgress;
    }

    ctx.save();

    // 設置字體和樣式（類似 setplayerdamagetext）
    ctx.font = `800 ${obj.fontSize}px ${obj.fontFamily}`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.shadowColor = "white";
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;
    ctx.fillStyle = obj.color;
    ctx.lineWidth = 2;
    ctx.strokeStyle = "white";

    // 計算文字總寬度來居中顯示
    let totalTextWidth = ctx.measureText(obj.text).width;
    let startX = (cellCenterX - cameraX) - (totalTextWidth / 2);

    // 逐字符繪製，加上彈跳效果（類似 setplayerdamagetext）
    for (let i = 0; i < obj.text.length; i++) {
        let char = obj.text[i];
        let charWidth = ctx.measureText(char).width;

        // 計算此字符的彈跳動畫
        let charAnimationStart = i * obj.charDelay;
        let charAnimationEnd = charAnimationStart + obj.charAnimationDuration;
        let bounceOffset = 0;

        if (elapsed >= charAnimationStart && elapsed <= charAnimationEnd) {
            // 字符正在彈跳
            let charProgress = (elapsed - charAnimationStart) / obj.charAnimationDuration;
            // 使用sin函數創造彈跳效果
            bounceOffset = -Math.sin(charProgress * Math.PI) * obj.bounceHeight;
        }

        // 計算字符位置
        let charX = startX + (charWidth / 2);
        let charY = currentY - cameraY + bounceOffset;

        // 設定透明度
        ctx.globalAlpha = currentAlpha;

        // 繪製字符（先描邊再填充，類似 setplayerdamagetext）
        ctx.strokeText(char, charX, charY);
        ctx.fillText(char, charX, charY);

        // 移動到下一個字符位置
        startX += charWidth;
    }

    ctx.restore();
}

// 新增：處理同一格子多個角色的情況
function updateMapObjectsWithOverlap() {
    mapObjects.length = 0;

    // 收集所有角色
    const allCharacters = [
        ...gameplayers.map(p => ({ ...p, type: 'player' })),
        ...gameenemys.map(e => ({ ...e, type: 'enemy' }))
    ];

    // 按位置分組
    const positionGroups = {};
    allCharacters.forEach(char => {
        if (!positionGroups[char.Position]) {
            positionGroups[char.Position] = [];
        }
        positionGroups[char.Position].push(char);
    });

    // 處理每個位置的角色
    Object.values(positionGroups).forEach(group => {
        group.forEach((char, index) => {
            const gridX = char.Position % mapCols;
            const gridY = Math.floor(char.Position / mapCols);

            // 同一格子內的角色，稍微偏移避免完全重疊
            const offsetX = index * 5; // 輕微水平偏移
            const offsetY = index * 3; // 輕微垂直偏移

            mapObjects.push({
                type: char.type,
                gridX: gridX,
                gridY: gridY,
                offsetX: offsetX,
                offsetY: offsetY,
                img: char.img || (char.type === 'player' ? playerImg : enemyImg),
                width: char.width || (char.type === 'player' ? 100 : 100),
                height: char.height || (char.type === 'player' ? 80 : 80),
                name: char.name,
                zIndex: gridY * 1000 + index // 主要按Y排序，同位置按順序
            });
        });
    });

    // 排序
    mapObjects.sort((a, b) => a.zIndex - b.zIndex);
    render();
}

// 開啟/關閉除錯模式的函數
function toggleDebugMode() {
    window.debugMode = !window.debugMode;
    console.log('除錯模式:', window.debugMode ? '開啟' : '關閉');
    render();
}

// 測試層級的函數
function testZOrder() {
    console.log('=== 層級測試 ===');
    mapObjects.forEach((obj, index) => {
        console.log(`${index}: ${obj.name} at (${obj.gridX}, ${obj.gridY}), zIndex: ${obj.zIndex}`);
    });
}

// 輔助函數：檢查位置是否有角色
function getCharacterAtPosition(cellIndex) {
    // 檢查玩家
    const player = gameplayers.find(p => p.Position === cellIndex);
    if (player) {
        return {
            type: 'player',
            character: player,
            index: gameplayers.indexOf(player)
        };
    }

    // 檢查敵人
    const enemy = gameenemys.find(e => e.Position === cellIndex);
    if (enemy) {
        return {
            type: 'enemy',
            character: enemy,
            index: gameenemys.indexOf(enemy)
        };
    }

    return null;
}

// 添加視覺回饋：顯示可點擊的角色
function highlightClickableCharacters() {
    if (runOBJ["當前行動方"] !== "Players") {
        return;
    }

    highlightCells = [];

    // 高亮顯示可操作的玩家
    gameplayers.forEach(player => {
        if (!player.AlreadyMove && player.CurHP > 0 && !player["是否電腦操作"]) {
            const gridX = player.Position % mapCols;
            const gridY = Math.floor(player.Position / mapCols);
            highlightCells.push({
                x: gridX,
                y: gridY,
                color: 'rgba(245, 0, 216, 0.78)' // 綠色表示可操作
            });
        }
    });

    // 高亮顯示敵人（可查看）
    gameenemys.forEach(enemy => {
        if (enemy.CurHP > 0) {
            const gridX = enemy.Position % mapCols;
            const gridY = Math.floor(enemy.Position / mapCols);
            highlightCells.push({
                x: gridX,
                y: gridY,
                color: 'rgba(255, 2, 2, 0.2)' // 紅色表示敵人
            });
        }
    });

    render();
}


function initClickableHighlights() {
    // 可以在每回合開始時呼叫這個函數
    highlightClickableCharacters();
}

// Clear all highlights (from Players.js, updated for Canvas)
function clearAllHighlights() {
    highlightCells.length = 0;
    moveTargetCells.clear();
    currentMovePlayerIdx = null;

    // 清除物品使用相關的事件監聽器
    if (window.currentItemUseClickHandler) {
        canvas.removeEventListener('click', window.currentItemUseClickHandler);
        window.currentItemUseClickHandler = null;
    }
    if (window.currentItemUseMouseMoveHandler) {
        canvas.removeEventListener('mousemove', window.currentItemUseMouseMoveHandler);
        window.currentItemUseMouseMoveHandler = null;
    }

    // 清除所有效果動畫、法術效果和恢復數值
    for (let i = mapObjects.length - 1; i >= 0; i--) {
        if (mapObjects[i].type === 'effect' ||
            mapObjects[i].type === 'recoveryNumber' ||
            mapObjects[i].type === 'damageNumber' ||
            mapObjects[i].type === 'magicEffect' ||
            mapObjects[i].type === 'useMagicEffect' ||
            mapObjects[i].type === 'spellEffect' ||
            mapObjects[i].type === 'targetEffect') {
            mapObjects.splice(i, 1);
        }
    }

    render();
}

function onPlayerMove(playerIndex, path) {
    // 確保路徑不為空
    if (path.length > 0) {
        animatePlayerMove(path, playerIndex); // 調用animatePlayerMove
    }
}

// Handle canvas click (from Init.js, updated for Canvas)
function handleClick(event) {
    const { row, col } = getCellFromMouse(event.clientX, event.clientY);
    const cellIndex = row * mapCols + col;
    if (row < 0 || row >= mapRows || col < 0 || col >= mapCols) return;
    
    if (runOBJ["當前行動方"] !== "Players") return;

    // 檢查是否在法術cursor期間，如果是則不處理角色點擊
    if (window.currentMagicUseClickHandler || window.currentMagicUseMouseMoveHandler) {
        console.log("法術cursor期間，忽略角色點擊");
        return;
    }

    // curHP小於0的敵人要排除點擊事件
    if (gameenemys.find(enemy => enemy.Position === cellIndex && enemy.CurHP <= 0)) {
        return;
    }

    if (runOBJ["當前操作"] === "選擇攻擊目標中" || runOBJ["當前物品操作"] !== null) {
        if (runOBJ["當前操作"] === "選擇攻擊目標中") {
            // 檢查點擊位置是否有存活的敵人
            const enemy = gameenemys.find(enemy => enemy.Position === cellIndex && enemy.CurHP > 0);

            // 同時檢查mapObjects中是否有對應的敵人物件
            const enemyObj = mapObjects.find(obj =>
                obj.type === 'enemy' &&
                obj.gridX === (cellIndex % mapCols) &&
                obj.gridY === Math.floor(cellIndex / mapCols) &&
                gameenemys[obj.enemyIndex] &&
                gameenemys[obj.enemyIndex].CurHP > 0
            );

            console.log(`點擊位置 ${cellIndex}: 找到敵人=${!!enemy}, 找到敵人物件=${!!enemyObj}, 在攻擊範圍=${moveTargetCells.has(cellIndex)}`);

            if (enemy && enemyObj && moveTargetCells.has(cellIndex)) {
                let enemyIndex = gameenemys.indexOf(enemy);
                let player = gameplayers[currentMovePlayerIdx];
                runOBJ["當前操作"] = "攻擊中";
                player.AlreadyMove = true;
                player.OldPosition = player.Position;
                runOBJ["當前物品操作"] = null;
                runOBJ["當前選取物品"] = null;
                if (player["是否蓄力"] === true && player["是否釋放蓄力"] === false) {
                    player.Move -= 2;
                    player["是否蓄力"] = false;
                    player["是否釋放蓄力"] = true;
                }
                clearAllHighlights();
                AccessBattle(player, enemy, currentMovePlayerIdx, enemyIndex);
            }
        }
        return;
    }
    const playerAtPosition = gameplayers.find(player => player.Position === cellIndex);
    if (playerAtPosition) {
        let playerIndex = gameplayers.indexOf(playerAtPosition);
        let player = gameplayers[playerIndex];

        // 檢查是否有 AI 玩家還未移動（但允許點擊人類玩家）
        let aiplayerismove = gameplayers.filter(p => p.AlreadyMove === false && p["是否電腦操作"] === true && p.CurHP > 0);
        if (aiplayerismove.length !== 0 && player["是否電腦操作"] === true) {
            console.log("AI 玩家移動中，無法點擊 AI 玩家");
            return;
        }

        console.log(`點擊玩家 ${player.name} (索引: ${playerIndex})`);
        console.log(`當前選取: ${runOBJ["當前選取"]}, 第二次選取: ${runOBJ["第二次選取"]}`);

        // 如果點擊的是不同的角色，重置第二次選取
        if (runOBJ["當前選取"] !== playerIndex) {
            runOBJ["第二次選取"] = null;
            console.log("點擊不同角色，重置第二次選取");
        }

        // 檢查是否為第二次點擊同一個角色
        if (runOBJ["當前選取"] === playerIndex && runOBJ["第二次選取"] !== playerIndex) {
            // 這是第二次點擊同一個角色
            runOBJ["第二次選取"] = playerIndex;
            console.log(`設置第二次選取為: ${playerIndex}`);
        }

        // 如果是第二次點擊同一個角色，顯示操作選單
        if (runOBJ["第二次選取"] === playerIndex &&
            runOBJ["當前操作"] !== "正在移動中" &&
            runOBJ["當前操作"] !== "已經移動但未進行操作" &&
            player.AlreadyMove === false &&
            !player["是否電腦操作"]) {
            console.log(`顯示玩家 ${player.name} 的操作選單`);
            clearAllHighlights();
            ShowPlayerOperation(player, playerIndex);
            operates.MoveComera(player.Position);
            return;
        }

        // 第一次點擊或其他情況：顯示移動範圍
        clearAllHighlights();
        if (player["是否蓄力"] === true && player["是否釋放蓄力"] === false && !player["已增加移動力"]) {
            player.Move += 2;
            player["已增加移動力"] = true;
        }

        // 檢查玩家是否已經移動過
        if (player.AlreadyMove === true) {
            console.log(`玩家 ${player.name} 已經移動過，只顯示移動範圍（不可移動）`);
            // 顯示移動範圍但設置為查看模式
            DrawPlayerMoveRangeViewOnly(player.Position, playerIndex, player.Move);
        } else {
            console.log(`玩家 ${player.name} 尚未移動，顯示可移動範圍`);
            // 顯示可移動的移動範圍
            DrawPlayerMoveRange(player.Position, playerIndex, player.Move);
        }

        Dom.LookRoleBtn.onclick = () => { lookrole(player); };
        operates.MoveComera(player.Position);
        runOBJ["當前選取"] = playerIndex;

        console.log(`設置當前選取為: ${playerIndex}`);

        if (player["是否電腦操作"]) return;
        return;
    }
    const enemyAtPosition = gameenemys.find(enemy => enemy.Position === cellIndex);
    if (enemyAtPosition && runOBJ["當前操作"] !== "已經移動但未進行操作") {
        let enemyIndex = gameenemys.indexOf(enemyAtPosition);
        let enemy = gameenemys[enemyIndex];
        operates.MoveComera(enemy.Position);
        tempname = [];
        clearAllHighlights();
        Dom.LookRoleBtn.onclick = () => { lookrole(enemy); };
        runOBJ["當前選取"] = null;
        runOBJ["第二次選取"] = null;
        DrawEnemyMoveRange(enemy.Position, enemyIndex, enemy.Move);
        return;
    }
    if (currentMovePlayerIdx !== null && moveTargetCells.has(cellIndex)) {
        const player = gameplayers[currentMovePlayerIdx];

        // 檢查是否為查看模式
        if (runOBJ["當前操作"] === "查看移動範圍中") {
            console.log("當前為查看移動範圍模式，不允許移動");
            return;
        }

        // 雙重檢查玩家是否已經移動過
        if (player.AlreadyMove === true) {
            console.log(`玩家 ${player.name} 已經移動過，不允許再次移動`);
            alert("此角色該回合已經移動過了");
            return;
        }

        // 檢查玩家是否為 AI 控制
        if (player["是否電腦操作"] === true) {
            console.log(`玩家 ${player.name} 是 AI 控制，不允許手動移動`);
            return;
        }
        const isTeammatePosition = gameplayers.some((teammate, index) =>
            index !== currentMovePlayerIdx && teammate.Position === cellIndex
        );
        if (isTeammatePosition) return;
        runOBJ["當前操作"] = "正在移動中";
        runOBJ["是否有行走"] = true;
        let path = findPath(player.Position, cellIndex);
        if (path.length > 0) {
            player.OldPosition = player.Position;
            player.Position = cellIndex;
            player.AlreadyMove = true;
            animatePlayerMove(path, currentMovePlayerIdx);
            setTimeout(() => {
                operates.MoveComera(cellIndex);
            }, 500);
        }
    }
}

function booleancanatkenemy(player, enemys) {
    let playerPosition = player.Position;
    let playerATKRange = player.ATKRange;
    let canatk = false;
    enemys.forEach(enemy => {
        let enemyPosition = enemy.Position;
        let row = Math.floor(playerPosition / controlLayer[currentLevel].size.cols);
        let col = playerPosition % controlLayer[currentLevel].size.cols;
        let enemyrow = Math.floor(enemyPosition / controlLayer[currentLevel].size.cols);
        let enemycol = enemyPosition % controlLayer[currentLevel].size.cols;
        if (Math.abs(row - enemyrow) + Math.abs(col - enemycol) <= playerATKRange) {
            canatk = true;
        }
    });
    return canatk;
}
