/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單按鈕
            this.createHostelMenuButton();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            border: 3px solid #FFD700;
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎從營地來到客棧！'
            : '🏨 歡迎來到客棧！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: #FFD700; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                ${instructionText}
            </p>
            <p style="margin: 0; font-size: 14px; color: #AAA;">
                按 <strong>ESC</strong> 或 <strong>Enter</strong> 開啟選單
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理選單相關按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    this.toggleHostelMenu();
                    break;
                case 'Enter':
                case ' ':
                    if (this.isMenuOpen) {
                        this.handleMenuSelection();
                    } else {
                        this.toggleHostelMenu();
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 創建客棧專用選單按鈕
    createHostelMenuButton() {
        console.log("創建客棧專用選單按鈕");

        // 創建選單按鈕容器
        this.menuButtonContainer = document.createElement('div');
        this.menuButtonContainer.id = 'hostelMenuButtonContainer';
        this.menuButtonContainer.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 5;
        `;

        // 創建主選單按鈕
        const mainMenuButton = document.createElement('button');
        mainMenuButton.textContent = '🏨 客棧選單';
        mainMenuButton.style.cssText = `
            padding: 15px 25px;
            background: linear-gradient(145deg, #8B4513, #A0522D);
            color: white;
            border: 3px solid #FFD700;
            border-radius: 15px;
            cursor: pointer;
            font-size: 18px;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            font-weight: bold;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
            transition: all 0.3s ease;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        `;

        // 按鈕懸停效果
        mainMenuButton.onmouseover = () => {
            mainMenuButton.style.background = 'linear-gradient(145deg, #FFD700, #FFA500)';
            mainMenuButton.style.color = '#000';
            mainMenuButton.style.transform = 'translateY(-3px)';
            mainMenuButton.style.boxShadow = '0 8px 16px rgba(255,215,0,0.6)';
        };

        mainMenuButton.onmouseout = () => {
            mainMenuButton.style.background = 'linear-gradient(145deg, #8B4513, #A0522D)';
            mainMenuButton.style.color = 'white';
            mainMenuButton.style.transform = 'translateY(0)';
            mainMenuButton.style.boxShadow = '0 6px 12px rgba(0,0,0,0.4)';
        };

        // 點擊事件
        mainMenuButton.onclick = () => this.toggleHostelMenu();

        this.menuButtonContainer.appendChild(mainMenuButton);
        this.hostelContainer.appendChild(this.menuButtonContainer);

        // 創建快捷按鈕組
        this.createQuickActionButtons();

        console.log("客棧選單按鈕創建完成");
    }

    // 創建快捷操作按鈕
    createQuickActionButtons() {
        console.log("創建快捷操作按鈕");

        // 快捷按鈕容器
        const quickButtonsContainer = document.createElement('div');
        quickButtonsContainer.id = 'hostelQuickButtons';
        quickButtonsContainer.style.cssText = `
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 5;
        `;

        // 快捷按鈕配置
        const quickButtons = [
            {
                text: '📊',
                title: '角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️',
                title: '加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '💾',
                title: '存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外按鈕
        if (this.previousScene !== 'camp') {
            quickButtons.push({
                text: '🚀',
                title: '前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            quickButtons.push({
                text: '🏕️',
                title: '返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建快捷按鈕
        quickButtons.forEach(buttonConfig => {
            const button = document.createElement('button');
            button.textContent = buttonConfig.text;
            button.title = buttonConfig.title;
            button.style.cssText = `
                width: 60px;
                height: 60px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 50%;
                cursor: pointer;
                font-size: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                box-shadow: 0 4px 8px rgba(0,0,0,0.4);
            `;

            button.onmouseover = () => {
                button.style.background = 'rgba(255, 215, 0, 0.9)';
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 6px 12px rgba(255,215,0,0.6)';
            };

            button.onmouseout = () => {
                button.style.background = 'rgba(0, 0, 0, 0.8)';
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 8px rgba(0,0,0,0.4)';
            };

            button.onclick = buttonConfig.action;
            quickButtonsContainer.appendChild(button);
        });

        this.hostelContainer.appendChild(quickButtonsContainer);
        console.log("快捷操作按鈕創建完成");
    }



    // 切換客棧選單
    toggleHostelMenu() {
        console.log("切換客棧選單");

        if (this.isMenuOpen) {
            this.closeHostelMenu();
        } else {
            this.openHostelMenu();
        }
    }

    // 開啟客棧選單
    openHostelMenu() {
        console.log("開啟客棧選單 - 動態創建");

        // 如果選單已經存在，先關閉
        if (this.hostelMenu) {
            this.closeHostelMenu();
            return;
        }

        // 暫停鍵盤控制
        this.keyboardPaused = true;

        // 動態創建選單 UI
        this.createHostelMenuUI();

        this.isMenuOpen = true;
        console.log("客棧選單已動態創建並開啟");
    }

    // 關閉客棧選單
    closeHostelMenu() {
        console.log("關閉客棧選單 - 移除 DOM 元素");

        // 恢復鍵盤控制
        this.keyboardPaused = false;

        // 移除選單 UI 和遮罩
        if (this.hostelMenu) {
            // 添加關閉動畫
            this.hostelMenu.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
            this.hostelMenu.style.opacity = '0';
            this.hostelMenu.style.transform = 'translate(-50%, -50%) scale(0.8)';

            setTimeout(() => {
                if (this.hostelMenu && this.hostelMenu.parentNode) {
                    this.hostelMenu.remove();
                }
                this.hostelMenu = null;
            }, 300);
        }

        // 移除遮罩
        const overlay = this.hostelContainer.querySelector('div[style*="background: rgba(0, 0, 0, 0.3)"]');
        if (overlay) {
            overlay.style.transition = 'opacity 0.3s ease-out';
            overlay.style.opacity = '0';
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.remove();
                }
            }, 300);
        }

        this.isMenuOpen = false;
        console.log("客棧選單和遮罩已關閉並移除");
    }

    // 創建客棧選單 UI（每次都重新創建）
    createHostelMenuUI() {
        console.log("動態創建客棧選單 UI");

        // 確保沒有現有選單（安全檢查）
        if (this.hostelMenu) {
            console.log("發現現有選單，先移除");
            this.hostelMenu.remove();
            this.hostelMenu = null;
        }

        // 創建選單容器（帶進入動畫）
        this.hostelMenu = document.createElement('div');
        this.hostelMenu.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            background: rgba(0, 0, 0, 0.95);
            border: 3px solid #FFD700;
            border-radius: 15px;
            padding: 30px;
            z-index: 10;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            min-width: 350px;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.7);
            opacity: 0;
            transition: all 0.3s ease-out;
        `;

        // 添加背景遮罩
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            z-index: 9;
        `;
        this.hostelContainer.appendChild(overlay);

        // 點擊遮罩關閉選單
        overlay.onclick = () => this.closeHostelMenu();

        // 選單標題（動態生成）
        const title = document.createElement('h2');
        const titleText = this.previousScene === 'camp' ? '🏨 客棧服務選單' : '🏨 客棧休息選單';
        title.textContent = titleText;
        title.style.cssText = `
            margin: 0 0 25px 0;
            text-align: center;
            color: #FFD700;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            animation: titleGlow 2s ease-in-out infinite alternate;
        `;
        this.hostelMenu.appendChild(title);

        // 添加 CSS 動畫
        if (!document.getElementById('hostelMenuStyles')) {
            const style = document.createElement('style');
            style.id = 'hostelMenuStyles';
            style.textContent = `
                @keyframes titleGlow {
                    from { text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,215,0,0.5); }
                    to { text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(255,215,0,0.8); }
                }
            `;
            document.head.appendChild(style);
        }

        // 動態生成選單選項
        const menuOptions = this.generateMenuOptions();

        console.log(`為 ${this.previousScene || '直接進入'} 場景生成 ${menuOptions.length} 個選單選項`);

        // 創建選項按鈕（帶描述和動畫）
        menuOptions.forEach((option, index) => {
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                margin: 8px 0;
                opacity: 0;
                transform: translateX(-20px);
                transition: all 0.3s ease;
                transition-delay: ${index * 0.1}s;
            `;

            const button = document.createElement('button');
            button.textContent = option.text;
            button.title = option.description; // 工具提示
            button.style.cssText = `
                width: 100%;
                padding: 15px;
                background: linear-gradient(145deg, #5a5a5a, #3a3a3a);
                color: white;
                border: 2px solid #666;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                position: relative;
                overflow: hidden;
            `;

            // 添加按鈕描述
            if (option.description) {
                const description = document.createElement('div');
                description.textContent = option.description;
                description.style.cssText = `
                    font-size: 12px;
                    color: #AAA;
                    margin-top: 5px;
                    text-align: center;
                `;
                buttonContainer.appendChild(description);
            }

            button.onmouseover = () => {
                button.style.background = 'linear-gradient(145deg, #FFD700, #FFA500)';
                button.style.color = '#000';
                button.style.transform = 'translateY(-2px) scale(1.02)';
                button.style.boxShadow = '0 8px 16px rgba(255,215,0,0.4)';
            };

            button.onmouseout = () => {
                button.style.background = 'linear-gradient(145deg, #5a5a5a, #3a3a3a)';
                button.style.color = 'white';
                button.style.transform = 'translateY(0) scale(1)';
                button.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
            };

            button.onclick = () => {
                console.log(`客棧選單選擇: ${option.text}`);
                this.closeHostelMenu();
                // 延遲執行動作，讓關閉動畫完成
                setTimeout(() => {
                    option.action();
                }, 300);
            };

            buttonContainer.appendChild(button);
            this.hostelMenu.appendChild(buttonContainer);

            // 觸發按鈕進入動畫
            setTimeout(() => {
                buttonContainer.style.opacity = '1';
                buttonContainer.style.transform = 'translateX(0)';
            }, 100 + index * 100);
        });

        this.hostelContainer.appendChild(this.hostelMenu);

        // 觸發進入動畫
        setTimeout(() => {
            this.hostelMenu.style.opacity = '1';
            this.hostelMenu.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 50);

        console.log("客棧選單 UI 動態創建完成");
    }

    // 動態生成選單選項
    generateMenuOptions() {
        console.log("動態生成選單選項");

        const baseOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus(),
                description: '查看角色的詳細資訊'
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu(),
                description: '分配角色屬性點數'
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu(),
                description: '保存當前遊戲進度'
            }
        ];

        // 根據來源場景動態添加選項
        if (this.previousScene !== 'camp') {
            // 直接進入客棧的選項
            baseOptions.push({
                text: '🚀 前往下一關',
                action: () => this.goToNextLevel(),
                description: '繼續冒險旅程'
            });
        }

        if (this.previousScene === 'camp') {
            // 從營地來的選項
            baseOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp(),
                description: '回到營地繼續探索'
            });
        }

        // 通用選項
        baseOptions.push({
            text: '❌ 關閉選單',
            action: () => this.closeHostelMenu(),
            description: '關閉此選單'
        });

        console.log(`生成了 ${baseOptions.length} 個選單選項`);
        return baseOptions;
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示存檔選單");

        // 調用現有的存檔系統
        if (typeof showSaveInterface === 'function') {
            showSaveInterface();
        } else if (typeof saveGameFunc === 'function') {
            saveGameFunc();
        } else {
            alert("存檔功能暫未實現");
        }
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof goToNextLevel === 'function') {
            goToNextLevel();
        } else if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 關閉選單
        if (this.isMenuOpen) {
            this.closeHostelMenu();
        }

        // 移除客棧選單按鈕容器
        if (this.menuButtonContainer && this.menuButtonContainer.parentNode) {
            this.menuButtonContainer.parentNode.removeChild(this.menuButtonContainer);
            this.menuButtonContainer = null;
        }

        // 移除快捷按鈕容器
        const quickButtons = document.getElementById('hostelQuickButtons');
        if (quickButtons && quickButtons.parentNode) {
            quickButtons.parentNode.removeChild(quickButtons);
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        console.log("客棧場景清理完成");
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}