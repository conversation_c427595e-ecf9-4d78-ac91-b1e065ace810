/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        // Sidebar 狀態保存
        this.originalSidebarState = null;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單面板
            this.createHostelMenuPanel();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 保存並隱藏原本的 sidebar 狀態
    saveSidebarState() {
        console.log("保存並隱藏原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            // 保存原始狀態
            this.originalSidebarState = {
                display: sidebar.style.display || getComputedStyle(sidebar).display,
                visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility,
                opacity: sidebar.style.opacity || getComputedStyle(sidebar).opacity,
                zIndex: sidebar.style.zIndex || getComputedStyle(sidebar).zIndex
            };

            // 隱藏 sidebar（客棧有自己的選單系統）
            sidebar.style.display = 'none';

            console.log("Sidebar 狀態已保存並隱藏:", this.originalSidebarState);
        } else {
            console.warn("找不到 sidebar 元素，無法保存狀態");
            this.originalSidebarState = null;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 保存並隱藏原本的 sidebar
        this.saveSidebarState();

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎來到客棧！'
            : '🏨 歡迎來到休息區！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: rgb(168, 105, 38);; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;color: rgb(168, 105, 38);">
                ${instructionText}
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理基本按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    // ESC 鍵返回營地或上一個場景
                    if (this.previousScene === 'camp') {
                        this.returnToCamp();
                    } else {
                        console.log("按下 ESC，但無明確的返回目標");
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 暂停键盘控制
    pauseKeyboardControls() {
        console.log("暂停客栈键盘控制");
        this.keyboardPaused = true;
    }

    // 恢复键盘控制
    resumeKeyboardControls() {
        console.log("恢复客栈键盘控制");
        this.keyboardPaused = false;
    }

    // 創建客棧專用選單面板
    createHostelMenuPanel() {
        console.log("創建客棧專用選單面板");

        // 創建選單面板容器
        this.menuPanel = document.createElement('div');
        this.menuPanel.id = 'hostelMenuPanel';
        this.menuPanel.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(168, 105, 38);
            border-radius: 10px;
            padding: 20px;
            z-index: 5;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        `;

        // 創建選單標題
        const title = document.createElement('h3');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 20px;
            border-bottom: 2px solid rgb(168, 105, 38);
            padding-bottom: 10px;
        `;
        this.menuPanel.appendChild(title);

        // 創建選單選項
        const menuOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '🎒 背包管理',
                action: () => this.showInventoryManagement()
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({
                text: '� 前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建選項按鈕
        menuOptions.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 12px;
                margin: 8px 0;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(168, 105, 38);
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            // 按鈕懸停效果
            button.onmouseover = () => {
                button.style.transform = 'translateX(5px)';
            };

            button.onmouseout = () => {
                button.style.transform = 'translateX(0)';
            };

            button.onclick = option.action;
            this.menuPanel.appendChild(button);
        });

        this.hostelContainer.appendChild(this.menuPanel);
        console.log("客棧選單面板創建完成");
    }









    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示客棧存檔選單");
        this.showSaveHostelDialog();
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 顯示背包管理界面
    showInventoryManagement() {
        console.log("顯示背包管理界面");

        // 暂停键盘事件监听
        this.pauseKeyboardControls();

        // 创建背包管理对话框
        this.createInventoryDialog();
    }

    // 创建背包管理对话框
    createInventoryDialog() {
        console.log("创建背包管理对话框");

        // 创建 dialog 元素
        const inventoryDialog = document.createElement("dialog");
        inventoryDialog.id = "inventoryDialog";
        inventoryDialog.style.cssText = `
            border: none;
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            padding: 0;
            margin: 0;
            background: rgba(0, 0, 0, 1);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
        `;

        // 创建背包管理内容
        const inventoryContent = document.createElement("div");
        inventoryContent.style.cssText = `
            width: 90%;
            height: 95%;
            background-image: url('./Public/hostel_bg.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 40px;
            box-sizing: border-box;
        `;

        const inventoryMidArea = document.createElement("div");
        inventoryMidArea.style.cssText = `
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;
            opacity: 0;
            animation: shopappear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
        `;

        // 左半边：行囊容器
        const publicBagContainer = this.createPublicBagContainer();

        // 右半边：角色背包容器
        const playerBagContainer = this.createPlayerBagContainer();

        // 关闭按钮
        const closeButton = this.createInventoryCloseButton(inventoryDialog);

        // 组装对话框
        inventoryMidArea.appendChild(publicBagContainer);
        inventoryMidArea.appendChild(playerBagContainer);
        inventoryContent.appendChild(inventoryMidArea);
        inventoryContent.appendChild(closeButton);
        inventoryDialog.appendChild(inventoryContent);

        // 添加到页面并显示
        document.body.appendChild(inventoryDialog);
        inventoryDialog.showModal();

        // 添加 Esc 键退出功能
        this.setupInventoryEscapeHandler(inventoryDialog);

        console.log("背包管理对话框已创建并显示");
    }

    // 创建行囊容器（左半边）
    createPublicBagContainer() {
        const publicBagContainer = document.createElement("div");
        publicBagContainer.id = "publicBagContainer";
        publicBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: left;
            overflow-y: auto;
            background: url(./Public/mybagwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-right: 20px;
        `;

        // 行囊标题
        const publicBagTitle = document.createElement("div");
        publicBagTitle.textContent = "行囊";
        publicBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            letter-spacing: 5px;
            margin-top: 10px;
            margin-bottom: 20px;
        `;
        publicBagContainer.appendChild(publicBagTitle);

        // 行囊物品容器
        const publicBagItemsContainer = document.createElement("div");
        publicBagItemsContainer.id = "publicBagItems";
        publicBagItemsContainer.style.cssText = `
            margin-left: 6%;
            margin-top: 5%;
            width: 90%;
            height: 75%;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        publicBagContainer.appendChild(publicBagItemsContainer);

        // 渲染行囊物品
        this.renderPublicBagItems(publicBagItemsContainer);

        return publicBagContainer;
    }

    // 创建角色背包容器（右半边）
    createPlayerBagContainer() {
        const playerBagContainer = document.createElement("div");
        playerBagContainer.id = "playerBagContainer";
        playerBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: right;
            overflow-y: auto;
            background: url(./Public/shopwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-left: 20px;
        `;

        // 角色背包标题容器
        const playerTitleContainer = document.createElement("div");
        playerTitleContainer.style.cssText = `
            position: relative;
            width: 100%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        `;

        // 上一位按钮
        const prevPlayerBtn = document.createElement("button");
        prevPlayerBtn.textContent = "◀";
        prevPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 角色背包标题
        const playerBagTitle = document.createElement("div");
        playerBagTitle.id = "playerBagTitle";
        playerBagTitle.textContent = "角色背包";
        playerBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            letter-spacing: 5px;
            flex: 1;
        `;

        // 下一位按钮
        const nextPlayerBtn = document.createElement("button");
        nextPlayerBtn.textContent = "▶";
        nextPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 组装标题容器
        playerTitleContainer.appendChild(prevPlayerBtn);
        playerTitleContainer.appendChild(playerBagTitle);
        playerTitleContainer.appendChild(nextPlayerBtn);
        playerBagContainer.appendChild(playerTitleContainer);

        // 角色背包物品容器
        const playerBagItemsContainer = document.createElement("div");
        playerBagItemsContainer.id = "playerBagItems";
        playerBagItemsContainer.style.cssText = `
            margin-left: 2%;
            margin-top: 5%;
            width: 88%;
            height: 75%;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        playerBagContainer.appendChild(playerBagItemsContainer);

        // 初始化角色背包切换系统
        this.initPlayerBagSwitchSystem(prevPlayerBtn, nextPlayerBtn, playerBagTitle, playerBagItemsContainer);

        return playerBagContainer;
    }

    // 创建关闭按钮
    createInventoryCloseButton(inventoryDialog) {
        const closeButton = document.createElement("button");
        closeButton.textContent = "關閉背包管理";
        closeButton.style.cssText = `
            padding: 15px 30px;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 10px;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            align-self: center;
            transition: all 0.3s ease;
            opacity: 0;
            animation: shopappear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
            margin-top: 20px;
        `;

        closeButton.addEventListener('click', () => {
            this.closeInventoryDialog(inventoryDialog);
        });

        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = 'rgb(165, 90, 24)';
            closeButton.style.color = 'white';
            closeButton.style.transform = 'scale(1.05)';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = 'rgb(247, 231, 173)';
            closeButton.style.color = 'rgb(168, 105, 38)';
            closeButton.style.transform = 'scale(1)';
        });

        return closeButton;
    }

    // 设置背包管理 Esc 键处理
    setupInventoryEscapeHandler(inventoryDialog) {
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeInventoryDialog(inventoryDialog);
                // 移除事件监听器
                document.removeEventListener('keydown', escapeHandler);
                console.log("按下 Esc 键，退出背包管理");
            }
        };

        // 添加事件监听器
        document.addEventListener('keydown', escapeHandler);

        // 保存事件处理器引用，以便在对话框关闭时清理
        inventoryDialog.escapeHandler = escapeHandler;
    }

    // 关闭背包管理对话框
    closeInventoryDialog(inventoryDialog) {
        console.log("关闭背包管理对话框");

        // 清理 Esc 键事件监听器
        if (inventoryDialog.escapeHandler) {
            document.removeEventListener('keydown', inventoryDialog.escapeHandler);
            inventoryDialog.escapeHandler = null;
            console.log("已清理背包管理 Esc 键事件监听器");
        }

        // 关闭对话框
        inventoryDialog.close();
        document.body.removeChild(inventoryDialog);

        // 恢复键盘控制
        this.resumeKeyboardControls();

        console.log("背包管理对话框已关闭，键盘控制已恢复");
    }

    // 渲染行囊物品
    renderPublicBagItems(container) {
        console.log("渲染行囊物品");

        // 清空容器
        container.innerHTML = '';

        // 统计物品数量
        const itemMap = new Map();
        if (typeof mybag !== 'undefined' && mybag.length > 0) {
            mybag.forEach(item => {
                if (itemMap.has(item.name)) {
                    const existing = itemMap.get(item.name);
                    existing.count += 1;
                } else {
                    itemMap.set(item.name, {
                        price: item.price || 0,
                        count: 1,
                        item: item
                    });
                }
            });
        }

        // 显示物品
        if (itemMap.size > 0) {
            itemMap.forEach((data, name) => {
                const itemDiv = document.createElement("div");
                itemDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 20px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    font-size: 24px;
                    color: rgb(168, 105, 38);
                    box-sizing: border-box;
                    border: 3px solid transparent;
                    transition: all 0.3s ease;
                `;

                itemDiv.innerHTML = `
                    <span style="font-weight: bold;">${name}</span>
                    <span style="color: rgb(165, 90, 24);">x${data.count}</span>
                `;

                // 悬停效果
                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.borderColor = 'rgb(165, 90, 24)';
                    itemDiv.style.backgroundColor = 'rgba(247, 231, 173, 0.3)';
                });

                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.borderColor = 'transparent';
                    itemDiv.style.backgroundColor = 'transparent';
                });

                // 点击事件：移动到角色背包
                itemDiv.addEventListener('click', () => {
                    this.moveItemToPlayerBag(data.item, name);
                });

                container.appendChild(itemDiv);
            });
        } else {
            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = "行囊是空的";
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 18px;
            `;
            container.appendChild(emptyMsg);
        }

        console.log(`行囊显示已更新，物品数量: ${itemMap.size}`);
    }

    // 初始化角色背包切换系统
    initPlayerBagSwitchSystem(prevBtn, nextBtn, titleElement, itemsContainer) {
        console.log("初始化角色背包切换系统");

        // 获取当前关卡的非 AI 玩家
        this.availablePlayers = this.getNonAIPlayers();

        if (this.availablePlayers.length === 0) {
            console.warn("没有找到非 AI 玩家");
            titleElement.textContent = "無可用角色";

            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = "沒有可用的角色背包";
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 18px;
            `;
            itemsContainer.appendChild(emptyMsg);

            // 禁用按钮
            prevBtn.disabled = true;
            nextBtn.disabled = true;
            prevBtn.style.opacity = '0.5';
            nextBtn.style.opacity = '0.5';
            return;
        }

        // 当前选中的角色索引
        this.currentPlayerIndex = 0;

        console.log("可用角色:", this.availablePlayers.map(player => player.name));

        // 设置按钮事件
        prevBtn.addEventListener('click', () => {
            this.switchToPreviousPlayer(titleElement, itemsContainer);
        });

        nextBtn.addEventListener('click', () => {
            this.switchToNextPlayer(titleElement, itemsContainer);
        });

        // 添加按钮悬停效果
        this.addPlayerBagButtonHoverEffects(prevBtn, nextBtn);

        // 初始化显示
        this.updatePlayerBagDisplay(titleElement, itemsContainer);
    }

    // 获取当前关卡的非 AI 玩家
    getNonAIPlayers() {
        console.log("获取当前关卡的非 AI 玩家");

        // 从当前关卡数据获取玩家
        if (typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined' &&
            controlLayer[currentLevel] && controlLayer[currentLevel].Players) {

            const levelPlayers = controlLayer[currentLevel].Players;
            const nonAIPlayers = [];

            levelPlayers.forEach(playerTemplate => {
                // 根据 ID 找到对应的玩家数据
                if (typeof playerData !== 'undefined') {
                    const player = playerData.find(p => p.id === playerTemplate.id);
                    if (player && !player.isAI) {
                        // 确保玩家有 Inventory 属性
                        if (!player.Inventory) {
                            player.Inventory = [];
                        }
                        nonAIPlayers.push(player);
                    }
                }
            });

            console.log(`找到 ${nonAIPlayers.length} 个非 AI 玩家`);
            return nonAIPlayers;
        }

        console.warn("无法获取当前关卡的玩家资料");
        return [];
    }

    // 切换到上一个角色
    switchToPreviousPlayer(titleElement, itemsContainer) {
        if (this.availablePlayers.length <= 1) return;

        this.currentPlayerIndex = (this.currentPlayerIndex - 1 + this.availablePlayers.length) % this.availablePlayers.length;
        console.log(`切换到上一个角色: ${this.availablePlayers[this.currentPlayerIndex].name}`);

        this.updatePlayerBagDisplay(titleElement, itemsContainer);
    }

    // 切换到下一个角色
    switchToNextPlayer(titleElement, itemsContainer) {
        if (this.availablePlayers.length <= 1) return;

        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.availablePlayers.length;
        console.log(`切换到下一个角色: ${this.availablePlayers[this.currentPlayerIndex].name}`);

        this.updatePlayerBagDisplay(titleElement, itemsContainer);
    }

    // 更新角色背包显示
    updatePlayerBagDisplay(titleElement, itemsContainer) {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentPlayerIndex];

        // 更新标题显示角色名称和背包容量
        const currentCount = currentPlayer.Inventory.length;
        const maxCount = 6;
        titleElement.textContent = `${currentPlayer.name} (${currentCount}/${maxCount})`;

        // 渲染角色背包物品
        this.renderPlayerBagItems(itemsContainer, currentPlayer);
    }

    // 渲染角色背包物品
    renderPlayerBagItems(container, player) {
        console.log(`渲染 ${player.name} 的背包物品`);

        // 清空容器
        container.innerHTML = '';

        // 统计物品数量
        const itemMap = new Map();
        if (player.Inventory && player.Inventory.length > 0) {
            player.Inventory.forEach(item => {
                if (itemMap.has(item.name)) {
                    const existing = itemMap.get(item.name);
                    existing.count += 1;
                } else {
                    itemMap.set(item.name, {
                        price: item.price || 0,
                        count: 1,
                        item: item
                    });
                }
            });
        }

        // 显示物品
        if (itemMap.size > 0) {
            itemMap.forEach((data, name) => {
                const itemDiv = document.createElement("div");
                itemDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 20px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    font-size: 24px;
                    color: rgb(168, 105, 38);
                    box-sizing: border-box;
                    border: 3px solid transparent;
                    transition: all 0.3s ease;
                `;

                itemDiv.innerHTML = `
                    <span style="font-weight: bold;">${name}</span>
                    <span style="color: rgb(165, 90, 24);">x${data.count}</span>
                `;

                // 悬停效果
                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.borderColor = 'rgb(165, 90, 24)';
                    itemDiv.style.backgroundColor = 'rgba(247, 231, 173, 0.3)';
                });

                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.borderColor = 'transparent';
                    itemDiv.style.backgroundColor = 'transparent';
                });

                // 点击事件：移动到行囊
                itemDiv.addEventListener('click', () => {
                    this.moveItemToPublicBag(data.item, name, player);
                });

                container.appendChild(itemDiv);
            });
        } else {
            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = `${player.name} 的背包是空的`;
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 18px;
            `;
            container.appendChild(emptyMsg);
        }

        console.log(`${player.name} 背包显示已更新，物品数量: ${itemMap.size}`);
    }

    // 添加按钮悬停效果
    addPlayerBagButtonHoverEffects(prevBtn, nextBtn) {
        // 上一位按钮悬停效果
        prevBtn.addEventListener('mouseenter', () => {
            if (!prevBtn.disabled) {
                prevBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                prevBtn.style.transform = 'scale(1.1)';
            }
        });

        prevBtn.addEventListener('mouseleave', () => {
            if (!prevBtn.disabled) {
                prevBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                prevBtn.style.transform = 'scale(1)';
            }
        });

        // 下一位按钮悬停效果
        nextBtn.addEventListener('mouseenter', () => {
            if (!nextBtn.disabled) {
                nextBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                nextBtn.style.transform = 'scale(1.1)';
            }
        });

        nextBtn.addEventListener('mouseleave', () => {
            if (!nextBtn.disabled) {
                nextBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                nextBtn.style.transform = 'scale(1)';
            }
        });
    }

    // 移动物品从行囊到角色背包
    moveItemToPlayerBag(item, itemName) {
        if (!this.availablePlayers || this.availablePlayers.length === 0) {
            alert("没有可用的角色背包");
            return;
        }

        const currentPlayer = this.availablePlayers[this.currentPlayerIndex];

        // 检查角色背包是否已满
        if (currentPlayer.Inventory.length >= 6) {
            alert(`${currentPlayer.name} 的背包已满！\n个人背包最多只能放6个物品。`);
            return;
        }

        // 从行囊中移除物品
        const itemIndex = mybag.findIndex(bagItem => bagItem.name === item.name);
        if (itemIndex === -1) {
            alert("在行囊中找不到该物品");
            return;
        }

        const removedItem = mybag.splice(itemIndex, 1)[0];

        // 添加到角色背包
        currentPlayer.Inventory.push(removedItem);

        console.log(`物品 ${itemName} 已从行囊移动到 ${currentPlayer.name} 的背包`);

        // 刷新显示
        this.refreshInventoryDisplay();

        // 显示成功消息
        this.showMoveItemMessage(`${itemName} 已移动到 ${currentPlayer.name} 的背包`);
    }

    // 移动物品从角色背包到行囊
    moveItemToPublicBag(item, itemName, player) {
        // 从角色背包中移除物品
        const itemIndex = player.Inventory.findIndex(bagItem => bagItem.name === item.name);
        if (itemIndex === -1) {
            alert(`在 ${player.name} 的背包中找不到该物品`);
            return;
        }

        const removedItem = player.Inventory.splice(itemIndex, 1)[0];

        // 添加到行囊
        if (typeof mybag === 'undefined') {
            window.mybag = [];
        }
        mybag.push(removedItem);

        console.log(`物品 ${itemName} 已从 ${player.name} 的背包移动到行囊`);

        // 刷新显示
        this.refreshInventoryDisplay();

        // 显示成功消息
        this.showMoveItemMessage(`${itemName} 已移动到行囊`);
    }

    // 刷新背包管理显示
    refreshInventoryDisplay() {
        console.log("刷新背包管理显示");

        // 刷新行囊显示
        const publicBagItems = document.getElementById("publicBagItems");
        if (publicBagItems) {
            this.renderPublicBagItems(publicBagItems);
        }

        // 刷新角色背包显示
        const playerBagItems = document.getElementById("playerBagItems");
        const playerBagTitle = document.getElementById("playerBagTitle");
        if (playerBagItems && playerBagTitle && this.availablePlayers && this.availablePlayers.length > 0) {
            this.updatePlayerBagDisplay(playerBagTitle, playerBagItems);
        }

        console.log("背包管理显示已刷新");
    }

    // 显示物品移动消息
    showMoveItemMessage(message) {
        // 创建消息对话框
        const messageDialog = document.createElement("dialog");
        messageDialog.id = "moveItemMessageDialog";
        messageDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            z-index: 10002;
        `;

        messageDialog.innerHTML = `
            <div style="
                background: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 4px solid rgb(165, 90, 24);
                border-radius: 15px;
                padding: 25px 35px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                min-width: 300px;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 15px 0; font-size: 22px; font-weight: bold;">✅ 移動成功！</h3>
                <p style="margin: 12px 0; font-size: 16px; font-weight: bold;">
                    ${message}
                </p>
            </div>
        `;

        document.body.appendChild(messageDialog);
        messageDialog.showModal();

        // 1.5秒后自动关闭
        setTimeout(() => {
            messageDialog.close();
            document.body.removeChild(messageDialog);
        }, 1500);
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
            
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 恢復原本的 sidebar 狀態
    restoreSidebar() {
        console.log("恢復原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar && this.originalSidebarState) {
            // 恢復保存的原始狀態
            sidebar.style.display = this.originalSidebarState.display;
            sidebar.style.visibility = this.originalSidebarState.visibility;
            sidebar.style.opacity = this.originalSidebarState.opacity;
            sidebar.style.zIndex = 9999;
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0px';
            sidebar.style.top = '0px';

            console.log("Sidebar 狀態已恢復到原始狀態:", this.originalSidebarState);
        } else if (sidebar) {
            // 如果沒有保存的狀態，使用默認值
            sidebar.style.display = 'flex';
            sidebar.style.flexDirection = 'column';
            sidebar.style.justifyContent = 'start';
            sidebar.style.alignItems = 'center';
            sidebar.style.justifySelf = 'flex-start';
            sidebar.style.gap = '10px';
            sidebar.style.overflow = 'hidden';
            sidebar.style.visibility = 'visible';
            sidebar.style.opacity = '1';
            sidebar.style.zIndex = '1000';

            console.log("Sidebar 狀態已恢復到默認狀態");
        } else {
            console.warn("找不到 sidebar 元素");
        }

        // 清除保存的狀態
        this.originalSidebarState = null;

        // 如果有營地場景的 sidebar 更新函數，調用它
        if (typeof updateCampSidebar === 'function') {
            updateCampSidebar();
        }

        // 觸發 sidebar 恢復事件，讓其他模組知道
        const sidebarRestoreEvent = new CustomEvent('sidebarRestored', {
            detail: { source: 'hostel' }
        });
        document.dispatchEvent(sidebarRestoreEvent);
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 移除選單面板
        if (this.menuPanel && this.menuPanel.parentNode) {
            this.menuPanel.parentNode.removeChild(this.menuPanel);
            this.menuPanel = null;
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // 恢復原本的 sidebar 狀態
        this.restoreSidebar();

        console.log("客棧場景清理完成");
    }

    // ===== 客棧存檔系統 =====

    // 獲取當前客棧狀態
    getCurrentHostelState() {
        console.log("收集當前客棧狀態");

        // 獲取當前關卡標題
        let levelTitle = "未知關卡";
        if (typeof currentLevel !== 'undefined' && typeof controlLayer !== 'undefined') {
            const levelData = controlLayer[currentLevel];
            if (levelData && levelData["標題"]) {
                levelTitle = levelData["標題"];
            }
        }

        const hostelState = {
            // 基本信息
            timestamp: Date.now(),
            hostelIndex: this.hostelIndex,
            previousScene: this.previousScene,

            // 關卡信息
            levelTitle: levelTitle,
            currentLevel: typeof currentLevel !== 'undefined' ? currentLevel : 0,

            // 玩家數據
            playerData: this.playerData,

            // 客棧特定狀態
            hostelSpecific: {
                isLoaded: this.isLoaded,
                isRunning: this.isRunning
            },

            // 背包和金錢（如果可用）
            inventory: {
                mybag: typeof mybag !== 'undefined' ? [...mybag] : [],
                mymoney: typeof mymoney !== 'undefined' ? mymoney : 0
            }
        };

        console.log("客棧狀態收集完成:", hostelState);
        return hostelState;
    }

    // 保存客棧狀態到指定槽位
    saveHostelState(slotIndex) {
        console.log(`保存客棧狀態到槽位 ${slotIndex}`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return false;
        }

        try {
            const hostelState = this.getCurrentHostelState();
            const storageKey = `hostelSave_${slotIndex}`;

            localStorage.setItem(storageKey, JSON.stringify(hostelState));
            console.log(`客棧狀態已保存到槽位 ${slotIndex}`);
            return true;
        } catch (error) {
            console.error("保存客棧狀態失敗:", error);
            return false;
        }
    }

    // 從指定槽位載入客棧狀態
    loadHostelState(slotIndex) {
        console.log(`從槽位 ${slotIndex} 載入客棧狀態`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                console.warn(`槽位 ${slotIndex} 沒有保存的客棧狀態`);
                return null;
            }

            const hostelState = JSON.parse(savedData);
            console.log(`客棧狀態已從槽位 ${slotIndex} 載入`);
            return hostelState;
        } catch (error) {
            console.error("載入客棧狀態失敗:", error);
            return null;
        }
    }

    // 獲取存檔槽位信息
    getHostelSlotInfo(slotIndex) {
        if (slotIndex < 0 || slotIndex >= 5) {
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                return null;
            }

            const hostelState = JSON.parse(savedData);
            return {
                timestamp: hostelState.timestamp,
                hostelIndex: hostelState.hostelIndex,
                previousScene: hostelState.previousScene,
                levelTitle: hostelState.levelTitle || "未知關卡",
                currentLevel: hostelState.currentLevel || 0,
                hasPlayerData: !!hostelState.playerData
            };
        } catch (error) {
            console.error("獲取客棧存檔槽位信息失敗:", error);
            return null;
        }
    }

    // 顯示客棧存檔對話框
    showSaveHostelDialog() {
        console.log("開啟客棧存檔介面");

        // 創建 dialog 元素
        const saveDialog = document.createElement("dialog");
        saveDialog.id = "save-hostel-dialog";
        saveDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const saveContent = document.createElement("div");
        saveContent.className = "save-content";
        saveContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "存取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        saveContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            saveDialog.close();
            this.hostelContainer.removeChild(saveDialog);
        });
        saveContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'save');
        }

        saveContent.appendChild(slotsContainer);
        saveDialog.appendChild(saveContent);
        this.hostelContainer.appendChild(saveDialog);
        saveDialog.showModal();
    }

    // 顯示客棧讀檔對話框（已移至 Init.js 的前历再续功能）
    showLoadHostelDialog() {
        console.log("開啟客棧讀檔介面");

        // 創建 dialog 元素
        const loadDialog = document.createElement("dialog");
        loadDialog.id = "load-hostel-dialog";
        loadDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const loadContent = document.createElement("div");
        loadContent.className = "save-content";
        loadContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        loadContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            this.hostelContainer.removeChild(loadDialog);
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'load');
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        this.hostelContainer.appendChild(loadDialog);
        loadDialog.showModal();
    }

    // 創建存檔槽
    createSaveSlot(container, slotIndex, mode) {
        const slot = document.createElement("div");
        slot.className = "save-slot";
        slot.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 110px;
            background-color: rgb(247, 231, 173);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            padding: 5px;
            cursor: pointer;
            box-sizing: border-box;
            opacity: 0;
        `;

        // 左欄：圖騰
        const totem = document.createElement("div");
        totem.className = "save-slot-totem";
        totem.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const totemImage = document.createElement("img");
        totemImage.draggable = false;
        totemImage.src = "./Public/saveicon.png";
        totemImage.style.cssText = `
            width: 360px;
            height: 95px;
        `;
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：客棧信息和存檔時間
        const middle = document.createElement("div");
        middle.className = "save-slot-middle";
        middle.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const hostelInfo = document.createElement("div");
        hostelInfo.className = "save-slot-level";
        hostelInfo.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 25px;
        `;
        const saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";
        saveTime.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 20px;
        `;

        // 獲取存檔信息
        const slotInfo = this.getHostelSlotInfo(slotIndex);
        if (slotInfo) {
            hostelInfo.style.display = "block";
            hostelInfo.textContent = `客棧 ${slotInfo.hostelIndex}`;
            saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            hostelInfo.style.display = "none";
            saveTime.textContent = "";
        }

        middle.appendChild(hostelInfo);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        const levelTitle = document.createElement("div");
        levelTitle.className = "save-slot-title";
        levelTitle.style.cssText = `
            width: 30%;
            text-align: center;
            font-weight: 600;
            color: rgb(168, 105, 38);
            font-size: 28px;
        `;

        // 顯示關卡標題，如果沒有存檔則顯示槽位編號
        if (slotInfo && slotInfo.levelTitle) {
            levelTitle.textContent = slotInfo.levelTitle;
        } else {
            levelTitle.textContent = `第 ${slotIndex + 1} 格`;
        }
        slot.appendChild(levelTitle);

        // 存檔槽點擊事件
        if (mode === 'save') {
            slot.addEventListener("click", () => {
                const confirmSave = confirm(`是否要將當前客棧存檔在第 ${slotIndex + 1} 格的位置？`);
                if (confirmSave) {
                    const success = this.saveHostelState(slotIndex);
                    if (success) {
                        // 更新顯示
                        const newSlotInfo = this.getHostelSlotInfo(slotIndex);
                        if (newSlotInfo) {
                            hostelInfo.style.display = "block";
                            hostelInfo.textContent = `客棧 ${newSlotInfo.hostelIndex}`;
                            saveTime.textContent = new Date(newSlotInfo.timestamp).toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                        alert("客棧存檔成功！");
                    } else {
                        alert("客棧存檔失敗！");
                    }
                }
            });
        } else if (mode === 'load') {
            slot.addEventListener("click", () => {
                if (!slotInfo) {
                    return;
                }

                const confirmLoad = confirm(`是否要讀取第 ${slotIndex + 1} 格的客棧存檔？\n客棧：${slotInfo.hostelIndex}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
                if (confirmLoad) {
                    const hostelState = this.loadHostelState(slotIndex);
                    if (hostelState) {
                        // 關閉對話框
                        const loadDialog = document.getElementById('load-hostel-dialog');
                        if (loadDialog) {
                            loadDialog.close();
                            this.hostelContainer.removeChild(loadDialog);
                        }

                        // 載入客棧場景
                        this.loadHostelScene(hostelState);
                        alert("客棧讀取成功！");
                    } else {
                        alert("客棧讀取失敗！");
                    }
                }
            });
        }

        // 添加動畫
        const delay = slotIndex * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        container.appendChild(slot);
    }

    // 載入客棧場景
    loadHostelScene(hostelState) {
        console.log("載入客棧場景狀態:", hostelState);

        try {
            // 使用場景管理器切換到客棧場景
            if (typeof sceneManager !== 'undefined') {
                sceneManager.switchToHostel(hostelState.hostelIndex, hostelState.previousScene);
                console.log("客棧場景載入成功");
            } else {
                console.error("場景管理器不可用");
                alert("無法載入客棧場景");
            }
        } catch (error) {
            console.error("載入客棧場景失敗:", error);
            alert("載入客棧場景失敗");
        }
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}