/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        // Sidebar 狀態保存
        this.originalSidebarState = null;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單面板
            this.createHostelMenuPanel();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 保存並隱藏原本的 sidebar 狀態
    saveSidebarState() {
        console.log("保存並隱藏原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            // 保存原始狀態
            this.originalSidebarState = {
                display: sidebar.style.display || getComputedStyle(sidebar).display,
                visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility,
                opacity: sidebar.style.opacity || getComputedStyle(sidebar).opacity,
                zIndex: sidebar.style.zIndex || getComputedStyle(sidebar).zIndex
            };

            // 隱藏 sidebar（客棧有自己的選單系統）
            sidebar.style.display = 'none';

            console.log("Sidebar 狀態已保存並隱藏:", this.originalSidebarState);
        } else {
            console.warn("找不到 sidebar 元素，無法保存狀態");
            this.originalSidebarState = null;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 保存並隱藏原本的 sidebar
        this.saveSidebarState();

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎來到客棧！'
            : '🏨 歡迎來到休息區！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: rgb(168, 105, 38);; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;color: rgb(168, 105, 38);">
                ${instructionText}
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理基本按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    // ESC 鍵返回營地或上一個場景
                    if (this.previousScene === 'camp') {
                        this.returnToCamp();
                    } else {
                        console.log("按下 ESC，但無明確的返回目標");
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 創建客棧專用選單面板
    createHostelMenuPanel() {
        console.log("創建客棧專用選單面板");

        // 創建選單面板容器
        this.menuPanel = document.createElement('div');
        this.menuPanel.id = 'hostelMenuPanel';
        this.menuPanel.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(168, 105, 38);
            border-radius: 10px;
            padding: 20px;
            z-index: 5;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        `;

        // 創建選單標題
        const title = document.createElement('h3');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 20px;
            border-bottom: 2px solid rgb(168, 105, 38);
            padding-bottom: 10px;
        `;
        this.menuPanel.appendChild(title);

        // 創建選單選項
        const menuOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '🎒 背包管理',
                action: () => this.showBagManagement()
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({
                text: '� 前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建選項按鈕
        menuOptions.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 12px;
                margin: 8px 0;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(168, 105, 38);
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            // 按鈕懸停效果
            button.onmouseover = () => {
                button.style.transform = 'translateX(5px)';
            };

            button.onmouseout = () => {
                button.style.transform = 'translateX(0)';
            };

            button.onclick = option.action;
            this.menuPanel.appendChild(button);
        });

        this.hostelContainer.appendChild(this.menuPanel);
        console.log("客棧選單面板創建完成");
    }









    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示客棧存檔選單");
        this.showSaveHostelDialog();
    }

    // 顯示背包管理界面
    showBagManagement() {
        console.log("顯示背包管理界面");

        // 創建背包管理對話框
        const bagDialog = document.createElement("dialog");
        bagDialog.id = "bagManagementDialog";
        bagDialog.style.cssText = `
            border: none;
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            padding: 0;
            margin: 0;
            background:transparent;
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
        `;

        // 創建背包管理內容
        const bagContent = document.createElement("div");
        bagContent.style.cssText = `
            width: 90%;
            height: 95%;
            background-image: url('./Public/hostel.gif');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 40px;
            box-sizing: border-box;
        `;

        // 創建中間區域（左右分割）
        const bagMidArea = document.createElement("div");
        bagMidArea.style.cssText = `
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;
            opacity: 0;
            animation: bagAppear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
        `;

        // 左半邊：行囊（公共背包）
        const publicBagContainer = document.createElement("div");
        publicBagContainer.id = "publicBagContainer";
        publicBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: left;
            overflow-y: auto;
            background: url(./Public/mybagwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-right: 5%;
        `;

        // 行囊標題
        const publicBagTitle = document.createElement("div");
        publicBagTitle.textContent = "行囊";
        publicBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            margin-top: 5px;
            width: 30%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            letter-spacing: 5px;
            position: relative;
            float: left;
            left: 70px;
        `;
        publicBagContainer.appendChild(publicBagTitle);

        // 行囊內容區域
        const publicBagArea = document.createElement("div");
        publicBagArea.style.cssText = `
            margin-left: 9%;
            margin-top: 13%;
            width: 88%;
            height: 72%;
            outline: none;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        publicBagContainer.appendChild(publicBagArea);

        // 右半邊：個人背包
        const personalBagContainer = document.createElement("div");
        personalBagContainer.id = "personalBagContainer";
        personalBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: right;
            overflow-y: auto;
            background: url(./Public/shopwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;

        // 個人背包標題
        const personalBagTitle = document.createElement("div");
        personalBagTitle.textContent = "個人背包";
        personalBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            margin-top: 5px;
            width: 30%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            letter-spacing: 5px;
            position: relative;
            float: right;
            right: 70px;
        `;
        personalBagContainer.appendChild(personalBagTitle);

        // 個人背包內容區域
        const personalBagArea = document.createElement("div");
        personalBagArea.style.cssText = `
            margin-left: 2%;
            margin-top: 12%;
            width: 88%;
            height: 72%;
            outline: none;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        personalBagContainer.appendChild(personalBagArea);

        // 初始化背包切換系統
        this.initBagManagementSystem(publicBagArea, personalBagArea);

        // 關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.textContent = "關閉背包";
        closeButton.style.cssText = `
            padding: 10px 20px;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            align-self: center;
            transition: background-color 0.3s ease;
            opacity: 0;
            animation: bagAppear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
        `;

        closeButton.addEventListener('click', () => {
            this.closeBagManagement(bagDialog);
        });

        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = 'rgb(165, 90, 24)';
            closeButton.style.color = 'white';
            closeButton.style.transform = 'scale(1.05)';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = 'rgb(247, 231, 173)';
            closeButton.style.color = 'rgb(168, 105, 38)';
            closeButton.style.transform = 'scale(1)';
        });

        // 組裝對話框
        bagMidArea.appendChild(publicBagContainer);
        bagMidArea.appendChild(personalBagContainer);
        bagContent.appendChild(bagMidArea);
        bagContent.appendChild(closeButton);
        bagDialog.appendChild(bagContent);

        // 添加到頁面並顯示
        document.body.appendChild(bagDialog);
        bagDialog.showModal();

        // 添加 Esc 鍵退出功能
        this.setupBagEscapeHandler(bagDialog);

        console.log("背包管理對話框已創建並顯示");
    }

    // 初始化背包管理系統
    initBagManagementSystem(publicBagArea, personalBagArea) {
        console.log("初始化背包管理系統");

        // 獲取當前關卡的非 AI 玩家
        this.availablePlayers = this.getNonAIPlayers();

        if (this.availablePlayers.length === 0) {
            console.error("沒有找到可用的玩家！");

            // 顯示錯誤信息給用戶
            this.showNoPlayersError(personalBagArea);

            // 仍然渲染行囊，但個人背包顯示錯誤信息
            this.renderPublicBag(publicBagArea);
            return;
        }

        console.log("可用玩家列表:", this.availablePlayers.map(p => ({
            name: p.name,
            bagItems: p.mybag ? p.mybag.length : 0,
            curHP: p.curHP,
            isAI: p.isAI
        })));

        this.currentBagIndex = 0; // 默認顯示第一個玩家背包

        // 渲染行囊（公共背包）
        this.renderPublicBag(publicBagArea);

        // 渲染個人背包
        this.renderPersonalBag(personalBagArea);
    }

    // 顯示沒有玩家的錯誤信息
    showNoPlayersError(container) {
        console.log("顯示沒有玩家的錯誤信息");

        container.innerHTML = '';

        const errorContainer = document.createElement("div");
        errorContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 40px;
            text-align: center;
        `;

        const errorIcon = document.createElement("div");
        errorIcon.textContent = "⚠️";
        errorIcon.style.cssText = `
            font-size: 60px;
            margin-bottom: 20px;
        `;

        const errorTitle = document.createElement("div");
        errorTitle.textContent = "無法載入角色數據";
        errorTitle.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        `;

        const errorMessage = document.createElement("div");
        errorMessage.innerHTML = `
            <p style="color: rgb(165, 90, 24); font-size: 18px; margin-bottom: 10px;">
                找不到當前關卡的角色數據
            </p>
            <p style="color: rgb(165, 90, 24); font-size: 16px; margin-bottom: 20px;">
                請確認：<br>
                • 遊戲是否正確載入<br>
                • 角色數據是否存在<br>
                • 是否從正確的場景進入客棧
            </p>
        `;

        const refreshButton = document.createElement("button");
        refreshButton.textContent = "重新載入";
        refreshButton.style.cssText = `
            padding: 10px 20px;
            background: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        `;

        refreshButton.addEventListener('click', () => {
            console.log("用戶點擊重新載入");
            this.initBagManagementSystem(
                document.querySelector('#publicBagContainer div:last-child'),
                container
            );
        });

        refreshButton.addEventListener('mouseenter', () => {
            refreshButton.style.backgroundColor = 'rgb(165, 90, 24)';
            refreshButton.style.color = 'white';
        });

        refreshButton.addEventListener('mouseleave', () => {
            refreshButton.style.backgroundColor = 'rgb(247, 231, 173)';
            refreshButton.style.color = 'rgb(168, 105, 38)';
        });

        errorContainer.appendChild(errorIcon);
        errorContainer.appendChild(errorTitle);
        errorContainer.appendChild(errorMessage);
        errorContainer.appendChild(refreshButton);
        container.appendChild(errorContainer);
    }

    // 獲取當前關卡中的非 AI 玩家
    getNonAIPlayers() {
        console.log("獲取當前關卡中的非 AI 玩家");
        console.log("當前環境變數檢查:");
        console.log("- typeof playerData:", typeof playerData);
        console.log("- typeof controlLayer:", typeof controlLayer);
        console.log("- typeof currentLevel:", typeof currentLevel);
        console.log("- this.playerData:", this.playerData ? "存在" : "不存在");

        let availablePlayers = [];

        // 方法1: 從全局 playerData 獲取
        if (typeof playerData !== 'undefined' && Array.isArray(playerData)) {
            console.log("playerData 內容:", playerData);
            availablePlayers = playerData.filter(player => {
                const isValid = player &&
                    !player.isAI &&
                    player.curHP > 0 &&
                    player.name &&
                    player.name.trim() !== '';
                console.log(`檢查玩家 ${player?.name}: isAI=${player?.isAI}, curHP=${player?.curHP}, 有效=${isValid}`);
                return isValid;
            });
            console.log("從 playerData 獲取到玩家:", availablePlayers.map(p => p.name));
        }

        // 方法2: 從當前關卡數據獲取（如果方法1沒有結果）
        if (availablePlayers.length === 0 && typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined') {
            console.log(`檢查關卡數據: currentLevel=${currentLevel}`);
            const levelData = controlLayer[currentLevel];
            console.log("關卡數據:", levelData);

            if (levelData && levelData.players) {
                console.log("關卡玩家數據:", levelData.players);
                availablePlayers = levelData.players.filter(player => {
                    const isValid = player &&
                        !player.isAI &&
                        player.curHP > 0 &&
                        player.name &&
                        player.name.trim() !== '';
                    console.log(`檢查關卡玩家 ${player?.name}: isAI=${player?.isAI}, curHP=${player?.curHP}, 有效=${isValid}`);
                    return isValid;
                });
                console.log("從關卡數據獲取到玩家:", availablePlayers.map(p => p.name));
            }
        }

        // 方法3: 從客棧保存的玩家數據獲取（如果前面都沒有結果）
        if (availablePlayers.length === 0 && this.playerData && Array.isArray(this.playerData)) {
            console.log("客棧玩家數據:", this.playerData);
            availablePlayers = this.playerData.filter(player => {
                const isValid = player &&
                    !player.isAI &&
                    player.curHP > 0 &&
                    player.name &&
                    player.name.trim() !== '';
                console.log(`檢查客棧玩家 ${player?.name}: isAI=${player?.isAI}, curHP=${player?.curHP}, 有效=${isValid}`);
                return isValid;
            });
            console.log("從客棧數據獲取到玩家:", availablePlayers.map(p => p.name));
        }

        // 方法4: 嘗試從 window 對象獲取
        if (availablePlayers.length === 0) {
            console.log("嘗試從 window 對象獲取玩家數據");

            // 檢查 window.playerData
            if (window.playerData && Array.isArray(window.playerData)) {
                console.log("window.playerData:", window.playerData);
                availablePlayers = window.playerData.filter(player => {
                    const isValid = player &&
                        !player.isAI &&
                        player.curHP > 0 &&
                        player.name &&
                        player.name.trim() !== '';
                    return isValid;
                });
                console.log("從 window.playerData 獲取到玩家:", availablePlayers.map(p => p.name));
            }

            // 檢查其他可能的全局變數
            if (availablePlayers.length === 0) {
                const possibleVars = ['players', 'gamePlayerData', 'currentPlayers'];
                for (const varName of possibleVars) {
                    if (window[varName] && Array.isArray(window[varName])) {
                        console.log(`檢查 window.${varName}:`, window[varName]);
                        const filtered = window[varName].filter(player => {
                            const isValid = player &&
                                !player.isAI &&
                                player.curHP > 0 &&
                                player.name &&
                                player.name.trim() !== '';
                            return isValid;
                        });
                        if (filtered.length > 0) {
                            availablePlayers = filtered;
                            console.log(`從 window.${varName} 獲取到玩家:`, availablePlayers.map(p => p.name));
                            break;
                        }
                    }
                }
            }
        }

        // 確保每個玩家都有背包
        availablePlayers.forEach(player => {
            if (!player.mybag) {
                player.mybag = [];
                console.log(`為玩家 ${player.name} 初始化空背包`);
            }
        });

        console.log(`總共找到 ${availablePlayers.length} 個可用玩家`);

        // 如果還是沒有找到玩家，提供更詳細的調試信息
        if (availablePlayers.length === 0) {
            console.warn("未找到任何可用玩家，調試信息:");
            console.log("- 全局變數列表:", Object.keys(window).filter(key => key.toLowerCase().includes('player')));
            console.log("- 當前 this 對象:", this);
            console.log("- 建議檢查玩家數據是否正確載入");
        }

        return availablePlayers;
    }

    // 渲染行囊（公共背包）
    renderPublicBag(container) {
        console.log("渲染行囊");
        container.innerHTML = '';

        const publicBag = typeof mybag !== 'undefined' ? mybag : [];

        if (publicBag.length === 0) {
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = "行囊是空的";
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 50px;
            `;
            container.appendChild(emptyMessage);
            return;
        }

        // 統計物品數量
        const itemMap = new Map();
        publicBag.forEach(item => {
            const key = item.name || item;
            itemMap.set(key, (itemMap.get(key) || 0) + 1);
        });

        // 渲染物品（參考 CampScene.js 樣式）
        itemMap.forEach((count, itemName) => {
            const itemDiv = document.createElement("div");
            itemDiv.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                margin-bottom: 10px;
                cursor: pointer;
                font-size: 24px;
                color: rgb(168, 105, 38);
                box-sizing: border-box;
                border: 3px solid transparent;
                transition: all 0.3s ease;
            `;

            itemDiv.innerHTML = `
                <span style="font-weight: bold; color: rgb(168, 105, 38);">${itemName} 【${count}件】</span>
                <span style="color: rgb(165, 90, 24); font-weight: bold;">移至背包</span>
            `;

            // 懸停效果（參考 CampScene.js）
            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.borderColor = 'brown';
                itemDiv.style.backgroundColor = 'rgba(168, 105, 38, 0.1)';
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.borderColor = 'transparent';
                itemDiv.style.backgroundColor = 'transparent';
            });

            // 點擊事件：移動到個人背包
            itemDiv.addEventListener('click', () => {
                this.moveItemToPersonalBag(itemName);
            });

            container.appendChild(itemDiv);
        });
    }

    // 渲染個人背包
    renderPersonalBag(container) {
        console.log("渲染個人背包，可用玩家數量:", this.availablePlayers.length);
        container.innerHTML = '';

        if (this.availablePlayers.length === 0) {
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = "沒有可用的角色";
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 50px;
            `;
            container.appendChild(emptyMessage);
            return;
        }

        // 背包標題容器（參考 CampScene.js 樣式）
        const bagTitleContainer = document.createElement("div");
        bagTitleContainer.style.cssText = `
            position: absolute;
            width: 30%;
            height: 10%;
            margin-left: 50px;
            margin-top: -12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        `;

        // 上一位按鈕（參考 CampScene.js 樣式）
        const prevBtn = document.createElement("button");
        prevBtn.textContent = "◀";
        prevBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        `;

        // 角色名稱標題（參考 CampScene.js 樣式）
        const playerName = document.createElement("div");
        playerName.id = "playerBagTitle";
        playerName.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            letter-spacing: 5px;
            flex: 1;
        `;

        // 下一位按鈕（參考 CampScene.js 樣式）
        const nextBtn = document.createElement("button");
        nextBtn.textContent = "▶";
        nextBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        `;

        // 按鈕懸停效果（參考 CampScene.js）
        [prevBtn, nextBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                btn.style.transform = 'scale(1.1)';
                btn.style.boxShadow = '0 0 10px rgba(165, 90, 24, 0.5)';
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                btn.style.transform = 'scale(1)';
                btn.style.boxShadow = 'none';
            });
        });

        // 組裝標題容器
        bagTitleContainer.appendChild(prevBtn);
        bagTitleContainer.appendChild(playerName);
        bagTitleContainer.appendChild(nextBtn);

        // 切換事件（參考 CampScene.js 的邏輯）
        prevBtn.addEventListener('click', () => {
            this.currentBagIndex = (this.currentBagIndex - 1 + this.availablePlayers.length) % this.availablePlayers.length;
            console.log(`切換到上一個角色，索引: ${this.currentBagIndex}`);
            this.updatePersonalBagDisplay(container);
        });

        nextBtn.addEventListener('click', () => {
            this.currentBagIndex = (this.currentBagIndex + 1) % this.availablePlayers.length;
            console.log(`切換到下一個角色，索引: ${this.currentBagIndex}`);
            this.updatePersonalBagDisplay(container);
        });

        // 如果只有一個角色，禁用切換按鈕
        if (this.availablePlayers.length <= 1) {
            prevBtn.disabled = true;
            nextBtn.disabled = true;
            prevBtn.style.opacity = '0.3';
            nextBtn.style.opacity = '0.3';
            prevBtn.style.cursor = 'not-allowed';
            nextBtn.style.cursor = 'not-allowed';
        }

        // 將標題容器添加到個人背包容器
        container.appendChild(bagTitleContainer);

        // 創建背包內容區域（參考 CampScene.js 樣式）
        const bagContentArea = document.createElement("div");
        bagContentArea.id = "personalBagContent";
        bagContentArea.style.cssText = `
            margin-left: 6%;
            margin-top: 10%;
            width: 90%;
            height: 72%;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        container.appendChild(bagContentArea);

        // 更新顯示
        this.updatePersonalBagDisplay(container);
    }

    // 更新個人背包顯示
    updatePersonalBagDisplay(container) {
        console.log(`更新個人背包顯示，當前角色索引: ${this.currentBagIndex}`);

        const playerNameElement = container.querySelector('#playerBagTitle');
        const bagContentArea = container.querySelector('#personalBagContent');

        if (!bagContentArea) {
            console.error("找不到背包內容區域");
            return;
        }

        if (this.currentBagIndex >= this.availablePlayers.length) {
            console.warn("角色索引超出範圍，重置為0");
            this.currentBagIndex = 0;
        }

        const currentPlayer = this.availablePlayers[this.currentBagIndex];
        console.log(`當前角色: ${currentPlayer.name}，背包物品數量: ${currentPlayer.mybag ? currentPlayer.mybag.length : 0}`);

        // 更新角色名稱顯示（參考 CampScene.js）
        if (playerNameElement) {
            playerNameElement.textContent = currentPlayer.name;
        }

        // 清空內容區域
        bagContentArea.innerHTML = '';

        const playerBag = currentPlayer.mybag || [];

        if (playerBag.length === 0) {
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = `${currentPlayer.name} 的背包是空的`;
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 30px;
                padding: 20px;
                background: rgba(247, 231, 173, 0.3);
                border-radius: 8px;
                border: 2px dashed rgb(165, 90, 24);
            `;
            bagContentArea.appendChild(emptyMessage);
            return;
        }

        // 統計物品數量
        const itemMap = new Map();
        playerBag.forEach(item => {
            const key = item.name || item;
            itemMap.set(key, (itemMap.get(key) || 0) + 1);
        });

        console.log(`${currentPlayer.name} 的背包物品統計:`, Array.from(itemMap.entries()));

        // 渲染物品（參考 CampScene.js 樣式）
        itemMap.forEach((count, itemName) => {
            const itemDiv = document.createElement("div");
            itemDiv.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                margin-bottom: 10px;
                cursor: pointer;
                font-size: 24px;
                color: rgb(168, 105, 38);
                box-sizing: border-box;
                border: 3px solid transparent;
                transition: all 0.3s ease;
            `;

            itemDiv.innerHTML = `
                <span style="font-weight: bold; color: rgb(168, 105, 38);">${itemName} 【${count}件】</span>
                <span style="color: rgb(165, 90, 24); font-weight: bold;">移至行囊</span>
            `;

            // 懸停效果（參考 CampScene.js）
            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.borderColor = 'brown';
                itemDiv.style.backgroundColor = 'rgba(168, 105, 38, 0.1)';
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.borderColor = 'transparent';
                itemDiv.style.backgroundColor = 'transparent';
            });

            // 點擊事件：移動到行囊
            itemDiv.addEventListener('click', () => {
                console.log(`點擊移動物品 ${itemName} 從 ${currentPlayer.name} 到行囊`);
                this.moveItemToPublicBag(itemName, this.currentBagIndex);
            });

            bagContentArea.appendChild(itemDiv);
        });
    }

    // 移動物品到個人背包
    moveItemToPersonalBag(itemName) {
        console.log(`嘗試移動物品 ${itemName} 到個人背包`);

        if (this.availablePlayers.length === 0) {
            alert("沒有可用的角色");
            return;
        }

        const currentPlayer = this.availablePlayers[this.currentBagIndex];
        console.log(`目標角色: ${currentPlayer.name}`);

        // 從行囊移除物品
        if (typeof mybag !== 'undefined' && Array.isArray(mybag)) {
            const itemIndex = mybag.findIndex(item => (item.name || item) === itemName);
            if (itemIndex !== -1) {
                const removedItem = mybag.splice(itemIndex, 1)[0];
                console.log(`從行囊移除物品:`, removedItem);

                // 添加到個人背包
                if (!currentPlayer.mybag) {
                    currentPlayer.mybag = [];
                    console.log(`為 ${currentPlayer.name} 初始化背包`);
                }
                currentPlayer.mybag.push(removedItem);
                console.log(`物品已添加到 ${currentPlayer.name} 的背包，當前背包物品數: ${currentPlayer.mybag.length}`);

                // 同步更新到全局 playerData（如果存在）
                this.syncPlayerDataToGlobal(currentPlayer);

                // 重新渲染
                this.renderPublicBag(document.querySelector('#publicBagContainer div:last-child'));
                this.updatePersonalBagDisplay(document.querySelector('#personalBagContainer'));

                console.log(`物品 ${itemName} 已成功移動到 ${currentPlayer.name} 的背包`);
            } else {
                console.warn(`在行囊中找不到物品: ${itemName}`);
            }
        } else {
            console.warn("行囊 mybag 不存在或不是數組");
        }
    }

    // 移動物品到行囊
    moveItemToPublicBag(itemName, playerIndex) {
        console.log(`嘗試移動物品 ${itemName} 從角色 ${playerIndex} 到行囊`);

        const player = this.availablePlayers[playerIndex];
        if (!player) {
            console.warn(`找不到索引為 ${playerIndex} 的角色`);
            return;
        }

        if (!player.mybag || !Array.isArray(player.mybag)) {
            console.warn(`角色 ${player.name} 的背包不存在或不是數組`);
            return;
        }

        // 從個人背包移除物品
        const itemIndex = player.mybag.findIndex(item => (item.name || item) === itemName);
        if (itemIndex !== -1) {
            const removedItem = player.mybag.splice(itemIndex, 1)[0];
            console.log(`從 ${player.name} 的背包移除物品:`, removedItem);

            // 添加到行囊
            if (typeof mybag !== 'undefined' && Array.isArray(mybag)) {
                mybag.push(removedItem);
                console.log(`物品已添加到行囊，當前行囊物品數: ${mybag.length}`);
            } else {
                console.warn("行囊 mybag 不存在或不是數組，嘗試初始化");
                if (typeof window !== 'undefined') {
                    window.mybag = [removedItem];
                }
            }

            // 同步更新到全局 playerData
            this.syncPlayerDataToGlobal(player);

            // 重新渲染
            this.renderPublicBag(document.querySelector('#publicBagContainer div:last-child'));
            this.updatePersonalBagDisplay(document.querySelector('#personalBagContainer'));

            console.log(`物品 ${itemName} 已成功移動到行囊`);
        } else {
            console.warn(`在 ${player.name} 的背包中找不到物品: ${itemName}`);
        }
    }

    // 同步玩家數據到全局
    syncPlayerDataToGlobal(updatedPlayer) {
        try {
            // 同步到全局 playerData
            if (typeof playerData !== 'undefined' && Array.isArray(playerData)) {
                const globalPlayerIndex = playerData.findIndex(p => p.name === updatedPlayer.name);
                if (globalPlayerIndex !== -1) {
                    playerData[globalPlayerIndex].mybag = [...(updatedPlayer.mybag || [])];
                    console.log(`已同步 ${updatedPlayer.name} 的背包到全局 playerData`);
                }
            }

            // 同步到關卡數據
            if (typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined') {
                const levelData = controlLayer[currentLevel];
                if (levelData && levelData.players) {
                    const levelPlayerIndex = levelData.players.findIndex(p => p.name === updatedPlayer.name);
                    if (levelPlayerIndex !== -1) {
                        levelData.players[levelPlayerIndex].mybag = [...(updatedPlayer.mybag || [])];
                        console.log(`已同步 ${updatedPlayer.name} 的背包到關卡數據`);
                    }
                }
            }

            // 同步到客棧數據
            if (this.playerData && Array.isArray(this.playerData)) {
                const hostelPlayerIndex = this.playerData.findIndex(p => p.name === updatedPlayer.name);
                if (hostelPlayerIndex !== -1) {
                    this.playerData[hostelPlayerIndex].mybag = [...(updatedPlayer.mybag || [])];
                    console.log(`已同步 ${updatedPlayer.name} 的背包到客棧數據`);
                }
            }
        } catch (error) {
            console.error("同步玩家數據時發生錯誤:", error);
        }
    }

    // 設置背包 Esc 鍵處理
    setupBagEscapeHandler(bagDialog) {
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeBagManagement(bagDialog);
                document.removeEventListener('keydown', escapeHandler);
                console.log("按下 Esc 鍵，退出背包管理");
            }
        };

        document.addEventListener('keydown', escapeHandler);
        bagDialog.escapeHandler = escapeHandler;
    }

    // 關閉背包管理
    closeBagManagement(bagDialog) {
        console.log("關閉背包管理");

        // 清理 Esc 鍵事件監聽器
        if (bagDialog.escapeHandler) {
            document.removeEventListener('keydown', bagDialog.escapeHandler);
            bagDialog.escapeHandler = null;
        }

        // 關閉對話框
        bagDialog.close();
        document.body.removeChild(bagDialog);

        console.log("背包管理已關閉");
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
            
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 恢復原本的 sidebar 狀態
    restoreSidebar() {
        console.log("恢復原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar && this.originalSidebarState) {
            // 恢復保存的原始狀態
            sidebar.style.display = this.originalSidebarState.display;
            sidebar.style.visibility = this.originalSidebarState.visibility;
            sidebar.style.opacity = this.originalSidebarState.opacity;
            sidebar.style.zIndex = 9999;
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0px';
            sidebar.style.top = '0px';

            console.log("Sidebar 狀態已恢復到原始狀態:", this.originalSidebarState);
        } else if (sidebar) {
            // 如果沒有保存的狀態，使用默認值
            sidebar.style.display = 'flex';
            sidebar.style.flexDirection = 'column';
            sidebar.style.justifyContent = 'start';
            sidebar.style.alignItems = 'center';
            sidebar.style.justifySelf = 'flex-start';
            sidebar.style.gap = '10px';
            sidebar.style.overflow = 'hidden';
            sidebar.style.visibility = 'visible';
            sidebar.style.opacity = '1';
            sidebar.style.zIndex = '1000';

            console.log("Sidebar 狀態已恢復到默認狀態");
        } else {
            console.warn("找不到 sidebar 元素");
        }

        // 清除保存的狀態
        this.originalSidebarState = null;

        // 如果有營地場景的 sidebar 更新函數，調用它
        if (typeof updateCampSidebar === 'function') {
            updateCampSidebar();
        }

        // 觸發 sidebar 恢復事件，讓其他模組知道
        const sidebarRestoreEvent = new CustomEvent('sidebarRestored', {
            detail: { source: 'hostel' }
        });
        document.dispatchEvent(sidebarRestoreEvent);
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 移除選單面板
        if (this.menuPanel && this.menuPanel.parentNode) {
            this.menuPanel.parentNode.removeChild(this.menuPanel);
            this.menuPanel = null;
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // 恢復原本的 sidebar 狀態
        this.restoreSidebar();

        console.log("客棧場景清理完成");
    }

    // ===== 客棧存檔系統 =====

    // 獲取當前客棧狀態
    getCurrentHostelState() {
        console.log("收集當前客棧狀態");

        // 獲取當前關卡標題
        let levelTitle = "未知關卡";
        if (typeof currentLevel !== 'undefined' && typeof controlLayer !== 'undefined') {
            const levelData = controlLayer[currentLevel];
            if (levelData && levelData["標題"]) {
                levelTitle = levelData["標題"];
            }
        }

        const hostelState = {
            // 基本信息
            timestamp: Date.now(),
            hostelIndex: this.hostelIndex,
            previousScene: this.previousScene,

            // 關卡信息
            levelTitle: levelTitle,
            currentLevel: typeof currentLevel !== 'undefined' ? currentLevel : 0,

            // 玩家數據
            playerData: this.playerData,

            // 客棧特定狀態
            hostelSpecific: {
                isLoaded: this.isLoaded,
                isRunning: this.isRunning
            },

            // 背包和金錢（如果可用）
            inventory: {
                mybag: typeof mybag !== 'undefined' ? [...mybag] : [],
                mymoney: typeof mymoney !== 'undefined' ? mymoney : 0
            }
        };

        console.log("客棧狀態收集完成:", hostelState);
        return hostelState;
    }

    // 保存客棧狀態到指定槽位
    saveHostelState(slotIndex) {
        console.log(`保存客棧狀態到槽位 ${slotIndex}`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return false;
        }

        try {
            const hostelState = this.getCurrentHostelState();
            const storageKey = `hostelSave_${slotIndex}`;

            localStorage.setItem(storageKey, JSON.stringify(hostelState));
            console.log(`客棧狀態已保存到槽位 ${slotIndex}`);
            return true;
        } catch (error) {
            console.error("保存客棧狀態失敗:", error);
            return false;
        }
    }

    // 從指定槽位載入客棧狀態
    loadHostelState(slotIndex) {
        console.log(`從槽位 ${slotIndex} 載入客棧狀態`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                console.warn(`槽位 ${slotIndex} 沒有保存的客棧狀態`);
                return null;
            }

            const hostelState = JSON.parse(savedData);
            console.log(`客棧狀態已從槽位 ${slotIndex} 載入`);
            return hostelState;
        } catch (error) {
            console.error("載入客棧狀態失敗:", error);
            return null;
        }
    }

    // 獲取存檔槽位信息
    getHostelSlotInfo(slotIndex) {
        if (slotIndex < 0 || slotIndex >= 5) {
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                return null;
            }

            const hostelState = JSON.parse(savedData);
            return {
                timestamp: hostelState.timestamp,
                hostelIndex: hostelState.hostelIndex,
                previousScene: hostelState.previousScene,
                levelTitle: hostelState.levelTitle || "未知關卡",
                currentLevel: hostelState.currentLevel || 0,
                hasPlayerData: !!hostelState.playerData
            };
        } catch (error) {
            console.error("獲取客棧存檔槽位信息失敗:", error);
            return null;
        }
    }

    // 顯示客棧存檔對話框
    showSaveHostelDialog() {
        console.log("開啟客棧存檔介面");

        // 創建 dialog 元素
        const saveDialog = document.createElement("dialog");
        saveDialog.id = "save-hostel-dialog";
        saveDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const saveContent = document.createElement("div");
        saveContent.className = "save-content";
        saveContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "存取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        saveContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            saveDialog.close();
            this.hostelContainer.removeChild(saveDialog);
        });
        saveContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'save');
        }

        saveContent.appendChild(slotsContainer);
        saveDialog.appendChild(saveContent);
        this.hostelContainer.appendChild(saveDialog);
        saveDialog.showModal();
    }

    // 顯示客棧讀檔對話框（已移至 Init.js 的前历再续功能）
    showLoadHostelDialog() {
        console.log("開啟客棧讀檔介面");

        // 創建 dialog 元素
        const loadDialog = document.createElement("dialog");
        loadDialog.id = "load-hostel-dialog";
        loadDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const loadContent = document.createElement("div");
        loadContent.className = "save-content";
        loadContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        loadContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            this.hostelContainer.removeChild(loadDialog);
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'load');
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        this.hostelContainer.appendChild(loadDialog);
        loadDialog.showModal();
    }

    // 創建存檔槽
    createSaveSlot(container, slotIndex, mode) {
        const slot = document.createElement("div");
        slot.className = "save-slot";
        slot.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 110px;
            background-color: rgb(247, 231, 173);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            padding: 5px;
            cursor: pointer;
            box-sizing: border-box;
            opacity: 0;
        `;

        // 左欄：圖騰
        const totem = document.createElement("div");
        totem.className = "save-slot-totem";
        totem.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const totemImage = document.createElement("img");
        totemImage.draggable = false;
        totemImage.src = "./Public/saveicon.png";
        totemImage.style.cssText = `
            width: 360px;
            height: 95px;
        `;
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：客棧信息和存檔時間
        const middle = document.createElement("div");
        middle.className = "save-slot-middle";
        middle.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const hostelInfo = document.createElement("div");
        hostelInfo.className = "save-slot-level";
        hostelInfo.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 25px;
        `;
        const saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";
        saveTime.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 20px;
        `;

        // 獲取存檔信息
        const slotInfo = this.getHostelSlotInfo(slotIndex);
        if (slotInfo) {
            hostelInfo.style.display = "block";
            hostelInfo.textContent = `客棧 ${slotInfo.hostelIndex}`;
            saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            hostelInfo.style.display = "none";
            saveTime.textContent = "";
        }

        middle.appendChild(hostelInfo);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        const levelTitle = document.createElement("div");
        levelTitle.className = "save-slot-title";
        levelTitle.style.cssText = `
            width: 30%;
            text-align: center;
            font-weight: 600;
            color: rgb(168, 105, 38);
            font-size: 28px;
        `;

        // 顯示關卡標題，如果沒有存檔則顯示槽位編號
        if (slotInfo && slotInfo.levelTitle) {
            levelTitle.textContent = slotInfo.levelTitle;
        } else {
            levelTitle.textContent = `第 ${slotIndex + 1} 格`;
        }
        slot.appendChild(levelTitle);

        // 存檔槽點擊事件
        if (mode === 'save') {
            slot.addEventListener("click", () => {
                const confirmSave = confirm(`是否要將當前客棧存檔在第 ${slotIndex + 1} 格的位置？`);
                if (confirmSave) {
                    const success = this.saveHostelState(slotIndex);
                    if (success) {
                        // 更新顯示
                        const newSlotInfo = this.getHostelSlotInfo(slotIndex);
                        if (newSlotInfo) {
                            hostelInfo.style.display = "block";
                            hostelInfo.textContent = `客棧 ${newSlotInfo.hostelIndex}`;
                            saveTime.textContent = new Date(newSlotInfo.timestamp).toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                        alert("客棧存檔成功！");
                    } else {
                        alert("客棧存檔失敗！");
                    }
                }
            });
        } else if (mode === 'load') {
            slot.addEventListener("click", () => {
                if (!slotInfo) {
                    return;
                }

                const confirmLoad = confirm(`是否要讀取第 ${slotIndex + 1} 格的客棧存檔？\n客棧：${slotInfo.hostelIndex}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
                if (confirmLoad) {
                    const hostelState = this.loadHostelState(slotIndex);
                    if (hostelState) {
                        // 關閉對話框
                        const loadDialog = document.getElementById('load-hostel-dialog');
                        if (loadDialog) {
                            loadDialog.close();
                            this.hostelContainer.removeChild(loadDialog);
                        }

                        // 載入客棧場景
                        this.loadHostelScene(hostelState);
                        alert("客棧讀取成功！");
                    } else {
                        alert("客棧讀取失敗！");
                    }
                }
            });
        }

        // 添加動畫
        const delay = slotIndex * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        container.appendChild(slot);
    }

    // 載入客棧場景
    loadHostelScene(hostelState) {
        console.log("載入客棧場景狀態:", hostelState);

        try {
            // 使用場景管理器切換到客棧場景
            if (typeof sceneManager !== 'undefined') {
                sceneManager.switchToHostel(hostelState.hostelIndex, hostelState.previousScene);
                console.log("客棧場景載入成功");
            } else {
                console.error("場景管理器不可用");
                alert("無法載入客棧場景");
            }
        } catch (error) {
            console.error("載入客棧場景失敗:", error);
            alert("載入客棧場景失敗");
        }
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 添加背包管理動畫樣式
    const bagAnimationStyle = document.createElement('style');
    bagAnimationStyle.textContent = `
        @keyframes bagAppear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #bagManagementDialog #publicBagContainer::-webkit-scrollbar,
        #bagManagementDialog #personalBagContainer::-webkit-scrollbar {
            display: none;
        }

        #bagManagementDialog #publicBagContainer,
        #bagManagementDialog #personalBagContainer {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
    `;

    if (!document.head.querySelector('style[data-bag-animation]')) {
        bagAnimationStyle.setAttribute('data-bag-animation', 'true');
        document.head.appendChild(bagAnimationStyle);
    }

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}