/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        // Sidebar 狀態保存
        this.originalSidebarState = null;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單面板
            this.createHostelMenuPanel();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 保存並隱藏原本的 sidebar 狀態
    saveSidebarState() {
        console.log("保存並隱藏原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            // 保存原始狀態
            this.originalSidebarState = {
                display: sidebar.style.display || getComputedStyle(sidebar).display,
                visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility,
                opacity: sidebar.style.opacity || getComputedStyle(sidebar).opacity,
                zIndex: sidebar.style.zIndex || getComputedStyle(sidebar).zIndex
            };

            // 隱藏 sidebar（客棧有自己的選單系統）
            sidebar.style.display = 'none';

            console.log("Sidebar 狀態已保存並隱藏:", this.originalSidebarState);
        } else {
            console.warn("找不到 sidebar 元素，無法保存狀態");
            this.originalSidebarState = null;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 保存並隱藏原本的 sidebar
        this.saveSidebarState();

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎來到客棧！'
            : '🏨 歡迎來到休息區！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: rgb(168, 105, 38);; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;color: rgb(168, 105, 38);">
                ${instructionText}
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理基本按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    // ESC 鍵返回營地或上一個場景
                    if (this.previousScene === 'camp') {
                        this.returnToCamp();
                    } else {
                        console.log("按下 ESC，但無明確的返回目標");
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 創建客棧專用選單面板
    createHostelMenuPanel() {
        console.log("創建客棧專用選單面板");

        // 創建選單面板容器
        this.menuPanel = document.createElement('div');
        this.menuPanel.id = 'hostelMenuPanel';
        this.menuPanel.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(168, 105, 38);
            border-radius: 10px;
            padding: 20px;
            z-index: 5;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        `;

        // 創建選單標題
        const title = document.createElement('h3');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 20px;
            border-bottom: 2px solid rgb(168, 105, 38);
            padding-bottom: 10px;
        `;
        this.menuPanel.appendChild(title);

        // 創建選單選項
        const menuOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '🎒 背包管理',
                action: () => this.showBagManagement()
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({
                text: '� 前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建選項按鈕
        menuOptions.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 12px;
                margin: 8px 0;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(168, 105, 38);
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            // 按鈕懸停效果
            button.onmouseover = () => {
                button.style.transform = 'translateX(5px)';
            };

            button.onmouseout = () => {
                button.style.transform = 'translateX(0)';
            };

            button.onclick = option.action;
            this.menuPanel.appendChild(button);
        });

        this.hostelContainer.appendChild(this.menuPanel);
        console.log("客棧選單面板創建完成");
    }









    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示客棧存檔選單");
        this.showSaveHostelDialog();
    }

    // 顯示背包管理界面
    showBagManagement() {
        console.log("顯示背包管理界面");

        // 創建背包管理對話框
        const bagDialog = document.createElement("dialog");
        bagDialog.id = "bagManagementDialog";
        bagDialog.style.cssText = `
            border: none;
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            padding: 0;
            margin: 0;
            background:transparent;
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
        `;

        // 創建背包管理內容
        const bagContent = document.createElement("div");
        bagContent.style.cssText = `
            width: 90%;
            height: 95%;
            background-image: url('./Public/hostel.gif');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 40px;
            box-sizing: border-box;
        `;

        // 創建中間區域（左右分割）
        const bagMidArea = document.createElement("div");
        bagMidArea.style.cssText = `
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;
            opacity: 0;
            animation: bagAppear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
        `;

        // 左半邊：行囊（公共背包）
        const publicBagContainer = document.createElement("div");
        publicBagContainer.id = "publicBagContainer";
        publicBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: left;
            overflow-y: auto;
            background: url(./Public/mybagwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-right: 5%;
        `;

        // 行囊標題
        const publicBagTitle = document.createElement("div");
        publicBagTitle.textContent = "行囊";
        publicBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            margin-top: 5px;
            width: 30%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            letter-spacing: 5px;
            position: relative;
            float: left;
            left: 70px;
        `;
        publicBagContainer.appendChild(publicBagTitle);

        // 行囊內容區域
        const publicBagArea = document.createElement("div");
        publicBagArea.style.cssText = `
            margin-left: 9%;
            margin-top: 13%;
            width: 88%;
            height: 72%;
            outline: none;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        publicBagContainer.appendChild(publicBagArea);

        // 右半邊：個人背包
        const personalBagContainer = document.createElement("div");
        personalBagContainer.id = "personalBagContainer";
        personalBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: right;
            overflow-y: auto;
            background: url(./Public/shopwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;

        // 個人背包標題
        const personalBagTitle = document.createElement("div");
        personalBagTitle.textContent = "個人背包";
        personalBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            margin-top: 5px;
            width: 30%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            letter-spacing: 5px;
            position: relative;
            float: right;
            right: 70px;
        `;
        personalBagContainer.appendChild(personalBagTitle);

        // 個人背包內容區域
        const personalBagArea = document.createElement("div");
        personalBagArea.style.cssText = `
            margin-left: 2%;
            margin-top: 12%;
            width: 88%;
            height: 72%;
            outline: none;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        personalBagContainer.appendChild(personalBagArea);

        // 初始化背包切換系統
        this.initBagManagementSystem(publicBagArea, personalBagArea);

        // 關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.textContent = "關閉背包";
        closeButton.style.cssText = `
            padding: 10px 20px;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            align-self: center;
            transition: background-color 0.3s ease;
            opacity: 0;
            animation: bagAppear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
        `;

        closeButton.addEventListener('click', () => {
            this.closeBagManagement(bagDialog);
        });

        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = 'rgb(165, 90, 24)';
            closeButton.style.color = 'white';
            closeButton.style.transform = 'scale(1.05)';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = 'rgb(247, 231, 173)';
            closeButton.style.color = 'rgb(168, 105, 38)';
            closeButton.style.transform = 'scale(1)';
        });

        // 組裝對話框
        bagMidArea.appendChild(publicBagContainer);
        bagMidArea.appendChild(personalBagContainer);
        bagContent.appendChild(bagMidArea);
        bagContent.appendChild(closeButton);
        bagDialog.appendChild(bagContent);

        // 添加到頁面並顯示
        document.body.appendChild(bagDialog);
        bagDialog.showModal();

        // 添加 Esc 鍵退出功能
        this.setupBagEscapeHandler(bagDialog);

        console.log("背包管理對話框已創建並顯示");
    }

    // 初始化背包管理系統
    initBagManagementSystem(publicBagArea, personalBagArea) {
        console.log("初始化背包管理系統");

        // 獲取當前關卡的非 AI 玩家
        this.availablePlayers = this.getNonAIPlayers();

        // 設置背包選項（參考 CampScene.js 的實現）
        this.bagOptions = [
            { type: 'public', name: '行囊', data: typeof mybag !== 'undefined' ? mybag : [] },
            ...this.availablePlayers.map(player => ({
                type: 'player',
                name: player.name,
                data: player.Inventory || player.mybag || [], // 優先使用 Inventory
                player: player
            }))
        ];

        this.currentBagIndex = 0; // 默認顯示第一個背包（行囊）

        console.log("可用背包選項:", this.bagOptions.map(bag => bag.name));

        // 渲染行囊（公共背包）
        this.renderPublicBag(publicBagArea);

        // 渲染個人背包
        this.renderPersonalBag(personalBagArea);
    }

    // 獲取非 AI 玩家（參考 CampScene.js 的實現）
    getNonAIPlayers() {
        console.log("獲取當前關卡的非 AI 玩家");

        // 從當前關卡數據獲取玩家
        if (typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined' &&
            controlLayer[currentLevel] && controlLayer[currentLevel].playerData) {

            const levelPlayers = controlLayer[currentLevel].playerData;
            const nonAIPlayers = levelPlayers.filter(player => !player.isAI && player.curHP > 0);
            console.log("從關卡數據獲取到的非 AI 玩家:", nonAIPlayers.map(p => p.name));
            return nonAIPlayers;
        }

        // 備用方案：從全局 playerData 獲取
        if (typeof playerData !== 'undefined' && Array.isArray(playerData)) {
            const nonAIPlayers = playerData.filter(player => !player.isAI && player.curHP > 0);
            console.log("從全局 playerData 獲取到的非 AI 玩家:", nonAIPlayers.map(p => p.name));
            return nonAIPlayers;
        }

        console.warn("無法獲取當前關卡的玩家資料");
        return [];
    }

    // 渲染行囊（公共背包）
    renderPublicBag(container) {
        console.log("渲染行囊");
        container.innerHTML = '';

        const publicBag = typeof mybag !== 'undefined' ? mybag : [];

        if (publicBag.length === 0) {
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = "行囊是空的";
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 50px;
            `;
            container.appendChild(emptyMessage);
            return;
        }

        // 統計物品數量
        const itemMap = new Map();
        publicBag.forEach(item => {
            const key = item.name || item;
            itemMap.set(key, (itemMap.get(key) || 0) + 1);
        });

        // 渲染物品
        itemMap.forEach((count, itemName) => {
            const itemDiv = document.createElement("div");
            itemDiv.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                margin-bottom: 10px;
                cursor: pointer;
                font-size: 20px;
                color: rgb(168, 105, 38);
                box-sizing: border-box;
                border: 3px solid transparent;
            `;

            itemDiv.innerHTML = `
                <span>${itemName}</span>
                <span>x${count}</span>
            `;

            // 懸停效果
            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.backgroundColor = 'rgba(168, 105, 38, 0.2)';
                itemDiv.style.borderColor = 'rgb(168, 105, 38)';
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.backgroundColor = 'transparent';
                itemDiv.style.borderColor = 'transparent';
            });

            // 點擊事件：移動到個人背包
            itemDiv.addEventListener('click', () => {
                this.moveItemToPersonalBag(itemName);
            });

            container.appendChild(itemDiv);
        });
    }

    // 渲染個人背包（參考 CampScene.js 的實現）
    renderPersonalBag(container) {
        console.log("渲染個人背包");
        container.innerHTML = '';

        if (this.bagOptions.length <= 1) { // 只有行囊，沒有個人背包
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = "沒有可用的角色";
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 50px;
            `;
            container.appendChild(emptyMessage);
            return;
        }

        // 創建背包標題容器（參考 CampScene.js）
        const bagTitleContainer = document.createElement("div");
        bagTitleContainer.style.cssText = `
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
        `;

        // 上一個背包按鈕
        const prevBtn = document.createElement("button");
        prevBtn.textContent = "◀";
        prevBtn.style.cssText = `
            padding: 8px 12px;
            background: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 3px solid rgb(165, 90, 24);
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        `;

        // 背包標題
        const bagTitle = document.createElement("span");
        bagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 20px;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        `;

        // 下一個背包按鈕
        const nextBtn = document.createElement("button");
        nextBtn.textContent = "▶";
        nextBtn.style.cssText = `
            padding: 8px 12px;
            background: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 3px solid rgb(165, 90, 24);
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        `;

        bagTitleContainer.appendChild(prevBtn);
        bagTitleContainer.appendChild(bagTitle);
        bagTitleContainer.appendChild(nextBtn);
        container.appendChild(bagTitleContainer);

        // 初始化背包切換系統（參考 CampScene.js）
        this.initPersonalBagSwitchSystem(prevBtn, nextBtn, bagTitle);

        // 創建背包內容區域
        const bagContentArea = document.createElement("div");
        bagContentArea.id = "personalBagContent";
        bagContentArea.style.cssText = `
            margin-left: 2%;
            margin-top: 5%;
            width: 96%;
            height: 65%;
            outline: none;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        container.appendChild(bagContentArea);

        // 初始化顯示
        this.updatePersonalBagDisplay();
    }

    // 初始化個人背包切換系統（參考 CampScene.js）
    initPersonalBagSwitchSystem(prevBtn, nextBtn, titleElement) {
        console.log("初始化個人背包切換系統");

        // 設置當前背包索引（跳過行囊，從第一個個人背包開始）
        this.currentPersonalBagIndex = 1; // 0 是行囊，1 開始是個人背包

        console.log("可用背包選項:", this.bagOptions.map(bag => bag.name));

        // 設置按鈕事件
        prevBtn.addEventListener('click', () => {
            this.switchToPreviousPersonalBag(titleElement);
        });

        nextBtn.addEventListener('click', () => {
            this.switchToNextPersonalBag(titleElement);
        });

        // 添加按鈕懸停效果（參考 CampScene.js）
        this.addPersonalBagButtonHoverEffects(prevBtn, nextBtn);

        // 初始化顯示
        this.updatePersonalBagTitleDisplay(titleElement);
    }

    // 切換到上一個個人背包
    switchToPreviousPersonalBag(titleElement) {
        const personalBagCount = this.bagOptions.length - 1; // 排除行囊
        if (personalBagCount <= 1) return;

        this.currentPersonalBagIndex = this.currentPersonalBagIndex - 1;
        if (this.currentPersonalBagIndex < 1) {
            this.currentPersonalBagIndex = this.bagOptions.length - 1; // 回到最後一個個人背包
        }

        console.log(`切換到上一個個人背包: ${this.bagOptions[this.currentPersonalBagIndex].name}`);
        this.updatePersonalBagTitleDisplay(titleElement);
        this.updatePersonalBagDisplay();
    }

    // 切換到下一個個人背包
    switchToNextPersonalBag(titleElement) {
        const personalBagCount = this.bagOptions.length - 1; // 排除行囊
        if (personalBagCount <= 1) return;

        this.currentPersonalBagIndex = this.currentPersonalBagIndex + 1;
        if (this.currentPersonalBagIndex >= this.bagOptions.length) {
            this.currentPersonalBagIndex = 1; // 回到第一個個人背包
        }

        console.log(`切換到下一個個人背包: ${this.bagOptions[this.currentPersonalBagIndex].name}`);
        this.updatePersonalBagTitleDisplay(titleElement);
        this.updatePersonalBagDisplay();
    }

    // 更新個人背包標題顯示
    updatePersonalBagTitleDisplay(titleElement) {
        if (this.currentPersonalBagIndex < 1 || this.currentPersonalBagIndex >= this.bagOptions.length) {
            this.currentPersonalBagIndex = 1; // 確保索引有效
        }

        const currentBag = this.bagOptions[this.currentPersonalBagIndex];
        if (currentBag && currentBag.type === 'player') {
            // 顯示個人背包容量信息（參考 CampScene.js）
            const currentCount = currentBag.data.length;
            const maxCount = 6;
            titleElement.textContent = `${currentBag.name} (${currentCount}/${maxCount})`;
        } else {
            titleElement.textContent = "個人背包";
        }
    }

    // 添加個人背包按鈕懸停效果
    addPersonalBagButtonHoverEffects(prevBtn, nextBtn) {
        // 上一個按鈕懸停效果
        prevBtn.addEventListener('mouseenter', () => {
            prevBtn.style.backgroundColor = 'rgb(165, 90, 24)';
            prevBtn.style.color = 'white';
            prevBtn.style.transform = 'scale(1.1)';
        });

        prevBtn.addEventListener('mouseleave', () => {
            prevBtn.style.backgroundColor = 'rgb(247, 231, 173)';
            prevBtn.style.color = 'rgb(168, 105, 38)';
            prevBtn.style.transform = 'scale(1)';
        });

        // 下一個按鈕懸停效果
        nextBtn.addEventListener('mouseenter', () => {
            nextBtn.style.backgroundColor = 'rgb(165, 90, 24)';
            nextBtn.style.color = 'white';
            nextBtn.style.transform = 'scale(1.1)';
        });

        nextBtn.addEventListener('mouseleave', () => {
            nextBtn.style.backgroundColor = 'rgb(247, 231, 173)';
            nextBtn.style.color = 'rgb(168, 105, 38)';
            nextBtn.style.transform = 'scale(1)';
        });
    }

    // 更新個人背包顯示
    updatePersonalBagDisplay() {
        const bagContentArea = document.querySelector('#personalBagContent');
        if (!bagContentArea) return;

        // 清空內容區域
        bagContentArea.innerHTML = '';

        // 確保索引有效
        if (this.currentPersonalBagIndex < 1 || this.currentPersonalBagIndex >= this.bagOptions.length) {
            this.currentPersonalBagIndex = 1;
        }

        const currentBag = this.bagOptions[this.currentPersonalBagIndex];
        if (!currentBag || currentBag.type !== 'player') {
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = "無效的個人背包";
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 30px;
            `;
            bagContentArea.appendChild(emptyMessage);
            return;
        }

        const playerBag = currentBag.data || [];

        if (playerBag.length === 0) {
            const emptyMessage = document.createElement("div");
            emptyMessage.textContent = `${currentBag.name} 的背包是空的`;
            emptyMessage.style.cssText = `
                text-align: center;
                color: rgb(168, 105, 38);
                font-size: 18px;
                margin-top: 30px;
            `;
            bagContentArea.appendChild(emptyMessage);
            return;
        }

        // 統計物品數量
        const itemMap = new Map();
        playerBag.forEach(item => {
            const key = item.name || item;
            itemMap.set(key, (itemMap.get(key) || 0) + 1);
        });

        // 渲染物品
        itemMap.forEach((count, itemName) => {
            const itemDiv = document.createElement("div");
            itemDiv.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                margin-bottom: 10px;
                cursor: pointer;
                font-size: 20px;
                color: rgb(168, 105, 38);
                box-sizing: border-box;
                border: 3px solid transparent;
                transition: all 0.3s ease;
            `;

            itemDiv.innerHTML = `
                <span>${itemName}</span>
                <span>x${count}</span>
            `;

            // 懸停效果
            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.backgroundColor = 'rgba(168, 105, 38, 0.2)';
                itemDiv.style.borderColor = 'rgb(168, 105, 38)';
                itemDiv.style.transform = 'translateX(5px)';
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.backgroundColor = 'transparent';
                itemDiv.style.borderColor = 'transparent';
                itemDiv.style.transform = 'translateX(0)';
            });

            // 點擊事件：移動到行囊
            itemDiv.addEventListener('click', () => {
                this.moveItemToPublicBag(itemName, this.currentPersonalBagIndex);
            });

            bagContentArea.appendChild(itemDiv);
        });
    }

    // 移動物品到個人背包
    moveItemToPersonalBag(itemName) {
        if (this.bagOptions.length <= 1) {
            alert("沒有可用的個人背包");
            return;
        }

        // 確保有效的個人背包索引
        if (this.currentPersonalBagIndex < 1 || this.currentPersonalBagIndex >= this.bagOptions.length) {
            this.currentPersonalBagIndex = 1;
        }

        const currentBag = this.bagOptions[this.currentPersonalBagIndex];
        if (!currentBag || currentBag.type !== 'player') {
            alert("無效的個人背包");
            return;
        }

        // 檢查個人背包是否已滿（最多6個）
        if (currentBag.data.length >= 6) {
            alert(`${currentBag.name} 的背包已滿！`);
            return;
        }

        // 從行囊移除物品
        if (typeof mybag !== 'undefined') {
            const itemIndex = mybag.findIndex(item => (item.name || item) === itemName);
            if (itemIndex !== -1) {
                const removedItem = mybag.splice(itemIndex, 1)[0];

                // 添加到個人背包
                currentBag.data.push(removedItem);

                // 重新渲染
                this.renderPublicBag(document.querySelector('#publicBagContainer div:last-child'));
                this.updatePersonalBagDisplay();

                // 更新標題顯示（顯示新的容量）
                const titleElement = document.querySelector('#personalBagContainer span');
                if (titleElement) {
                    this.updatePersonalBagTitleDisplay(titleElement);
                }

                console.log(`物品 ${itemName} 已移動到 ${currentBag.name} 的背包`);
            }
        }
    }

    // 移動物品到行囊
    moveItemToPublicBag(itemName, bagIndex) {
        if (bagIndex < 1 || bagIndex >= this.bagOptions.length) return;

        const currentBag = this.bagOptions[bagIndex];
        if (!currentBag || currentBag.type !== 'player') return;

        // 從個人背包移除物品
        const itemIndex = currentBag.data.findIndex(item => (item.name || item) === itemName);
        if (itemIndex !== -1) {
            const removedItem = currentBag.data.splice(itemIndex, 1)[0];

            // 添加到行囊
            if (typeof mybag !== 'undefined') {
                mybag.push(removedItem);
            }

            // 重新渲染
            this.renderPublicBag(document.querySelector('#publicBagContainer div:last-child'));
            this.updatePersonalBagDisplay();

            // 更新標題顯示（顯示新的容量）
            const titleElement = document.querySelector('#personalBagContainer span');
            if (titleElement) {
                this.updatePersonalBagTitleDisplay(titleElement);
            }

            console.log(`物品 ${itemName} 已從 ${currentBag.name} 移動到行囊`);
        }
    }

    // 設置背包 Esc 鍵處理
    setupBagEscapeHandler(bagDialog) {
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeBagManagement(bagDialog);
                document.removeEventListener('keydown', escapeHandler);
                console.log("按下 Esc 鍵，退出背包管理");
            }
        };

        document.addEventListener('keydown', escapeHandler);
        bagDialog.escapeHandler = escapeHandler;
    }

    // 關閉背包管理
    closeBagManagement(bagDialog) {
        console.log("關閉背包管理");

        // 清理 Esc 鍵事件監聽器
        if (bagDialog.escapeHandler) {
            document.removeEventListener('keydown', bagDialog.escapeHandler);
            bagDialog.escapeHandler = null;
        }

        // 關閉對話框
        bagDialog.close();
        document.body.removeChild(bagDialog);

        console.log("背包管理已關閉");
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
            
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 恢復原本的 sidebar 狀態
    restoreSidebar() {
        console.log("恢復原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar && this.originalSidebarState) {
            // 恢復保存的原始狀態
            sidebar.style.display = this.originalSidebarState.display;
            sidebar.style.visibility = this.originalSidebarState.visibility;
            sidebar.style.opacity = this.originalSidebarState.opacity;
            sidebar.style.zIndex = 9999;
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0px';
            sidebar.style.top = '0px';

            console.log("Sidebar 狀態已恢復到原始狀態:", this.originalSidebarState);
        } else if (sidebar) {
            // 如果沒有保存的狀態，使用默認值
            sidebar.style.display = 'flex';
            sidebar.style.flexDirection = 'column';
            sidebar.style.justifyContent = 'start';
            sidebar.style.alignItems = 'center';
            sidebar.style.justifySelf = 'flex-start';
            sidebar.style.gap = '10px';
            sidebar.style.overflow = 'hidden';
            sidebar.style.visibility = 'visible';
            sidebar.style.opacity = '1';
            sidebar.style.zIndex = '1000';

            console.log("Sidebar 狀態已恢復到默認狀態");
        } else {
            console.warn("找不到 sidebar 元素");
        }

        // 清除保存的狀態
        this.originalSidebarState = null;

        // 如果有營地場景的 sidebar 更新函數，調用它
        if (typeof updateCampSidebar === 'function') {
            updateCampSidebar();
        }

        // 觸發 sidebar 恢復事件，讓其他模組知道
        const sidebarRestoreEvent = new CustomEvent('sidebarRestored', {
            detail: { source: 'hostel' }
        });
        document.dispatchEvent(sidebarRestoreEvent);
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 移除選單面板
        if (this.menuPanel && this.menuPanel.parentNode) {
            this.menuPanel.parentNode.removeChild(this.menuPanel);
            this.menuPanel = null;
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // 恢復原本的 sidebar 狀態
        this.restoreSidebar();

        console.log("客棧場景清理完成");
    }

    // ===== 客棧存檔系統 =====

    // 獲取當前客棧狀態
    getCurrentHostelState() {
        console.log("收集當前客棧狀態");

        // 獲取當前關卡標題
        let levelTitle = "未知關卡";
        if (typeof currentLevel !== 'undefined' && typeof controlLayer !== 'undefined') {
            const levelData = controlLayer[currentLevel];
            if (levelData && levelData["標題"]) {
                levelTitle = levelData["標題"];
            }
        }

        const hostelState = {
            // 基本信息
            timestamp: Date.now(),
            hostelIndex: this.hostelIndex,
            previousScene: this.previousScene,

            // 關卡信息
            levelTitle: levelTitle,
            currentLevel: typeof currentLevel !== 'undefined' ? currentLevel : 0,

            // 玩家數據
            playerData: this.playerData,

            // 客棧特定狀態
            hostelSpecific: {
                isLoaded: this.isLoaded,
                isRunning: this.isRunning
            },

            // 背包和金錢（如果可用）
            inventory: {
                mybag: typeof mybag !== 'undefined' ? [...mybag] : [],
                mymoney: typeof mymoney !== 'undefined' ? mymoney : 0
            }
        };

        console.log("客棧狀態收集完成:", hostelState);
        return hostelState;
    }

    // 保存客棧狀態到指定槽位
    saveHostelState(slotIndex) {
        console.log(`保存客棧狀態到槽位 ${slotIndex}`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return false;
        }

        try {
            const hostelState = this.getCurrentHostelState();
            const storageKey = `hostelSave_${slotIndex}`;

            localStorage.setItem(storageKey, JSON.stringify(hostelState));
            console.log(`客棧狀態已保存到槽位 ${slotIndex}`);
            return true;
        } catch (error) {
            console.error("保存客棧狀態失敗:", error);
            return false;
        }
    }

    // 從指定槽位載入客棧狀態
    loadHostelState(slotIndex) {
        console.log(`從槽位 ${slotIndex} 載入客棧狀態`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                console.warn(`槽位 ${slotIndex} 沒有保存的客棧狀態`);
                return null;
            }

            const hostelState = JSON.parse(savedData);
            console.log(`客棧狀態已從槽位 ${slotIndex} 載入`);
            return hostelState;
        } catch (error) {
            console.error("載入客棧狀態失敗:", error);
            return null;
        }
    }

    // 獲取存檔槽位信息
    getHostelSlotInfo(slotIndex) {
        if (slotIndex < 0 || slotIndex >= 5) {
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                return null;
            }

            const hostelState = JSON.parse(savedData);
            return {
                timestamp: hostelState.timestamp,
                hostelIndex: hostelState.hostelIndex,
                previousScene: hostelState.previousScene,
                levelTitle: hostelState.levelTitle || "未知關卡",
                currentLevel: hostelState.currentLevel || 0,
                hasPlayerData: !!hostelState.playerData
            };
        } catch (error) {
            console.error("獲取客棧存檔槽位信息失敗:", error);
            return null;
        }
    }

    // 顯示客棧存檔對話框
    showSaveHostelDialog() {
        console.log("開啟客棧存檔介面");

        // 創建 dialog 元素
        const saveDialog = document.createElement("dialog");
        saveDialog.id = "save-hostel-dialog";
        saveDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const saveContent = document.createElement("div");
        saveContent.className = "save-content";
        saveContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "存取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        saveContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            saveDialog.close();
            this.hostelContainer.removeChild(saveDialog);
        });
        saveContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'save');
        }

        saveContent.appendChild(slotsContainer);
        saveDialog.appendChild(saveContent);
        this.hostelContainer.appendChild(saveDialog);
        saveDialog.showModal();
    }

    // 顯示客棧讀檔對話框（已移至 Init.js 的前历再续功能）
    showLoadHostelDialog() {
        console.log("開啟客棧讀檔介面");

        // 創建 dialog 元素
        const loadDialog = document.createElement("dialog");
        loadDialog.id = "load-hostel-dialog";
        loadDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const loadContent = document.createElement("div");
        loadContent.className = "save-content";
        loadContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        loadContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            this.hostelContainer.removeChild(loadDialog);
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'load');
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        this.hostelContainer.appendChild(loadDialog);
        loadDialog.showModal();
    }

    // 創建存檔槽
    createSaveSlot(container, slotIndex, mode) {
        const slot = document.createElement("div");
        slot.className = "save-slot";
        slot.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 110px;
            background-color: rgb(247, 231, 173);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            padding: 5px;
            cursor: pointer;
            box-sizing: border-box;
            opacity: 0;
        `;

        // 左欄：圖騰
        const totem = document.createElement("div");
        totem.className = "save-slot-totem";
        totem.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const totemImage = document.createElement("img");
        totemImage.draggable = false;
        totemImage.src = "./Public/saveicon.png";
        totemImage.style.cssText = `
            width: 360px;
            height: 95px;
        `;
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：客棧信息和存檔時間
        const middle = document.createElement("div");
        middle.className = "save-slot-middle";
        middle.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const hostelInfo = document.createElement("div");
        hostelInfo.className = "save-slot-level";
        hostelInfo.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 25px;
        `;
        const saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";
        saveTime.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 20px;
        `;

        // 獲取存檔信息
        const slotInfo = this.getHostelSlotInfo(slotIndex);
        if (slotInfo) {
            hostelInfo.style.display = "block";
            hostelInfo.textContent = `客棧 ${slotInfo.hostelIndex}`;
            saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            hostelInfo.style.display = "none";
            saveTime.textContent = "";
        }

        middle.appendChild(hostelInfo);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        const levelTitle = document.createElement("div");
        levelTitle.className = "save-slot-title";
        levelTitle.style.cssText = `
            width: 30%;
            text-align: center;
            font-weight: 600;
            color: rgb(168, 105, 38);
            font-size: 28px;
        `;

        // 顯示關卡標題，如果沒有存檔則顯示槽位編號
        if (slotInfo && slotInfo.levelTitle) {
            levelTitle.textContent = slotInfo.levelTitle;
        } else {
            levelTitle.textContent = `第 ${slotIndex + 1} 格`;
        }
        slot.appendChild(levelTitle);

        // 存檔槽點擊事件
        if (mode === 'save') {
            slot.addEventListener("click", () => {
                const confirmSave = confirm(`是否要將當前客棧存檔在第 ${slotIndex + 1} 格的位置？`);
                if (confirmSave) {
                    const success = this.saveHostelState(slotIndex);
                    if (success) {
                        // 更新顯示
                        const newSlotInfo = this.getHostelSlotInfo(slotIndex);
                        if (newSlotInfo) {
                            hostelInfo.style.display = "block";
                            hostelInfo.textContent = `客棧 ${newSlotInfo.hostelIndex}`;
                            saveTime.textContent = new Date(newSlotInfo.timestamp).toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                        alert("客棧存檔成功！");
                    } else {
                        alert("客棧存檔失敗！");
                    }
                }
            });
        } else if (mode === 'load') {
            slot.addEventListener("click", () => {
                if (!slotInfo) {
                    return;
                }

                const confirmLoad = confirm(`是否要讀取第 ${slotIndex + 1} 格的客棧存檔？\n客棧：${slotInfo.hostelIndex}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
                if (confirmLoad) {
                    const hostelState = this.loadHostelState(slotIndex);
                    if (hostelState) {
                        // 關閉對話框
                        const loadDialog = document.getElementById('load-hostel-dialog');
                        if (loadDialog) {
                            loadDialog.close();
                            this.hostelContainer.removeChild(loadDialog);
                        }

                        // 載入客棧場景
                        this.loadHostelScene(hostelState);
                        alert("客棧讀取成功！");
                    } else {
                        alert("客棧讀取失敗！");
                    }
                }
            });
        }

        // 添加動畫
        const delay = slotIndex * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        container.appendChild(slot);
    }

    // 載入客棧場景
    loadHostelScene(hostelState) {
        console.log("載入客棧場景狀態:", hostelState);

        try {
            // 使用場景管理器切換到客棧場景
            if (typeof sceneManager !== 'undefined') {
                sceneManager.switchToHostel(hostelState.hostelIndex, hostelState.previousScene);
                console.log("客棧場景載入成功");
            } else {
                console.error("場景管理器不可用");
                alert("無法載入客棧場景");
            }
        } catch (error) {
            console.error("載入客棧場景失敗:", error);
            alert("載入客棧場景失敗");
        }
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 添加背包管理動畫樣式
    const bagAnimationStyle = document.createElement('style');
    bagAnimationStyle.textContent = `
        @keyframes bagAppear {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #bagManagementDialog #publicBagContainer::-webkit-scrollbar,
        #bagManagementDialog #personalBagContainer::-webkit-scrollbar {
            display: none;
        }

        #bagManagementDialog #publicBagContainer,
        #bagManagementDialog #personalBagContainer {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
    `;

    if (!document.head.querySelector('style[data-bag-animation]')) {
        bagAnimationStyle.setAttribute('data-bag-animation', 'true');
        document.head.appendChild(bagAnimationStyle);
    }

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}