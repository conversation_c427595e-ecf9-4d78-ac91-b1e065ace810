/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        // Sidebar 狀態保存
        this.originalSidebarState = null;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單面板
            this.createHostelMenuPanel();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 保存並隱藏原本的 sidebar 狀態
    saveSidebarState() {
        console.log("保存並隱藏原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            // 保存原始狀態
            this.originalSidebarState = {
                display: sidebar.style.display || getComputedStyle(sidebar).display,
                visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility,
                opacity: sidebar.style.opacity || getComputedStyle(sidebar).opacity,
                zIndex: sidebar.style.zIndex || getComputedStyle(sidebar).zIndex
            };

            // 隱藏 sidebar（客棧有自己的選單系統）
            sidebar.style.display = 'none';

            console.log("Sidebar 狀態已保存並隱藏:", this.originalSidebarState);
        } else {
            console.warn("找不到 sidebar 元素，無法保存狀態");
            this.originalSidebarState = null;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 保存並隱藏原本的 sidebar
        this.saveSidebarState();

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎來到客棧！'
            : '🏨 歡迎來到休息區！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: rgb(168, 105, 38);; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;color: rgb(168, 105, 38);">
                ${instructionText}
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理基本按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    // ESC 鍵返回營地或上一個場景
                    if (this.previousScene === 'camp') {
                        this.returnToCamp();
                    } else {
                        console.log("按下 ESC，但無明確的返回目標");
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 創建客棧專用選單面板
    createHostelMenuPanel() {
        console.log("創建客棧專用選單面板");

        // 創建選單面板容器
        this.menuPanel = document.createElement('div');
        this.menuPanel.id = 'hostelMenuPanel';
        this.menuPanel.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(168, 105, 38);
            border-radius: 10px;
            padding: 20px;
            z-index: 5;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        `;

        // 創建選單標題
        const title = document.createElement('h3');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 20px;
            border-bottom: 2px solid rgb(168, 105, 38);
            padding-bottom: 10px;
        `;
        this.menuPanel.appendChild(title);

        // 創建選單選項
        const menuOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({
                text: '� 前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建選項按鈕
        menuOptions.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 12px;
                margin: 8px 0;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(168, 105, 38);
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            // 按鈕懸停效果
            button.onmouseover = () => {
                button.style.transform = 'translateX(5px)';
            };

            button.onmouseout = () => {
                button.style.transform = 'translateX(0)';
            };

            button.onclick = option.action;
            this.menuPanel.appendChild(button);
        });

        this.hostelContainer.appendChild(this.menuPanel);
        console.log("客棧選單面板創建完成");
    }









    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示客棧存檔選單");
        this.showSaveHostelDialog();
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
            
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 恢復原本的 sidebar 狀態
    restoreSidebar() {
        console.log("恢復原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar && this.originalSidebarState) {
            // 恢復保存的原始狀態
            sidebar.style.display = this.originalSidebarState.display;
            sidebar.style.visibility = this.originalSidebarState.visibility;
            sidebar.style.opacity = this.originalSidebarState.opacity;
            sidebar.style.zIndex = 9999;
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0px';
            sidebar.style.top = '0px';

            console.log("Sidebar 狀態已恢復到原始狀態:", this.originalSidebarState);
        } else if (sidebar) {
            // 如果沒有保存的狀態，使用默認值
            sidebar.style.display = 'flex';
            sidebar.style.flexDirection = 'column';
            sidebar.style.justifyContent = 'start';
            sidebar.style.alignItems = 'center';
            sidebar.style.justifySelf = 'flex-start';
            sidebar.style.gap = '10px';
            sidebar.style.overflow = 'hidden';
            sidebar.style.visibility = 'visible';
            sidebar.style.opacity = '1';
            sidebar.style.zIndex = '1000';

            console.log("Sidebar 狀態已恢復到默認狀態");
        } else {
            console.warn("找不到 sidebar 元素");
        }

        // 清除保存的狀態
        this.originalSidebarState = null;

        // 如果有營地場景的 sidebar 更新函數，調用它
        if (typeof updateCampSidebar === 'function') {
            updateCampSidebar();
        }

        // 觸發 sidebar 恢復事件，讓其他模組知道
        const sidebarRestoreEvent = new CustomEvent('sidebarRestored', {
            detail: { source: 'hostel' }
        });
        document.dispatchEvent(sidebarRestoreEvent);
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 移除選單面板
        if (this.menuPanel && this.menuPanel.parentNode) {
            this.menuPanel.parentNode.removeChild(this.menuPanel);
            this.menuPanel = null;
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // 恢復原本的 sidebar 狀態
        this.restoreSidebar();

        console.log("客棧場景清理完成");
    }

    // ===== 客棧存檔系統 =====

    // 獲取當前客棧狀態
    getCurrentHostelState() {
        console.log("收集當前客棧狀態");

        // 獲取當前關卡標題
        let levelTitle = "未知關卡";
        if (typeof currentLevel !== 'undefined' && typeof controlLayer !== 'undefined') {
            const levelData = controlLayer[currentLevel];
            if (levelData && levelData["標題"]) {
                levelTitle = levelData["標題"];
            }
        }

        const hostelState = {
            // 基本信息
            timestamp: Date.now(),
            hostelIndex: this.hostelIndex,
            previousScene: this.previousScene,

            // 關卡信息
            levelTitle: levelTitle,
            currentLevel: typeof currentLevel !== 'undefined' ? currentLevel : 0,

            // 玩家數據
            playerData: this.playerData,

            // 客棧特定狀態
            hostelSpecific: {
                isLoaded: this.isLoaded,
                isRunning: this.isRunning
            },

            // 背包和金錢（如果可用）
            inventory: {
                mybag: typeof mybag !== 'undefined' ? [...mybag] : [],
                mymoney: typeof mymoney !== 'undefined' ? mymoney : 0
            }
        };

        console.log("客棧狀態收集完成:", hostelState);
        return hostelState;
    }

    // 保存客棧狀態到指定槽位
    saveHostelState(slotIndex) {
        console.log(`保存客棧狀態到槽位 ${slotIndex}`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return false;
        }

        try {
            const hostelState = this.getCurrentHostelState();
            const storageKey = `hostelSave_${slotIndex}`;

            localStorage.setItem(storageKey, JSON.stringify(hostelState));
            console.log(`客棧狀態已保存到槽位 ${slotIndex}`);
            return true;
        } catch (error) {
            console.error("保存客棧狀態失敗:", error);
            return false;
        }
    }

    // 從指定槽位載入客棧狀態
    loadHostelState(slotIndex) {
        console.log(`從槽位 ${slotIndex} 載入客棧狀態`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                console.warn(`槽位 ${slotIndex} 沒有保存的客棧狀態`);
                return null;
            }

            const hostelState = JSON.parse(savedData);
            console.log(`客棧狀態已從槽位 ${slotIndex} 載入`);
            return hostelState;
        } catch (error) {
            console.error("載入客棧狀態失敗:", error);
            return null;
        }
    }

    // 獲取存檔槽位信息
    getHostelSlotInfo(slotIndex) {
        if (slotIndex < 0 || slotIndex >= 5) {
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                return null;
            }

            const hostelState = JSON.parse(savedData);
            return {
                timestamp: hostelState.timestamp,
                hostelIndex: hostelState.hostelIndex,
                previousScene: hostelState.previousScene,
                levelTitle: hostelState.levelTitle || "未知關卡",
                currentLevel: hostelState.currentLevel || 0,
                hasPlayerData: !!hostelState.playerData
            };
        } catch (error) {
            console.error("獲取客棧存檔槽位信息失敗:", error);
            return null;
        }
    }

    // 顯示客棧存檔對話框
    showSaveHostelDialog() {
        console.log("開啟客棧存檔介面");

        // 創建 dialog 元素
        const saveDialog = document.createElement("dialog");
        saveDialog.id = "save-hostel-dialog";
        saveDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const saveContent = document.createElement("div");
        saveContent.className = "save-content";
        saveContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "存取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        saveContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            saveDialog.close();
            this.hostelContainer.removeChild(saveDialog);
        });
        saveContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'save');
        }

        saveContent.appendChild(slotsContainer);
        saveDialog.appendChild(saveContent);
        this.hostelContainer.appendChild(saveDialog);
        saveDialog.showModal();
    }

    // 顯示客棧讀檔對話框（已移至 Init.js 的前历再续功能）
    showLoadHostelDialog() {
        console.log("開啟客棧讀檔介面");

        // 創建 dialog 元素
        const loadDialog = document.createElement("dialog");
        loadDialog.id = "load-hostel-dialog";
        loadDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const loadContent = document.createElement("div");
        loadContent.className = "save-content";
        loadContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        loadContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            this.hostelContainer.removeChild(loadDialog);
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'load');
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        this.hostelContainer.appendChild(loadDialog);
        loadDialog.showModal();
    }

    // 創建存檔槽
    createSaveSlot(container, slotIndex, mode) {
        const slot = document.createElement("div");
        slot.className = "save-slot";
        slot.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 110px;
            background-color: rgb(247, 231, 173);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            padding: 5px;
            cursor: pointer;
            box-sizing: border-box;
            opacity: 0;
        `;

        // 左欄：圖騰
        const totem = document.createElement("div");
        totem.className = "save-slot-totem";
        totem.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const totemImage = document.createElement("img");
        totemImage.draggable = false;
        totemImage.src = "./Public/saveicon.png";
        totemImage.style.cssText = `
            width: 360px;
            height: 95px;
        `;
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：客棧信息和存檔時間
        const middle = document.createElement("div");
        middle.className = "save-slot-middle";
        middle.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const hostelInfo = document.createElement("div");
        hostelInfo.className = "save-slot-level";
        hostelInfo.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 25px;
        `;
        const saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";
        saveTime.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 20px;
        `;

        // 獲取存檔信息
        const slotInfo = this.getHostelSlotInfo(slotIndex);
        if (slotInfo) {
            hostelInfo.style.display = "block";
            hostelInfo.textContent = `客棧 ${slotInfo.hostelIndex}`;
            saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            hostelInfo.style.display = "none";
            saveTime.textContent = "";
        }

        middle.appendChild(hostelInfo);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        const levelTitle = document.createElement("div");
        levelTitle.className = "save-slot-title";
        levelTitle.style.cssText = `
            width: 30%;
            text-align: center;
            font-weight: 600;
            color: rgb(168, 105, 38);
            font-size: 28px;
        `;

        // 顯示關卡標題，如果沒有存檔則顯示槽位編號
        if (slotInfo && slotInfo.levelTitle) {
            levelTitle.textContent = slotInfo.levelTitle;
        } else {
            levelTitle.textContent = `第 ${slotIndex + 1} 格`;
        }
        slot.appendChild(levelTitle);

        // 存檔槽點擊事件
        if (mode === 'save') {
            slot.addEventListener("click", () => {
                const confirmSave = confirm(`是否要將當前客棧存檔在第 ${slotIndex + 1} 格的位置？`);
                if (confirmSave) {
                    const success = this.saveHostelState(slotIndex);
                    if (success) {
                        // 更新顯示
                        const newSlotInfo = this.getHostelSlotInfo(slotIndex);
                        if (newSlotInfo) {
                            hostelInfo.style.display = "block";
                            hostelInfo.textContent = `客棧 ${newSlotInfo.hostelIndex}`;
                            saveTime.textContent = new Date(newSlotInfo.timestamp).toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                        alert("客棧存檔成功！");
                    } else {
                        alert("客棧存檔失敗！");
                    }
                }
            });
        } else if (mode === 'load') {
            slot.addEventListener("click", () => {
                if (!slotInfo) {
                    return;
                }

                const confirmLoad = confirm(`是否要讀取第 ${slotIndex + 1} 格的客棧存檔？\n客棧：${slotInfo.hostelIndex}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
                if (confirmLoad) {
                    const hostelState = this.loadHostelState(slotIndex);
                    if (hostelState) {
                        // 關閉對話框
                        const loadDialog = document.getElementById('load-hostel-dialog');
                        if (loadDialog) {
                            loadDialog.close();
                            this.hostelContainer.removeChild(loadDialog);
                        }

                        // 載入客棧場景
                        this.loadHostelScene(hostelState);
                        alert("客棧讀取成功！");
                    } else {
                        alert("客棧讀取失敗！");
                    }
                }
            });
        }

        // 添加動畫
        const delay = slotIndex * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        container.appendChild(slot);
    }

    // 載入客棧場景
    loadHostelScene(hostelState) {
        console.log("載入客棧場景狀態:", hostelState);

        try {
            // 使用場景管理器切換到客棧場景
            if (typeof sceneManager !== 'undefined') {
                sceneManager.switchToHostel(hostelState.hostelIndex, hostelState.previousScene);
                console.log("客棧場景載入成功");
            } else {
                console.error("場景管理器不可用");
                alert("無法載入客棧場景");
            }
        } catch (error) {
            console.error("載入客棧場景失敗:", error);
            alert("載入客棧場景失敗");
        }
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}