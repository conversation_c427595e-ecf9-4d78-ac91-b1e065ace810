/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        // Sidebar 狀態保存
        this.originalSidebarState = null;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單面板
            this.createHostelMenuPanel();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 保存並隱藏原本的 sidebar 狀態
    saveSidebarState() {
        console.log("保存並隱藏原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            // 保存原始狀態
            this.originalSidebarState = {
                display: sidebar.style.display || getComputedStyle(sidebar).display,
                visibility: sidebar.style.visibility || getComputedStyle(sidebar).visibility,
                opacity: sidebar.style.opacity || getComputedStyle(sidebar).opacity,
                zIndex: sidebar.style.zIndex || getComputedStyle(sidebar).zIndex
            };

            // 隱藏 sidebar（客棧有自己的選單系統）
            sidebar.style.display = 'none';

            console.log("Sidebar 狀態已保存並隱藏:", this.originalSidebarState);
        } else {
            console.warn("找不到 sidebar 元素，無法保存狀態");
            this.originalSidebarState = null;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 保存並隱藏原本的 sidebar
        this.saveSidebarState();

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎來到客棧！'
            : '🏨 歡迎來到休息區！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: rgb(168, 105, 38);; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;color: rgb(168, 105, 38);">
                ${instructionText}
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理基本按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    // ESC 鍵返回營地或上一個場景
                    if (this.previousScene === 'camp') {
                        this.returnToCamp();
                    } else {
                        console.log("按下 ESC，但無明確的返回目標");
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 暂停键盘控制
    pauseKeyboardControls() {
        console.log("暂停客栈键盘控制");
        this.keyboardPaused = true;
    }

    // 恢复键盘控制
    resumeKeyboardControls() {
        console.log("恢复客栈键盘控制");
        this.keyboardPaused = false;
    }

    // 創建客棧專用選單面板
    createHostelMenuPanel() {
        console.log("創建客棧專用選單面板");

        // 創建選單面板容器
        this.menuPanel = document.createElement('div');
        this.menuPanel.id = 'hostelMenuPanel';
        this.menuPanel.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(168, 105, 38);
            border-radius: 10px;
            padding: 20px;
            z-index: 5;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        `;

        // 創建選單標題
        const title = document.createElement('h3');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 20px;
            border-bottom: 2px solid rgb(168, 105, 38);
            padding-bottom: 10px;
        `;
        this.menuPanel.appendChild(title);

        // 創建選單選項
        const menuOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '🎒 背包管理',
                action: () => this.showInventoryManagement()
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({
                text: '� 前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建選項按鈕
        menuOptions.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 12px;
                margin: 8px 0;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(168, 105, 38);
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            // 按鈕懸停效果
            button.onmouseover = () => {
                button.style.transform = 'translateX(5px)';
            };

            button.onmouseout = () => {
                button.style.transform = 'translateX(0)';
            };

            button.onclick = option.action;
            this.menuPanel.appendChild(button);
        });

        this.hostelContainer.appendChild(this.menuPanel);
        console.log("客棧選單面板創建完成");
    }









    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 暂停键盘事件监听
        this.pauseKeyboardControls();

        // 创建加点数对话框
        this.createLevelUpDialog();
    }

    // 创建加点数对话框
    createLevelUpDialog() {
        console.log("创建加点数对话框");

        // 获取当前关卡的非 AI 玩家
        this.availablePlayers = this.getNonAIPlayers();

        if (this.availablePlayers.length === 0) {
            alert("没有可用的角色进行加点");
            this.resumeKeyboardControls();
            return;
        }

        // 当前选中的角色索引
        this.currentLevelUpPlayerIndex = 0;

        // 创建 dialog 元素
        const levelUpDialog = document.createElement("dialog");
        levelUpDialog.id = "levelUpDialog";
        levelUpDialog.style.cssText = `
            border: none;
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            padding: 0;
            margin: 0;
            background: transparent;
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
        `;

        // 创建加点数内容
        const levelUpContent = document.createElement("div");
        levelUpContent.style.cssText = `
            width: 80%;
            height: 85%;
            background: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 30px;
            box-sizing: border-box;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        `;

        // 创建标题区域
        const titleArea = this.createLevelUpTitleArea();

        // 创建主要内容区域（左右布局）
        const mainContentArea = document.createElement("div");
        mainContentArea.style.cssText = `
            flex: 1;
            display: flex;
            gap: 30px;
            margin: 20px 0;
        `;

        // 创建左侧区域
        const leftArea = this.createLevelUpLeftArea();

        // 创建右侧区域（五内属性）
        const rightArea = this.createAttributesArea();

        // 创建控制按钮区域
        const controlArea = this.createLevelUpControlArea(levelUpDialog);

        // 组装主要内容区域
        mainContentArea.appendChild(leftArea);
        mainContentArea.appendChild(rightArea);

        // 组装对话框
        levelUpContent.appendChild(titleArea);
        levelUpContent.appendChild(mainContentArea);
        levelUpContent.appendChild(controlArea);
        levelUpDialog.appendChild(levelUpContent);

        // 添加到页面并显示
        document.body.appendChild(levelUpDialog);
        levelUpDialog.showModal();

        // 初始化显示
        this.updateLevelUpDisplay();

        // 添加 Esc 键退出功能
        this.setupLevelUpEscapeHandler(levelUpDialog);

        console.log("加点数对话框已创建并显示");
    }

    // 创建加点数标题区域
    createLevelUpTitleArea() {
        const titleArea = document.createElement("div");
        titleArea.style.cssText = `
            text-align: center;
            margin-bottom: 20px;
        `;

        const title = document.createElement("h2");
        title.textContent = "⬆️ 角色加點";
        title.style.cssText = `
            color: rgb(165, 90, 24);
            font-size: 32px;
            font-weight: bold;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        `;

        titleArea.appendChild(title);
        return titleArea;
    }

    // 创建左侧区域
    createLevelUpLeftArea() {
        const leftArea = document.createElement("div");
        leftArea.style.cssText = `
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(165, 90, 24, 0.3);
        `;

        // 创建角色切换区域
        const playerSwitchArea = this.createPlayerSwitchArea();

        // 创建角色详细信息区域
        const playerDetailsArea = this.createPlayerDetailsArea();

        leftArea.appendChild(playerSwitchArea);
        leftArea.appendChild(playerDetailsArea);

        return leftArea;
    }

    // 创建角色切换区域
    createPlayerSwitchArea() {
        const switchArea = document.createElement("div");
        switchArea.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 15px;
            background: rgba(247, 231, 173, 0.6);
            border-radius: 8px;
            border: 2px solid rgba(165, 90, 24, 0.5);
        `;

        // 上一位按钮
        const prevPlayerBtn = document.createElement("button");
        prevPlayerBtn.id = "levelUpPrevPlayer";
        prevPlayerBtn.textContent = "◀";
        prevPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 角色信息显示
        const playerInfo = document.createElement("div");
        playerInfo.id = "levelUpPlayerInfo";
        playerInfo.style.cssText = `
            text-align: center;
            flex: 1;
            min-width: 200px;
        `;

        // 下一位按钮
        const nextPlayerBtn = document.createElement("button");
        nextPlayerBtn.id = "levelUpNextPlayer";
        nextPlayerBtn.textContent = "▶";
        nextPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 设置按钮事件
        prevPlayerBtn.addEventListener('click', () => this.switchToPreviousLevelUpPlayer());
        nextPlayerBtn.addEventListener('click', () => this.switchToNextLevelUpPlayer());

        // 添加悬停效果
        this.addLevelUpButtonHoverEffects(prevPlayerBtn, nextPlayerBtn);

        switchArea.appendChild(prevPlayerBtn);
        switchArea.appendChild(playerInfo);
        switchArea.appendChild(nextPlayerBtn);

        return switchArea;
    }

    // 创建角色详细信息区域
    createPlayerDetailsArea() {
        const detailsArea = document.createElement("div");
        detailsArea.id = "levelUpPlayerDetails";
        detailsArea.style.cssText = `
            flex: 1;
            padding: 20px;
            background: rgba(247, 231, 173, 0.6);
            border-radius: 8px;
            border: 2px solid rgba(165, 90, 24, 0.5);
            display: flex;
            flex-direction: column;
            gap: 15px;
        `;

        // 角色基本信息
        const basicInfo = document.createElement("div");
        basicInfo.id = "playerBasicInfo";
        basicInfo.style.cssText = `
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 6px;
        `;

        // 当前五内属性显示
        const currentAttributesDiv = document.createElement("div");
        currentAttributesDiv.id = "currentAttributes";
        currentAttributesDiv.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;

        const attributesTitle = document.createElement("div");
        attributesTitle.textContent = "當前五內屬性";
        attributesTitle.style.cssText = `
            font-size: 18px;
            font-weight: bold;
            color: rgb(165, 90, 24);
            text-align: center;
            margin-bottom: 10px;
        `;

        const attributesList = document.createElement("div");
        attributesList.id = "currentAttributesList";
        attributesList.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 8px;
        `;

        currentAttributesDiv.appendChild(attributesTitle);
        currentAttributesDiv.appendChild(attributesList);

        detailsArea.appendChild(basicInfo);
        detailsArea.appendChild(currentAttributesDiv);

        return detailsArea;
    }

    // 创建五内属性区域
    createAttributesArea() {
        const attributesArea = document.createElement("div");
        attributesArea.id = "levelUpAttributesArea";
        attributesArea.style.cssText = `
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(165, 90, 24, 0.3);
        `;

        // 剩余点数显示
        const remainingPointsDiv = document.createElement("div");
        remainingPointsDiv.id = "remainingPoints";
        remainingPointsDiv.style.cssText = `
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: rgb(165, 90, 24);
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(247, 231, 173, 0.8);
            border-radius: 8px;
            border: 2px solid rgb(165, 90, 24);
        `;

        // 五内属性列表
        const attributesList = document.createElement("div");
        attributesList.id = "attributesList";
        attributesList.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 15px;
        `;

        // 五内属性名称和说明
        const attributes = [
            { key: "迅", name: "迅", description: "影響移動力和迴避率" },
            { key: "烈", name: "烈", description: "影響攻擊力和暴擊率" },
            { key: "神", name: "神", description: "影響命中率和法術威力" },
            { key: "魔", name: "魔", description: "影響法力值和法術抗性" },
            { key: "魂", name: "魂", description: "影響生命值和精神抗性" }
        ];

        // 为每个属性创建控制行
        attributes.forEach(attr => {
            const attrRow = this.createAttributeRow(attr);
            attributesList.appendChild(attrRow);
        });

        attributesArea.appendChild(remainingPointsDiv);
        attributesArea.appendChild(attributesList);

        return attributesArea;
    }

    // 创建单个属性控制行
    createAttributeRow(attribute) {
        const row = document.createElement("div");
        row.className = `attribute-row-${attribute.key}`;
        row.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: rgba(247, 231, 173, 0.6);
            border-radius: 8px;
            border: 2px solid rgba(165, 90, 24, 0.5);
            transition: all 0.3s ease;
        `;

        // 属性名称和说明
        const nameSection = document.createElement("div");
        nameSection.style.cssText = `
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 5px;
        `;

        const nameDiv = document.createElement("div");
        nameDiv.style.cssText = `
            font-size: 20px;
            font-weight: bold;
            color: rgb(165, 90, 24);
        `;
        nameDiv.textContent = attribute.name;

        const descDiv = document.createElement("div");
        descDiv.style.cssText = `
            font-size: 14px;
            color: rgb(168, 105, 38);
            opacity: 0.8;
        `;
        descDiv.textContent = attribute.description;

        nameSection.appendChild(nameDiv);
        nameSection.appendChild(descDiv);

        // 当前值显示
        const currentValueDiv = document.createElement("div");
        currentValueDiv.id = `current-${attribute.key}`;
        currentValueDiv.style.cssText = `
            font-size: 18px;
            font-weight: bold;
            color: rgb(168, 105, 38);
            min-width: 60px;
            text-align: center;
        `;

        // 进度条容器
        const progressContainer = document.createElement("div");
        progressContainer.style.cssText = `
            flex: 1;
            margin: 0 20px;
            position: relative;
        `;

        const progressBar = document.createElement("div");
        progressBar.id = `progress-${attribute.key}`;
        progressBar.style.cssText = `
            width: 100%;
            height: 20px;
            background: rgba(165, 90, 24, 0.2);
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid rgb(165, 90, 24);
        `;

        const progressFill = document.createElement("div");
        progressFill.id = `progress-fill-${attribute.key}`;
        progressFill.style.cssText = `
            height: 100%;
            background: linear-gradient(90deg, rgb(165, 90, 24), rgb(200, 120, 50));
            border-radius: 10px;
            transition: width 0.5s ease;
            width: 0%;
        `;

        progressBar.appendChild(progressFill);
        progressContainer.appendChild(progressBar);

        // 控制按钮
        const controlsDiv = document.createElement("div");
        controlsDiv.style.cssText = `
            display: flex;
            gap: 10px;
            align-items: center;
        `;

        const minusBtn = document.createElement("button");
        minusBtn.id = `minus-${attribute.key}`;
        minusBtn.textContent = "−";
        minusBtn.style.cssText = `
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgb(220, 53, 69);
            color: white;
            border: 2px solid rgb(200, 35, 51);
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const plusBtn = document.createElement("button");
        plusBtn.id = `plus-${attribute.key}`;
        plusBtn.textContent = "+";
        plusBtn.style.cssText = `
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgb(40, 167, 69);
            color: white;
            border: 2px solid rgb(25, 135, 84);
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 设置按钮事件
        minusBtn.addEventListener('click', () => this.decreaseAttribute(attribute.key));
        plusBtn.addEventListener('click', () => this.increaseAttribute(attribute.key));

        // 添加按钮悬停效果
        this.addAttributeButtonHoverEffects(minusBtn, plusBtn);

        controlsDiv.appendChild(minusBtn);
        controlsDiv.appendChild(plusBtn);

        // 组装行
        row.appendChild(nameSection);
        row.appendChild(currentValueDiv);
        row.appendChild(progressContainer);
        row.appendChild(controlsDiv);

        return row;
    }

    // 创建控制按钮区域
    createLevelUpControlArea(levelUpDialog) {
        const controlArea = document.createElement("div");
        controlArea.style.cssText = `
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        `;

        // 确认按钮
        const confirmBtn = document.createElement("button");
        confirmBtn.textContent = "✅ 確認加點";
        confirmBtn.style.cssText = `
            padding: 15px 30px;
            background: rgb(40, 167, 69);
            color: white;
            border: 2px solid rgb(25, 135, 84);
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        `;

        // 重置按钮
        const resetBtn = document.createElement("button");
        resetBtn.textContent = "🔄 重置加點";
        resetBtn.style.cssText = `
            padding: 15px 30px;
            background: rgb(255, 193, 7);
            color: rgb(33, 37, 41);
            border: 2px solid rgb(255, 193, 7);
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        `;

        // 关闭按钮
        const closeBtn = document.createElement("button");
        closeBtn.textContent = "❌ 關閉";
        closeBtn.style.cssText = `
            padding: 15px 30px;
            background: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        `;

        // 设置按钮事件
        confirmBtn.addEventListener('click', () => this.confirmLevelUp());
        resetBtn.addEventListener('click', () => this.resetLevelUp());
        closeBtn.addEventListener('click', () => this.closeLevelUpDialog(levelUpDialog));

        // 添加悬停效果
        this.addLevelUpControlButtonHoverEffects(confirmBtn, resetBtn, closeBtn);

        controlArea.appendChild(confirmBtn);
        controlArea.appendChild(resetBtn);
        controlArea.appendChild(closeBtn);

        return controlArea;
    }

    // 添加属性按钮悬停效果
    addAttributeButtonHoverEffects(minusBtn, plusBtn) {
        // 减号按钮悬停效果
        minusBtn.addEventListener('mouseenter', () => {
            minusBtn.style.backgroundColor = 'rgb(200, 35, 51)';
            minusBtn.style.transform = 'scale(1.1)';
        });

        minusBtn.addEventListener('mouseleave', () => {
            minusBtn.style.backgroundColor = 'rgb(220, 53, 69)';
            minusBtn.style.transform = 'scale(1)';
        });

        // 加号按钮悬停效果
        plusBtn.addEventListener('mouseenter', () => {
            plusBtn.style.backgroundColor = 'rgb(25, 135, 84)';
            plusBtn.style.transform = 'scale(1.1)';
        });

        plusBtn.addEventListener('mouseleave', () => {
            plusBtn.style.backgroundColor = 'rgb(40, 167, 69)';
            plusBtn.style.transform = 'scale(1)';
        });
    }

    // 添加加点数按钮悬停效果
    addLevelUpButtonHoverEffects(prevBtn, nextBtn) {
        // 上一位按钮悬停效果
        prevBtn.addEventListener('mouseenter', () => {
            if (!prevBtn.disabled) {
                prevBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                prevBtn.style.transform = 'scale(1.1)';
            }
        });

        prevBtn.addEventListener('mouseleave', () => {
            if (!prevBtn.disabled) {
                prevBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                prevBtn.style.transform = 'scale(1)';
            }
        });

        // 下一位按钮悬停效果
        nextBtn.addEventListener('mouseenter', () => {
            if (!nextBtn.disabled) {
                nextBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                nextBtn.style.transform = 'scale(1.1)';
            }
        });

        nextBtn.addEventListener('mouseleave', () => {
            if (!nextBtn.disabled) {
                nextBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                nextBtn.style.transform = 'scale(1)';
            }
        });
    }

    // 添加控制按钮悬停效果
    addLevelUpControlButtonHoverEffects(confirmBtn, resetBtn, closeBtn) {
        // 确认按钮
        confirmBtn.addEventListener('mouseenter', () => {
            confirmBtn.style.backgroundColor = 'rgb(25, 135, 84)';
            confirmBtn.style.transform = 'translateY(-2px)';
        });

        confirmBtn.addEventListener('mouseleave', () => {
            confirmBtn.style.backgroundColor = 'rgb(40, 167, 69)';
            confirmBtn.style.transform = 'translateY(0)';
        });

        // 重置按钮
        resetBtn.addEventListener('mouseenter', () => {
            resetBtn.style.backgroundColor = 'rgb(255, 176, 0)';
            resetBtn.style.transform = 'translateY(-2px)';
        });

        resetBtn.addEventListener('mouseleave', () => {
            resetBtn.style.backgroundColor = 'rgb(255, 193, 7)';
            resetBtn.style.transform = 'translateY(0)';
        });

        // 关闭按钮
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.backgroundColor = 'rgb(255, 241, 183)';
            closeBtn.style.borderColor = 'rgb(185, 110, 44)';
            closeBtn.style.transform = 'translateY(-2px)';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.backgroundColor = 'rgb(247, 231, 173)';
            closeBtn.style.borderColor = 'rgb(165, 90, 24)';
            closeBtn.style.transform = 'translateY(0)';
        });
    }

    // 角色切换方法
    switchToPreviousLevelUpPlayer() {
        if (this.availablePlayers.length <= 1) return;

        this.currentLevelUpPlayerIndex = (this.currentLevelUpPlayerIndex - 1 + this.availablePlayers.length) % this.availablePlayers.length;
        console.log(`切换到上一个角色: ${this.availablePlayers[this.currentLevelUpPlayerIndex].name}`);

        this.updateLevelUpDisplay();
    }

    switchToNextLevelUpPlayer() {
        if (this.availablePlayers.length <= 1) return;

        this.currentLevelUpPlayerIndex = (this.currentLevelUpPlayerIndex + 1) % this.availablePlayers.length;
        console.log(`切换到下一个角色: ${this.availablePlayers[this.currentLevelUpPlayerIndex].name}`);

        this.updateLevelUpDisplay();
    }

    // 更新加点数显示
    updateLevelUpDisplay() {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentLevelUpPlayerIndex];

        // 初始化临时加点数据（如果不存在）
        if (!this.tempLevelUpData) {
            this.tempLevelUpData = {};
        }
        if (!this.tempLevelUpData[currentPlayer.id]) {
            this.tempLevelUpData[currentPlayer.id] = {
                "迅": 0,
                "烈": 0,
                "神": 0,
                "魔": 0,
                "魂": 0
            };
        }

        // 更新角色信息显示
        const playerInfo = document.getElementById('levelUpPlayerInfo');
        if (playerInfo) {
            playerInfo.innerHTML = `
                <div style="font-size: 20px; font-weight: bold; color: rgb(165, 90, 24);">
                    ${currentPlayer.name}
                </div>
            `;
        }

        // 更新角色详细信息
        const basicInfo = document.getElementById('playerBasicInfo');
        if (basicInfo) {
            basicInfo.innerHTML = `
                <div style="font-size: 18px; font-weight: bold; color: rgb(165, 90, 24); margin-bottom: 10px;">
                    ${currentPlayer.name}
                </div>
                <div style="font-size: 14px; color: rgb(168, 105, 38); margin-bottom: 5px;">
                    等級: ${currentPlayer.level || 1}
                </div>
                <div style="font-size: 14px; color: rgb(168, 105, 38); margin-bottom: 5px;">
                    職業: ${currentPlayer.job || '未知'}
                </div>
                <div style="font-size: 16px; font-weight: bold; color: rgb(40, 167, 69);">
                    可用點數: ${currentPlayer["點數"]}
                </div>
            `;
        }

        // 更新当前属性列表
        const currentAttributesList = document.getElementById('currentAttributesList');
        if (currentAttributesList) {
            const attributes = ["迅", "烈", "神", "魔", "魂"];
            currentAttributesList.innerHTML = '';

            attributes.forEach(attr => {
                const currentValue = currentPlayer["五內"][attr];
                const tempIncrease = this.tempLevelUpData[currentPlayer.id][attr];

                const attrDiv = document.createElement("div");
                attrDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 5px 10px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 4px;
                    font-size: 14px;
                `;

                attrDiv.innerHTML = `
                    <span style="color: rgb(165, 90, 24); font-weight: bold;">${attr}</span>
                    <span style="color: rgb(168, 105, 38);">
                        ${currentValue}${tempIncrease > 0 ? ` <span style="color: rgb(40, 167, 69);">+${tempIncrease}</span>` : ''}
                    </span>
                `;

                currentAttributesList.appendChild(attrDiv);
            });
        }

        // 更新剩余点数显示
        const usedPoints = Object.values(this.tempLevelUpData[currentPlayer.id]).reduce((sum, val) => sum + val, 0);
        const remainingPoints = currentPlayer["點數"] - usedPoints;

        const remainingPointsDiv = document.getElementById('remainingPoints');
        if (remainingPointsDiv) {
            remainingPointsDiv.innerHTML = `
                <div style="font-size: 20px;">剩餘點數</div>
                <div style="font-size: 32px; margin-top: 5px; color: ${remainingPoints > 0 ? 'rgb(40, 167, 69)' : 'rgb(220, 53, 69)'};">
                    ${remainingPoints}
                </div>
            `;
        }

        // 更新五内属性显示
        const attributes = ["迅", "烈", "神", "魔", "魂"];
        attributes.forEach(attr => {
            const currentValue = currentPlayer["五內"][attr];
            const tempIncrease = this.tempLevelUpData[currentPlayer.id][attr];
            const newValue = currentValue + tempIncrease;

            // 更新当前值显示
            const currentValueDiv = document.getElementById(`current-${attr}`);
            if (currentValueDiv) {
                currentValueDiv.innerHTML = tempIncrease > 0 ?
                    `${currentValue} <span style="color: rgb(40, 167, 69);">+${tempIncrease}</span>` :
                    `${currentValue}`;
            }

            // 更新进度条
            const progressFill = document.getElementById(`progress-fill-${attr}`);
            if (progressFill) {
                const maxValue = 100; // 假设最大值为100
                const percentage = Math.min((newValue / maxValue) * 100, 100);
                progressFill.style.width = `${percentage}%`;

                // 如果有临时增加，显示不同颜色
                if (tempIncrease > 0) {
                    const originalPercentage = Math.min((currentValue / maxValue) * 100, 100);
                    progressFill.style.background = `linear-gradient(90deg,
                        rgb(165, 90, 24) 0%,
                        rgb(165, 90, 24) ${originalPercentage}%,
                        rgb(40, 167, 69) ${originalPercentage}%,
                        rgb(40, 167, 69) 100%)`;
                } else {
                    progressFill.style.background = 'linear-gradient(90deg, rgb(165, 90, 24), rgb(200, 120, 50))';
                }
            }

            // 更新按钮状态
            const minusBtn = document.getElementById(`minus-${attr}`);
            const plusBtn = document.getElementById(`plus-${attr}`);

            if (minusBtn) {
                minusBtn.disabled = tempIncrease <= 0;
                minusBtn.style.opacity = tempIncrease <= 0 ? '0.5' : '1';
                minusBtn.style.cursor = tempIncrease <= 0 ? 'not-allowed' : 'pointer';
            }

            if (plusBtn) {
                plusBtn.disabled = remainingPoints <= 0;
                plusBtn.style.opacity = remainingPoints <= 0 ? '0.5' : '1';
                plusBtn.style.cursor = remainingPoints <= 0 ? 'not-allowed' : 'pointer';
            }
        });

        console.log(`加点数显示已更新: ${currentPlayer.name}`);
    }

    // 增加属性点数
    increaseAttribute(attributeKey) {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentLevelUpPlayerIndex];

        // 检查是否还有剩余点数
        const usedPoints = Object.values(this.tempLevelUpData[currentPlayer.id]).reduce((sum, val) => sum + val, 0);
        const remainingPoints = currentPlayer["點數"] - usedPoints;

        if (remainingPoints <= 0) {
            alert("沒有剩餘點數可以分配！");
            return;
        }

        // 增加临时点数
        this.tempLevelUpData[currentPlayer.id][attributeKey]++;

        console.log(`增加 ${currentPlayer.name} 的 ${attributeKey} 属性点数`);

        // 更新显示
        this.updateLevelUpDisplay();
    }

    // 减少属性点数
    decreaseAttribute(attributeKey) {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentLevelUpPlayerIndex];

        // 检查是否有可减少的点数
        if (this.tempLevelUpData[currentPlayer.id][attributeKey] <= 0) {
            return;
        }

        // 减少临时点数
        this.tempLevelUpData[currentPlayer.id][attributeKey]--;

        console.log(`减少 ${currentPlayer.name} 的 ${attributeKey} 属性点数`);

        // 更新显示
        this.updateLevelUpDisplay();
    }

    // 确认加点
    confirmLevelUp() {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentLevelUpPlayerIndex];
        const tempData = this.tempLevelUpData[currentPlayer.id];

        // 检查是否有分配点数
        const totalUsedPoints = Object.values(tempData).reduce((sum, val) => sum + val, 0);
        if (totalUsedPoints === 0) {
            alert("請先分配屬性點數！");
            return;
        }

        // 确认对话框
        const confirmMessage = `確定要為 ${currentPlayer.name} 分配以下屬性點數嗎？\n\n` +
            Object.entries(tempData)
                .filter(([key, value]) => value > 0)
                .map(([key, value]) => `${key}: +${value}`)
                .join('\n') +
            `\n\n總共使用 ${totalUsedPoints} 點數`;

        if (confirm(confirmMessage)) {
            // 应用加点
            Object.entries(tempData).forEach(([key, value]) => {
                if (value > 0) {
                    currentPlayer["五內"][key] += value;
                }
            });

            // 减少可用点数
            currentPlayer["點數"] -= totalUsedPoints;

            // 清除临时数据
            this.tempLevelUpData[currentPlayer.id] = {
                "迅": 0,
                "烈": 0,
                "神": 0,
                "魔": 0,
                "魂": 0
            };

            // 更新显示
            this.updateLevelUpDisplay();

            // 显示成功消息
            this.showLevelUpSuccessMessage(currentPlayer.name, totalUsedPoints);

            console.log(`${currentPlayer.name} 加点完成，使用了 ${totalUsedPoints} 点数`);
        }
    }

    // 重置加点
    resetLevelUp() {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentLevelUpPlayerIndex];

        // 重置临时数据
        this.tempLevelUpData[currentPlayer.id] = {
            "迅": 0,
            "烈": 0,
            "神": 0,
            "魔": 0,
            "魂": 0
        };

        // 更新显示
        this.updateLevelUpDisplay();

        console.log(`${currentPlayer.name} 的加点已重置`);
    }

    // 显示加点成功消息
    showLevelUpSuccessMessage(playerName, usedPoints) {
        // 创建消息对话框
        const messageDialog = document.createElement("dialog");
        messageDialog.id = "levelUpSuccessDialog";
        messageDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            z-index: 10005;
        `;

        messageDialog.innerHTML = `
            <div style="
                background: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 4px solid rgb(165, 90, 24);
                border-radius: 15px;
                padding: 25px 35px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                min-width: 300px;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 15px 0; font-size: 22px; font-weight: bold;">✅ 加點成功！</h3>
                <p style="margin: 12px 0; font-size: 16px; font-weight: bold;">
                    ${playerName} 成功分配了 ${usedPoints} 個屬性點數！
                </p>
                <p style="margin: 12px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    屬性提升已生效
                </p>
            </div>
        `;

        document.body.appendChild(messageDialog);
        messageDialog.showModal();

        // 2秒后自动关闭
        setTimeout(() => {
            messageDialog.close();
            document.body.removeChild(messageDialog);
        }, 2000);
    }

    // 设置加点数 Esc 键处理
    setupLevelUpEscapeHandler(levelUpDialog) {
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeLevelUpDialog(levelUpDialog);
                // 移除事件监听器
                document.removeEventListener('keydown', escapeHandler);
                console.log("按下 Esc 键，退出加点数");
            }
        };

        // 添加事件监听器
        document.addEventListener('keydown', escapeHandler);

        // 保存事件处理器引用，以便在对话框关闭时清理
        levelUpDialog.escapeHandler = escapeHandler;
    }

    // 关闭加点数对话框
    closeLevelUpDialog(levelUpDialog) {
        console.log("关闭加点数对话框");

        // 检查是否有未确认的加点
        let hasUnconfirmedChanges = false;
        if (this.tempLevelUpData) {
            Object.values(this.tempLevelUpData).forEach(playerData => {
                const totalUsed = Object.values(playerData).reduce((sum, val) => sum + val, 0);
                if (totalUsed > 0) {
                    hasUnconfirmedChanges = true;
                }
            });
        }

        // 如果有未确认的更改，询问用户
        if (hasUnconfirmedChanges) {
            const confirmClose = confirm("您有未確認的加點分配，確定要關閉嗎？\n未確認的更改將會丟失。");
            if (!confirmClose) {
                return;
            }
        }

        // 清理 ESC 键事件监听器
        if (levelUpDialog.escapeHandler) {
            document.removeEventListener('keydown', levelUpDialog.escapeHandler);
            levelUpDialog.escapeHandler = null;
            console.log("已清理加点数 Esc 键事件监听器");
        }

        // 清理临时数据
        this.tempLevelUpData = null;
        this.currentLevelUpPlayerIndex = 0;

        // 关闭对话框
        levelUpDialog.close();
        document.body.removeChild(levelUpDialog);

        // 恢复键盘控制
        this.resumeKeyboardControls();

        console.log("加点数对话框已关闭，键盘控制已恢复");
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示客棧存檔選單");
        this.showSaveHostelDialog();
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 顯示背包管理界面
    showInventoryManagement() {
        console.log("顯示背包管理界面");

        // 暂停键盘事件监听
        this.pauseKeyboardControls();

        // 创建背包管理对话框
        this.createInventoryDialog();
    }

    // 创建背包管理对话框
    createInventoryDialog() {
        console.log("创建背包管理对话框");

        // 创建 dialog 元素
        const inventoryDialog = document.createElement("dialog");
        inventoryDialog.id = "inventoryDialog";
        inventoryDialog.style.cssText = `
            border: none;
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            padding: 0;
            margin: 0;
            background: transparent;
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
        `;

        // 创建背包管理内容
        const inventoryContent = document.createElement("div");
        inventoryContent.style.cssText = `
            width: 90%;
            height: 95%;
            background: transparent;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 40px;
            box-sizing: border-box;
            border-radius: 15px;
        `;

        const inventoryMidArea = document.createElement("div");
        inventoryMidArea.style.cssText = `
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;
            opacity: 0;
            animation: shopappear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
        `;

        // 左半边：行囊容器
        const publicBagContainer = this.createPublicBagContainer();

        // 右半边：角色背包容器
        const playerBagContainer = this.createPlayerBagContainer();

        // 关闭按钮
        const closeButton = this.createInventoryCloseButton(inventoryDialog);

        // 组装对话框
        inventoryMidArea.appendChild(publicBagContainer);
        inventoryMidArea.appendChild(playerBagContainer);
        inventoryContent.appendChild(inventoryMidArea);
        inventoryContent.appendChild(closeButton);
        inventoryDialog.appendChild(inventoryContent);

        // 添加到页面并显示
        document.body.appendChild(inventoryDialog);
        inventoryDialog.showModal();

        // 添加 Esc 键退出功能
        this.setupInventoryEscapeHandler(inventoryDialog);

        console.log("背包管理对话框已创建并显示");
    }

    // 创建行囊容器（左半边）
    createPublicBagContainer() {
        const publicBagContainer = document.createElement("div");
        publicBagContainer.id = "publicBagContainer";
        publicBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: left;
            overflow-y: auto;
            background: url(./Public/mybagwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-right: 20px;

        `;

        // 行囊标题
        const publicBagTitle = document.createElement("div");
        publicBagTitle.textContent = "行囊";
        publicBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            letter-spacing: 5px;
            margin-top: 10px;
            margin-bottom: 20px;
            width: 40%;
            margin-top: -8px;
            margin-left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            
        `;
        publicBagContainer.appendChild(publicBagTitle);

        // 行囊物品容器
        const publicBagItemsContainer = document.createElement("div");
        publicBagItemsContainer.id = "publicBagItems";
        publicBagItemsContainer.style.cssText = `
            margin-left: 6%;
            margin-top: 2%;
            width: 90%;
            height: 75%;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        publicBagContainer.appendChild(publicBagItemsContainer);

        // 渲染行囊物品
        this.renderPublicBagItems(publicBagItemsContainer);

        return publicBagContainer;
    }

    // 创建角色背包容器（右半边）
    createPlayerBagContainer() {
        const playerBagContainer = document.createElement("div");
        playerBagContainer.id = "playerBagContainer";
        playerBagContainer.style.cssText = `
            flex: 1;
            position: relative;
            float: right;
            overflow-y: auto;
            background: url(./Public/shopwin.png);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            width: 45%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-left: 20px;
        `;

        // 角色背包标题容器
        const playerTitleContainer = document.createElement("div");
        playerTitleContainer.style.cssText = `
            position: relative;
            float: right;
            width: 35%;
            height: 10%;
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin-top: -8px;
            margin-right: 40px;
        `;

        // 上一位按钮
        const prevPlayerBtn = document.createElement("button");
        prevPlayerBtn.textContent = "◀";
        prevPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 角色背包标题
        const playerBagTitle = document.createElement("div");
        playerBagTitle.id = "playerBagTitle";
        playerBagTitle.textContent = "角色背包";
        playerBagTitle.style.cssText = `
            color: rgb(168, 105, 38);
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            letter-spacing: 5px;
            flex: 1;

        `;

        // 下一位按钮
        const nextPlayerBtn = document.createElement("button");
        nextPlayerBtn.textContent = "▶";
        nextPlayerBtn.style.cssText = `
            background: rgba(168, 105, 38, 0.8);
            color: rgb(247, 231, 173);
            border: 2px solid rgb(165, 90, 24);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 组装标题容器
        playerTitleContainer.appendChild(prevPlayerBtn);
        playerTitleContainer.appendChild(playerBagTitle);
        playerTitleContainer.appendChild(nextPlayerBtn);
        playerBagContainer.appendChild(playerTitleContainer);

        // 角色背包物品容器
        const playerBagItemsContainer = document.createElement("div");
        playerBagItemsContainer.id = "playerBagItems";
        playerBagItemsContainer.style.cssText = `
            margin-left: 2%;
            margin-top: 9%;
            width: 88%;
            height: 75%;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
        `;
        playerBagContainer.appendChild(playerBagItemsContainer);

        // 初始化角色背包切换系统
        this.initPlayerBagSwitchSystem(prevPlayerBtn, nextPlayerBtn, playerBagTitle, playerBagItemsContainer);

        return playerBagContainer;
    }

    // 创建关闭按钮
    createInventoryCloseButton(inventoryDialog) {
        const closeButton = document.createElement("button");
        closeButton.textContent = "關閉背包管理";
        closeButton.style.cssText = `
            padding: 15px 30px;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 10px;
            font-size: 25px;
            font-weight: bold;
            cursor: pointer;
            align-self: center;
            transition: all 0.3s ease;
            opacity: 0;
            animation: shopappear 1.5s forwards ease-in-out;
            animation-delay: 0.5s;
            margin-top: 20px;
        `;

        closeButton.addEventListener('click', () => {
            this.closeInventoryDialog(inventoryDialog);
        });

        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = 'rgb(165, 90, 24)';
            closeButton.style.color = 'white';
            closeButton.style.transform = 'scale(1.05)';
        });

        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = 'rgb(247, 231, 173)';
            closeButton.style.color = 'rgb(168, 105, 38)';
            closeButton.style.transform = 'scale(1)';
        });

        return closeButton;
    }

    // 设置背包管理 Esc 键处理
    setupInventoryEscapeHandler(inventoryDialog) {
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeInventoryDialog(inventoryDialog);
                // 移除事件监听器
                document.removeEventListener('keydown', escapeHandler);
                console.log("按下 Esc 键，退出背包管理");
            }
        };

        // 添加事件监听器
        document.addEventListener('keydown', escapeHandler);

        // 保存事件处理器引用，以便在对话框关闭时清理
        inventoryDialog.escapeHandler = escapeHandler;
    }

    // 关闭背包管理对话框
    closeInventoryDialog(inventoryDialog) {
        console.log("关闭背包管理对话框");

        // 清理 Esc 键事件监听器
        if (inventoryDialog.escapeHandler) {
            document.removeEventListener('keydown', inventoryDialog.escapeHandler);
            inventoryDialog.escapeHandler = null;
            console.log("已清理背包管理 Esc 键事件监听器");
        }

        // 关闭对话框
        inventoryDialog.close();
        document.body.removeChild(inventoryDialog);

        // 恢复键盘控制
        this.resumeKeyboardControls();

        console.log("背包管理对话框已关闭，键盘控制已恢复");
    }

    // 渲染行囊物品
    renderPublicBagItems(container) {
        console.log("渲染行囊物品");

        // 清空容器
        container.innerHTML = '';

        // 统计物品数量
        const itemMap = new Map();
        if (typeof mybag !== 'undefined' && mybag.length > 0) {
            mybag.forEach(item => {
                if (itemMap.has(item.name)) {
                    const existing = itemMap.get(item.name);
                    existing.count += 1;
                } else {
                    itemMap.set(item.name, {
                        price: item.price || 0,
                        count: 1,
                        item: item
                    });
                }
            });
        }

        // 显示物品
        if (itemMap.size > 0) {
            itemMap.forEach((data, name) => {
                const itemDiv = document.createElement("div");
                itemDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 20px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    font-size: 24px;
                    color: rgb(168, 105, 38);
                    box-sizing: border-box;
                    border: 3px solid transparent;
                    transition: all 0.3s ease;
                `;

                itemDiv.innerHTML = `
                    <span style="font-weight: bold;">${name}</span>
                    <span style="color: rgb(165, 90, 24);">x${data.count}</span>
                `;

                // 悬停效果
                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.borderColor = 'rgb(165, 90, 24)';
                    itemDiv.style.backgroundColor = 'rgba(247, 231, 173, 0.3)';
                });

                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.borderColor = 'transparent';
                    itemDiv.style.backgroundColor = 'transparent';
                });

                // 点击事件：显示操作选单
                itemDiv.addEventListener('click', () => {
                    this.showItemActionMenu(data.item, name, 'public');
                });

                container.appendChild(itemDiv);
            });
        } else {
            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = "行囊是空的";
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 18px;
            `;
            container.appendChild(emptyMsg);
        }

        console.log(`行囊显示已更新，物品数量: ${itemMap.size}`);
    }

    // 初始化角色背包切换系统
    initPlayerBagSwitchSystem(prevBtn, nextBtn, titleElement, itemsContainer) {
        console.log("初始化角色背包切换系统");

        // 获取当前关卡的非 AI 玩家
        this.availablePlayers = this.getNonAIPlayers();

        if (this.availablePlayers.length === 0) {
            console.warn("没有找到非 AI 玩家");
            titleElement.textContent = "無可用角色";

            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = "沒有可用的角色背包";
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 18px;
            `;
            itemsContainer.appendChild(emptyMsg);

            // 禁用按钮
            prevBtn.disabled = true;
            nextBtn.disabled = true;
            prevBtn.style.opacity = '0.5';
            nextBtn.style.opacity = '0.5';
            return;
        }

        // 当前选中的角色索引
        this.currentPlayerIndex = 0;

        console.log("可用角色:", this.availablePlayers.map(player => player.name));

        // 设置按钮事件
        prevBtn.addEventListener('click', () => {
            this.switchToPreviousPlayer(titleElement, itemsContainer);
        });

        nextBtn.addEventListener('click', () => {
            this.switchToNextPlayer(titleElement, itemsContainer);
        });

        // 添加按钮悬停效果
        this.addPlayerBagButtonHoverEffects(prevBtn, nextBtn);

        // 初始化显示
        this.updatePlayerBagDisplay(titleElement, itemsContainer);
    }

    // 获取当前关卡的非 AI 玩家
    getNonAIPlayers() {
        console.log("获取当前关卡的非 AI 玩家");

        // 从当前关卡数据获取玩家
        if (typeof controlLayer !== 'undefined' && typeof currentLevel !== 'undefined' &&
            controlLayer[currentLevel] && controlLayer[currentLevel].Players) {

            const levelPlayers = controlLayer[currentLevel].Players;
            const nonAIPlayers = [];

            levelPlayers.forEach(playerTemplate => {
                // 根据 ID 找到对应的玩家数据
                let playerData = null;

                // 先从全局玩家变量中查找
                if (typeof Player0 !== 'undefined' && Player0.id === playerTemplate.id && !Player0["是否電腦操作"]) {
                    playerData = Player0;
                } else if (typeof Player1 !== 'undefined' && Player1.id === playerTemplate.id && !Player1["是否電腦操作"]) {
                    playerData = Player1;
                } else if (typeof Player2 !== 'undefined' && Player2.id === playerTemplate.id && !Player2["是否電腦操作"]) {
                    playerData = Player2;
                } else if (typeof Player3 !== 'undefined' && Player3.id === playerTemplate.id && !Player3["是否電腦操作"]) {
                    playerData = Player3;
                } else if (typeof Player4 !== 'undefined' && Player4.id === playerTemplate.id && !Player4["是否電腦操作"]) {
                    playerData = Player4;
                } else if (typeof Player5 !== 'undefined' && Player5.id === playerTemplate.id && !Player5["是否電腦操作"]) {
                    playerData = Player5;
                }

                if (playerData) {
                    // 确保玩家有 Inventory 属性
                    if (!playerData.Inventory) {
                        playerData.Inventory = [];
                    }
                    nonAIPlayers.push(playerData);
                    console.log(`找到非 AI 玩家: ${playerData.name} (${playerData.id})`);
                }
            });

            console.log(`总共找到 ${nonAIPlayers.length} 个非 AI 玩家`);
            return nonAIPlayers;
        }

        console.warn("无法获取当前关卡的玩家资料");
        return [];
    }

    // 切换到上一个角色
    switchToPreviousPlayer(titleElement, itemsContainer) {
        if (this.availablePlayers.length <= 1) return;

        this.currentPlayerIndex = (this.currentPlayerIndex - 1 + this.availablePlayers.length) % this.availablePlayers.length;
        console.log(`切换到上一个角色: ${this.availablePlayers[this.currentPlayerIndex].name}`);

        this.updatePlayerBagDisplay(titleElement, itemsContainer);
    }

    // 切换到下一个角色
    switchToNextPlayer(titleElement, itemsContainer) {
        if (this.availablePlayers.length <= 1) return;

        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.availablePlayers.length;
        console.log(`切换到下一个角色: ${this.availablePlayers[this.currentPlayerIndex].name}`);

        this.updatePlayerBagDisplay(titleElement, itemsContainer);
    }

    // 更新角色背包显示
    updatePlayerBagDisplay(titleElement, itemsContainer) {
        if (!this.availablePlayers || this.availablePlayers.length === 0) return;

        const currentPlayer = this.availablePlayers[this.currentPlayerIndex];

        // 更新标题显示角色名称和背包容量
        const currentCount = currentPlayer.Inventory.length;
        const maxCount = 6;
        titleElement.textContent = `${currentPlayer.name}`;

        // 渲染角色背包物品
        this.renderPlayerBagItems(itemsContainer, currentPlayer);
    }

    // 渲染角色背包物品
    renderPlayerBagItems(container, player) {
        console.log(`渲染 ${player.name} 的背包物品`);

        // 清空容器
        container.innerHTML = '';

        // 统计物品数量
        const itemMap = new Map();
        if (player.Inventory && player.Inventory.length > 0) {
            player.Inventory.forEach(item => {
                if (itemMap.has(item.name)) {
                    const existing = itemMap.get(item.name);
                    existing.count += 1;
                } else {
                    itemMap.set(item.name, {
                        price: item.price || 0,
                        count: 1,
                        item: item
                    });
                }
            });
        }

        // 显示物品
        if (itemMap.size > 0) {
            itemMap.forEach((data, name) => {
                const itemDiv = document.createElement("div");
                itemDiv.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 20px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    font-size: 24px;
                    color: rgb(168, 105, 38);
                    box-sizing: border-box;
                    border: 3px solid transparent;
                    transition: all 0.3s ease;
                `;

                itemDiv.innerHTML = `
                    <span style="font-weight: bold;">${name}</span>
                    <span style="color: rgb(165, 90, 24);">x${data.count}</span>
                `;

                // 悬停效果
                itemDiv.addEventListener('mouseenter', () => {
                    itemDiv.style.borderColor = 'rgb(165, 90, 24)';
                    itemDiv.style.backgroundColor = 'rgba(247, 231, 173, 0.3)';
                });

                itemDiv.addEventListener('mouseleave', () => {
                    itemDiv.style.borderColor = 'transparent';
                    itemDiv.style.backgroundColor = 'transparent';
                });

                // 点击事件：显示操作选单
                itemDiv.addEventListener('click', () => {
                    this.showItemActionMenu(data.item, name, 'player', player);
                });

                container.appendChild(itemDiv);
            });
        } else {
            const emptyMsg = document.createElement("div");
            emptyMsg.textContent = `${player.name} 的背包是空的`;
            emptyMsg.style.cssText = `
                color: #999;
                text-align: center;
                font-style: italic;
                padding: 20px;
                font-size: 18px;
            `;
            container.appendChild(emptyMsg);
        }

        console.log(`${player.name} 背包显示已更新，物品数量: ${itemMap.size}`);
    }

    // 添加按钮悬停效果
    addPlayerBagButtonHoverEffects(prevBtn, nextBtn) {
        // 上一位按钮悬停效果
        prevBtn.addEventListener('mouseenter', () => {
            if (!prevBtn.disabled) {
                prevBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                prevBtn.style.transform = 'scale(1.1)';
            }
        });

        prevBtn.addEventListener('mouseleave', () => {
            if (!prevBtn.disabled) {
                prevBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                prevBtn.style.transform = 'scale(1)';
            }
        });

        // 下一位按钮悬停效果
        nextBtn.addEventListener('mouseenter', () => {
            if (!nextBtn.disabled) {
                nextBtn.style.backgroundColor = 'rgba(165, 90, 24, 1)';
                nextBtn.style.transform = 'scale(1.1)';
            }
        });

        nextBtn.addEventListener('mouseleave', () => {
            if (!nextBtn.disabled) {
                nextBtn.style.backgroundColor = 'rgba(168, 105, 38, 0.8)';
                nextBtn.style.transform = 'scale(1)';
            }
        });
    }

    // 移动物品从行囊到角色背包
    moveItemToPlayerBag(item, itemName) {
        if (!this.availablePlayers || this.availablePlayers.length === 0) {
            alert("没有可用的角色背包");
            return;
        }

        const currentPlayer = this.availablePlayers[this.currentPlayerIndex];

        // 检查角色背包是否已满
        if (currentPlayer.Inventory.length >= 6) {
            alert(`${currentPlayer.name} 的背包已满！\n个人背包最多只能放6个物品。`);
            return;
        }

        // 从行囊中移除物品
        const itemIndex = mybag.findIndex(bagItem => bagItem.name === item.name);
        if (itemIndex === -1) {
            alert("在行囊中找不到该物品");
            return;
        }

        const removedItem = mybag.splice(itemIndex, 1)[0];

        // 添加到角色背包
        currentPlayer.Inventory.push(removedItem);

        console.log(`物品 ${itemName} 已从行囊移动到 ${currentPlayer.name} 的背包`);

        // 刷新显示
        this.refreshInventoryDisplay();

        // 显示成功消息
        this.showMoveItemMessage(`${itemName} 已移动到 ${currentPlayer.name} 的背包`);
    }

    // 移动物品从角色背包到行囊
    moveItemToPublicBag(item, itemName, player) {
        // 从角色背包中移除物品
        const itemIndex = player.Inventory.findIndex(bagItem => bagItem.name === item.name);
        if (itemIndex === -1) {
            alert(`在 ${player.name} 的背包中找不到该物品`);
            return;
        }

        const removedItem = player.Inventory.splice(itemIndex, 1)[0];

        // 添加到行囊
        if (typeof mybag === 'undefined') {
            window.mybag = [];
        }
        mybag.push(removedItem);

        console.log(`物品 ${itemName} 已从 ${player.name} 的背包移动到行囊`);

        // 刷新显示
        this.refreshInventoryDisplay();

        // 显示成功消息
        this.showMoveItemMessage(`${itemName} 已移动到行囊`);
    }

    // 刷新背包管理显示
    refreshInventoryDisplay() {
        console.log("刷新背包管理显示");

        // 刷新行囊显示
        const publicBagItems = document.getElementById("publicBagItems");
        if (publicBagItems) {
            this.renderPublicBagItems(publicBagItems);
        }

        // 刷新角色背包显示
        const playerBagItems = document.getElementById("playerBagItems");
        const playerBagTitle = document.getElementById("playerBagTitle");
        if (playerBagItems && playerBagTitle && this.availablePlayers && this.availablePlayers.length > 0) {
            this.updatePlayerBagDisplay(playerBagTitle, playerBagItems);
        }

        console.log("背包管理显示已刷新");
    }

    // 显示物品移动消息
    showMoveItemMessage(message) {
        // 创建消息对话框
        const messageDialog = document.createElement("dialog");
        messageDialog.id = "moveItemMessageDialog";
        messageDialog.style.cssText = `
            border: none;
            border-radius: 10px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            z-index: 10002;
        `;

        messageDialog.innerHTML = `
            <div style="
                background: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 4px solid rgb(165, 90, 24);
                border-radius: 15px;
                padding: 25px 35px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                min-width: 300px;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 15px 0; font-size: 22px; font-weight: bold;">✅ 移動成功！</h3>
                <p style="margin: 12px 0; font-size: 16px; font-weight: bold;">
                    ${message}
                </p>
            </div>
        `;

        document.body.appendChild(messageDialog);
        messageDialog.showModal();

        // 1.5秒后自动关闭
        setTimeout(() => {
            messageDialog.close();
            document.body.removeChild(messageDialog);
        }, 1500);
    }

    // 显示物品操作选单
    showItemActionMenu(item, itemName, bagType, player = null) {
        console.log(`显示物品操作选单: ${itemName} (${bagType})`);

        // 创建操作选单对话框
        const actionDialog = document.createElement("dialog");
        actionDialog.id = "itemActionDialog";
        actionDialog.style.cssText = `
            border: none;
            border-radius: 15px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            z-index: 10003;
        `;

        // 确定操作选项
        const isFromPublicBag = bagType === 'public';
        const transferText = isFromPublicBag ? '轉移到角色背包' : '轉移到行囊';
        const transferAction = isFromPublicBag ?
            () => this.moveItemToPlayerBag(item, itemName) :
            () => this.moveItemToPublicBag(item, itemName, player);

        actionDialog.innerHTML = `
            <div style="
                background: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 4px solid rgb(165, 90, 24);
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                min-width: 350px;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 20px 0; font-size: 24px; font-weight: bold;">
                    📦 ${itemName}
                </h3>
                <p style="margin: 15px 0; font-size: 16px; color: rgb(168, 105, 38);">
                    請選擇要執行的操作：
                </p>

                <div style="display: flex; flex-direction: column; gap: 15px; margin-top: 25px;">
                    <button onclick="transferItem()" style="
                        width: 100%;
                        padding: 15px;
                        color: rgb(168, 105, 38);
                        background: rgb(247, 231, 173);
                        border: 2px solid rgb(165, 90, 24);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 18px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        🔄 ${transferText}
                    </button>

                    <button onclick="discardItem()" style="
                        width: 100%;
                        padding: 15px;
                        color: rgb(168, 105, 38);
                        background: rgb(247, 231, 173);
                        border: 2px solid rgb(165, 90, 24);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 18px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        🗑️ 丟棄物品
                    </button>

                    <button onclick="closeActionMenu()" style="
                        width: 100%;
                        padding: 12px;
                        color: rgb(168, 105, 38);
                        background: rgb(247, 231, 173);
                        border: 2px solid rgb(165, 90, 24);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        ❌ 取消
                    </button>
                </div>
            </div>
        `;

        // 设置全局函数供按钮调用
        window.transferItem = () => {
            this.closeItemActionDialog(actionDialog);
            transferAction();
        };

        window.discardItem = () => {
            this.closeItemActionDialog(actionDialog);
            this.showDiscardConfirmDialog(item, itemName, bagType, player);
        };

        window.closeActionMenu = () => {
            this.closeItemActionDialog(actionDialog);
        };

        // 添加按钮悬停效果
        document.body.appendChild(actionDialog);
        actionDialog.showModal();

        // 为按钮添加悬停效果
        const buttons = actionDialog.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                button.style.backgroundColor = 'rgb(255, 241, 183)';
                button.style.borderColor = 'rgb(185, 110, 44)';
                button.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.backgroundColor = 'rgb(247, 231, 173)';
                button.style.borderColor = 'rgb(165, 90, 24)';
                button.style.transform = 'translateY(0)';
            });
        });

        // 添加 ESC 键关闭功能
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeItemActionDialog(actionDialog);
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        actionDialog.escapeHandler = escapeHandler;

        console.log("物品操作选单已显示");
    }

    // 关闭物品操作对话框
    closeItemActionDialog(actionDialog) {
        console.log("关闭物品操作对话框");

        // 清理 ESC 键事件监听器
        if (actionDialog.escapeHandler) {
            document.removeEventListener('keydown', actionDialog.escapeHandler);
            actionDialog.escapeHandler = null;
        }

        // 清理全局函数
        if (window.transferItem) delete window.transferItem;
        if (window.discardItem) delete window.discardItem;
        if (window.closeActionMenu) delete window.closeActionMenu;

        // 关闭对话框
        actionDialog.close();
        document.body.removeChild(actionDialog);

        console.log("物品操作对话框已关闭");
    }

    // 显示丢弃确认对话框
    showDiscardConfirmDialog(item, itemName, bagType, player = null) {
        console.log(`显示丢弃确认对话框: ${itemName}`);

        // 创建确认对话框
        const confirmDialog = document.createElement("dialog");
        confirmDialog.id = "discardConfirmDialog";
        confirmDialog.style.cssText = `
            border: none;
            border-radius: 15px;
            padding: 0;
            background: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            max-width: none;
            max-height: none;
            z-index: 10004;
        `;

        confirmDialog.innerHTML = `
            <div style="
                background: rgb(247, 231, 173);
                color: rgb(168, 105, 38);
                border: 4px solid rgb(165, 90, 24);
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                min-width: 350px;
            ">
                <h3 style="color: rgb(165, 90, 24); margin: 0 0 20px 0; font-size: 24px; font-weight: bold;">
                    ⚠️ 確認丟棄
                </h3>
                <p style="margin: 15px 0; font-size: 18px; color: rgb(168, 105, 38); font-weight: bold;">
                    確定要丟棄 <span style="color: rgb(165, 90, 24); text-decoration: underline;">${itemName}</span> 嗎？
                </p>
                <p style="margin: 15px 0; font-size: 14px; color: rgb(168, 105, 38);">
                    丟棄的物品將永久消失，無法復原！
                </p>

                <div style="display: flex; gap: 15px; margin-top: 25px;">
                    <button onclick="confirmDiscard()" style="
                        flex: 1;
                        padding: 15px;
                        color: white;
                        background: rgb(220, 53, 69);
                        border: 2px solid rgb(200, 35, 51);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        🗑️ 確定丟棄
                    </button>

                    <button onclick="cancelDiscard()" style="
                        flex: 1;
                        padding: 15px;
                        color: rgb(168, 105, 38);
                        background: rgb(247, 231, 173);
                        border: 2px solid rgb(165, 90, 24);
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        ❌ 取消
                    </button>
                </div>
            </div>
        `;

        // 设置全局函数供按钮调用
        window.confirmDiscard = () => {
            this.closeDiscardConfirmDialog(confirmDialog);
            this.discardItem(item, itemName, bagType, player);
        };

        window.cancelDiscard = () => {
            this.closeDiscardConfirmDialog(confirmDialog);
        };

        // 添加按钮悬停效果
        document.body.appendChild(confirmDialog);
        confirmDialog.showModal();

        // 为按钮添加悬停效果
        const confirmBtn = confirmDialog.querySelector('button[onclick="confirmDiscard()"]');
        const cancelBtn = confirmDialog.querySelector('button[onclick="cancelDiscard()"]');

        confirmBtn.addEventListener('mouseenter', () => {
            confirmBtn.style.backgroundColor = 'rgb(200, 35, 51)';
            confirmBtn.style.transform = 'translateY(-2px)';
        });

        confirmBtn.addEventListener('mouseleave', () => {
            confirmBtn.style.backgroundColor = 'rgb(220, 53, 69)';
            confirmBtn.style.transform = 'translateY(0)';
        });

        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.backgroundColor = 'rgb(255, 241, 183)';
            cancelBtn.style.borderColor = 'rgb(185, 110, 44)';
            cancelBtn.style.transform = 'translateY(-2px)';
        });

        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.backgroundColor = 'rgb(247, 231, 173)';
            cancelBtn.style.borderColor = 'rgb(165, 90, 24)';
            cancelBtn.style.transform = 'translateY(0)';
        });

        // 添加 ESC 键关闭功能
        const escapeHandler = (event) => {
            if (event.code === 'Escape') {
                event.preventDefault();
                event.stopPropagation();
                this.closeDiscardConfirmDialog(confirmDialog);
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        confirmDialog.escapeHandler = escapeHandler;

        console.log("丢弃确认对话框已显示");
    }

    // 关闭丢弃确认对话框
    closeDiscardConfirmDialog(confirmDialog) {
        console.log("关闭丢弃确认对话框");

        // 清理 ESC 键事件监听器
        if (confirmDialog.escapeHandler) {
            document.removeEventListener('keydown', confirmDialog.escapeHandler);
            confirmDialog.escapeHandler = null;
        }

        // 清理全局函数
        if (window.confirmDiscard) delete window.confirmDiscard;
        if (window.cancelDiscard) delete window.cancelDiscard;

        // 关闭对话框
        confirmDialog.close();
        document.body.removeChild(confirmDialog);

        console.log("丢弃确认对话框已关闭");
    }

    // 丢弃物品
    discardItem(item, itemName, bagType, player = null) {
        console.log(`丢弃物品: ${itemName} (${bagType})`);

        try {
            if (bagType === 'public') {
                // 从行囊中移除物品
                const itemIndex = mybag.findIndex(bagItem => bagItem.name === item.name);
                if (itemIndex === -1) {
                    alert("在行囊中找不到该物品");
                    return;
                }
                mybag.splice(itemIndex, 1);
                console.log(`物品 ${itemName} 已从行囊中丢弃`);
            } else {
                // 从角色背包中移除物品
                if (!player || !player.Inventory) {
                    alert("角色背包数据异常");
                    return;
                }
                const itemIndex = player.Inventory.findIndex(bagItem => bagItem.name === item.name);
                if (itemIndex === -1) {
                    alert(`在 ${player.name} 的背包中找不到该物品`);
                    return;
                }
                player.Inventory.splice(itemIndex, 1);
                console.log(`物品 ${itemName} 已从 ${player.name} 的背包中丢弃`);
            }

            // 刷新显示
            this.refreshInventoryDisplay();

            // 显示丢弃成功消息
            this.showMoveItemMessage(`${itemName} 已丢弃`);

        } catch (error) {
            console.error("丢弃物品时发生错误:", error);
            alert("丢弃物品失败，请重试");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
            
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 恢復原本的 sidebar 狀態
    restoreSidebar() {
        console.log("恢復原本的 sidebar 狀態");

        const sidebar = document.getElementById('sidebar');
        if (sidebar && this.originalSidebarState) {
            // 恢復保存的原始狀態
            sidebar.style.display = this.originalSidebarState.display;
            sidebar.style.visibility = this.originalSidebarState.visibility;
            sidebar.style.opacity = this.originalSidebarState.opacity;
            sidebar.style.zIndex = 9999;
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0px';
            sidebar.style.top = '0px';

            console.log("Sidebar 狀態已恢復到原始狀態:", this.originalSidebarState);
        } else if (sidebar) {
            // 如果沒有保存的狀態，使用默認值
            sidebar.style.display = 'flex';
            sidebar.style.flexDirection = 'column';
            sidebar.style.justifyContent = 'start';
            sidebar.style.alignItems = 'center';
            sidebar.style.justifySelf = 'flex-start';
            sidebar.style.gap = '10px';
            sidebar.style.overflow = 'hidden';
            sidebar.style.visibility = 'visible';
            sidebar.style.opacity = '1';
            sidebar.style.zIndex = '1000';

            console.log("Sidebar 狀態已恢復到默認狀態");
        } else {
            console.warn("找不到 sidebar 元素");
        }

        // 清除保存的狀態
        this.originalSidebarState = null;

        // 如果有營地場景的 sidebar 更新函數，調用它
        if (typeof updateCampSidebar === 'function') {
            updateCampSidebar();
        }

        // 觸發 sidebar 恢復事件，讓其他模組知道
        const sidebarRestoreEvent = new CustomEvent('sidebarRestored', {
            detail: { source: 'hostel' }
        });
        document.dispatchEvent(sidebarRestoreEvent);
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 移除選單面板
        if (this.menuPanel && this.menuPanel.parentNode) {
            this.menuPanel.parentNode.removeChild(this.menuPanel);
            this.menuPanel = null;
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // 恢復原本的 sidebar 狀態
        this.restoreSidebar();

        console.log("客棧場景清理完成");
    }

    // ===== 客棧存檔系統 =====

    // 獲取當前客棧狀態
    getCurrentHostelState() {
        console.log("收集當前客棧狀態");

        // 獲取當前關卡標題
        let levelTitle = "未知關卡";
        if (typeof currentLevel !== 'undefined' && typeof controlLayer !== 'undefined') {
            const levelData = controlLayer[currentLevel];
            if (levelData && levelData["標題"]) {
                levelTitle = levelData["標題"];
            }
        }

        const hostelState = {
            // 基本信息
            timestamp: Date.now(),
            hostelIndex: this.hostelIndex,
            previousScene: this.previousScene,

            // 關卡信息
            levelTitle: levelTitle,
            currentLevel: typeof currentLevel !== 'undefined' ? currentLevel : 0,

            // 玩家數據
            playerData: this.playerData,

            // 客棧特定狀態
            hostelSpecific: {
                isLoaded: this.isLoaded,
                isRunning: this.isRunning
            },

            // 背包和金錢（如果可用）
            inventory: {
                mybag: typeof mybag !== 'undefined' ? [...mybag] : [],
                mymoney: typeof mymoney !== 'undefined' ? mymoney : 0
            }
        };

        console.log("客棧狀態收集完成:", hostelState);
        return hostelState;
    }

    // 保存客棧狀態到指定槽位
    saveHostelState(slotIndex) {
        console.log(`保存客棧狀態到槽位 ${slotIndex}`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return false;
        }

        try {
            const hostelState = this.getCurrentHostelState();
            const storageKey = `hostelSave_${slotIndex}`;

            localStorage.setItem(storageKey, JSON.stringify(hostelState));
            console.log(`客棧狀態已保存到槽位 ${slotIndex}`);
            return true;
        } catch (error) {
            console.error("保存客棧狀態失敗:", error);
            return false;
        }
    }

    // 從指定槽位載入客棧狀態
    loadHostelState(slotIndex) {
        console.log(`從槽位 ${slotIndex} 載入客棧狀態`);

        if (slotIndex < 0 || slotIndex >= 5) {
            console.error("無效的存檔槽位:", slotIndex);
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                console.warn(`槽位 ${slotIndex} 沒有保存的客棧狀態`);
                return null;
            }

            const hostelState = JSON.parse(savedData);
            console.log(`客棧狀態已從槽位 ${slotIndex} 載入`);
            return hostelState;
        } catch (error) {
            console.error("載入客棧狀態失敗:", error);
            return null;
        }
    }

    // 獲取存檔槽位信息
    getHostelSlotInfo(slotIndex) {
        if (slotIndex < 0 || slotIndex >= 5) {
            return null;
        }

        try {
            const storageKey = `hostelSave_${slotIndex}`;
            const savedData = localStorage.getItem(storageKey);

            if (!savedData) {
                return null;
            }

            const hostelState = JSON.parse(savedData);
            return {
                timestamp: hostelState.timestamp,
                hostelIndex: hostelState.hostelIndex,
                previousScene: hostelState.previousScene,
                levelTitle: hostelState.levelTitle || "未知關卡",
                currentLevel: hostelState.currentLevel || 0,
                hasPlayerData: !!hostelState.playerData
            };
        } catch (error) {
            console.error("獲取客棧存檔槽位信息失敗:", error);
            return null;
        }
    }

    // 顯示客棧存檔對話框
    showSaveHostelDialog() {
        console.log("開啟客棧存檔介面");

        // 創建 dialog 元素
        const saveDialog = document.createElement("dialog");
        saveDialog.id = "save-hostel-dialog";
        saveDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const saveContent = document.createElement("div");
        saveContent.className = "save-content";
        saveContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "存取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        saveContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            saveDialog.close();
            this.hostelContainer.removeChild(saveDialog);
        });
        saveContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'save');
        }

        saveContent.appendChild(slotsContainer);
        saveDialog.appendChild(saveContent);
        this.hostelContainer.appendChild(saveDialog);
        saveDialog.showModal();
    }

    // 顯示客棧讀檔對話框（已移至 Init.js 的前历再续功能）
    showLoadHostelDialog() {
        console.log("開啟客棧讀檔介面");

        // 創建 dialog 元素
        const loadDialog = document.createElement("dialog");
        loadDialog.id = "load-hostel-dialog";
        loadDialog.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 94.5%;
            height: 100%;
            background-image: url(./Public/savegamebg.png);
            background-size: cover;
            background-position: center;
            border: none;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            z-index: 10000;
        `;

        // 創建 dialog 內容容器
        const loadContent = document.createElement("div");
        loadContent.className = "save-content";
        loadContent.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            position: relative;
            width: 100%;
            height: 100%;
        `;

        // 添加標題
        const title = document.createElement("div");
        title.className = "save-title";
        title.textContent = "讀取客棧";
        title.style.cssText = `
            font-size: 32px;
            color: rgb(168, 105, 38);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            background-image: url(./Public/showskillbg.png);
            background-size: 100%;
            background-repeat: no-repeat;
            width: 300px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
        `;
        loadContent.appendChild(title);

        // 添加關閉按鈕
        const closeButton = document.createElement("button");
        closeButton.className = "save-close-btn";
        closeButton.textContent = "↩";
        closeButton.style.cssText = `
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 5px 15px;
            font-size: 35px;
            font-weight: 600;
            background-color: rgb(247, 231, 173);
            color: rgb(168, 105, 38);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            cursor: pointer;
        `;
        closeButton.addEventListener("click", () => {
            loadDialog.close();
            this.hostelContainer.removeChild(loadDialog);
        });
        loadContent.appendChild(closeButton);

        // 創建存檔槽容器
        const slotsContainer = document.createElement("div");
        slotsContainer.className = "save-slots-container";
        slotsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 20px;
        `;

        // 創建五個存檔槽
        for (let i = 0; i < 5; i++) {
            this.createSaveSlot(slotsContainer, i, 'load');
        }

        loadContent.appendChild(slotsContainer);
        loadDialog.appendChild(loadContent);
        this.hostelContainer.appendChild(loadDialog);
        loadDialog.showModal();
    }

    // 創建存檔槽
    createSaveSlot(container, slotIndex, mode) {
        const slot = document.createElement("div");
        slot.className = "save-slot";
        slot.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 110px;
            background-color: rgb(247, 231, 173);
            border: 5px solid rgb(165, 90, 24);
            border-radius: 5px;
            padding: 5px;
            cursor: pointer;
            box-sizing: border-box;
            opacity: 0;
        `;

        // 左欄：圖騰
        const totem = document.createElement("div");
        totem.className = "save-slot-totem";
        totem.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const totemImage = document.createElement("img");
        totemImage.draggable = false;
        totemImage.src = "./Public/saveicon.png";
        totemImage.style.cssText = `
            width: 360px;
            height: 95px;
        `;
        totem.appendChild(totemImage);
        slot.appendChild(totem);

        // 中間欄：客棧信息和存檔時間
        const middle = document.createElement("div");
        middle.className = "save-slot-middle";
        middle.style.cssText = `
            width: 30%;
            text-align: center;
        `;
        const hostelInfo = document.createElement("div");
        hostelInfo.className = "save-slot-level";
        hostelInfo.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 25px;
        `;
        const saveTime = document.createElement("div");
        saveTime.className = "save-slot-time";
        saveTime.style.cssText = `
            color: rgb(168, 105, 38);
            font-size: 20px;
        `;

        // 獲取存檔信息
        const slotInfo = this.getHostelSlotInfo(slotIndex);
        if (slotInfo) {
            hostelInfo.style.display = "block";
            hostelInfo.textContent = `客棧 ${slotInfo.hostelIndex}`;
            saveTime.textContent = new Date(slotInfo.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            hostelInfo.style.display = "none";
            saveTime.textContent = "";
        }

        middle.appendChild(hostelInfo);
        middle.appendChild(saveTime);
        slot.appendChild(middle);

        // 右欄：關卡標題
        const levelTitle = document.createElement("div");
        levelTitle.className = "save-slot-title";
        levelTitle.style.cssText = `
            width: 30%;
            text-align: center;
            font-weight: 600;
            color: rgb(168, 105, 38);
            font-size: 28px;
        `;

        // 顯示關卡標題，如果沒有存檔則顯示槽位編號
        if (slotInfo && slotInfo.levelTitle) {
            levelTitle.textContent = slotInfo.levelTitle;
        } else {
            levelTitle.textContent = `第 ${slotIndex + 1} 格`;
        }
        slot.appendChild(levelTitle);

        // 存檔槽點擊事件
        if (mode === 'save') {
            slot.addEventListener("click", () => {
                const confirmSave = confirm(`是否要將當前客棧存檔在第 ${slotIndex + 1} 格的位置？`);
                if (confirmSave) {
                    const success = this.saveHostelState(slotIndex);
                    if (success) {
                        // 更新顯示
                        const newSlotInfo = this.getHostelSlotInfo(slotIndex);
                        if (newSlotInfo) {
                            hostelInfo.style.display = "block";
                            hostelInfo.textContent = `客棧 ${newSlotInfo.hostelIndex}`;
                            saveTime.textContent = new Date(newSlotInfo.timestamp).toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                        alert("客棧存檔成功！");
                    } else {
                        alert("客棧存檔失敗！");
                    }
                }
            });
        } else if (mode === 'load') {
            slot.addEventListener("click", () => {
                if (!slotInfo) {
                    return;
                }

                const confirmLoad = confirm(`是否要讀取第 ${slotIndex + 1} 格的客棧存檔？\n客棧：${slotInfo.hostelIndex}\n時間：${new Date(slotInfo.timestamp).toLocaleString()}`);
                if (confirmLoad) {
                    const hostelState = this.loadHostelState(slotIndex);
                    if (hostelState) {
                        // 關閉對話框
                        const loadDialog = document.getElementById('load-hostel-dialog');
                        if (loadDialog) {
                            loadDialog.close();
                            this.hostelContainer.removeChild(loadDialog);
                        }

                        // 載入客棧場景
                        this.loadHostelScene(hostelState);
                        alert("客棧讀取成功！");
                    } else {
                        alert("客棧讀取失敗！");
                    }
                }
            });
        }

        // 添加動畫
        const delay = slotIndex * 0.2;
        slot.style.animation = `slideIn 0.5s ease-out ${delay}s forwards`;

        container.appendChild(slot);
    }

    // 載入客棧場景
    loadHostelScene(hostelState) {
        console.log("載入客棧場景狀態:", hostelState);

        try {
            // 使用場景管理器切換到客棧場景
            if (typeof sceneManager !== 'undefined') {
                sceneManager.switchToHostel(hostelState.hostelIndex, hostelState.previousScene);
                console.log("客棧場景載入成功");
            } else {
                console.error("場景管理器不可用");
                alert("無法載入客棧場景");
            }
        } catch (error) {
            console.error("載入客棧場景失敗:", error);
            alert("載入客棧場景失敗");
        }
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}