/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 設置 sidebar 按鈕支持
            this.setupSidebarSupport();

            // 設置音樂
            this.setupMusic();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('./Public/Shop/shop.gif');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理選單相關按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    this.toggleHostelMenu();
                    break;
                case 'Enter':
                case ' ':
                    if (this.isMenuOpen) {
                        this.handleMenuSelection();
                    } else {
                        this.toggleHostelMenu();
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 設置 sidebar 支持
    setupSidebarSupport() {
        console.log("設置客棧場景 sidebar 支持");

        // 確保 sidebar 可見
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.style.display = 'block';
            sidebar.style.zIndex = '1000';
        }

        // 設置客棧專用的 sidebar 按鈕
        this.updateSidebarForHostel();
    }

    // 更新 sidebar 為客棧模式
    updateSidebarForHostel() {
        console.log("更新 sidebar 為客棧模式");

        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        // 清空現有內容
        sidebar.innerHTML = '';

        // 創建客棧專用按鈕
        const hostelButtons = [
            {
                text: '角色狀態',
                onclick: () => this.showCharacterStatus()
            },
            {
                text: '加點數',
                onclick: () => this.showLevelUpMenu()
            },
            {
                text: '存檔',
                onclick: () => this.showSaveMenu()
            },
            {
                text: '客棧選單',
                onclick: () => this.toggleHostelMenu()
            }
        ];

        // 根據來源場景添加額外按鈕
        if (this.previousScene !== 'camp') {
            // 只有直接進入客棧才顯示「前往下一關」
            hostelButtons.push({
                text: '前往下一關',
                onclick: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            // 從營地來的話，添加返回營地按鈕
            hostelButtons.push({
                text: '返回營地',
                onclick: () => this.returnToCamp()
            });
        }

        // 創建按鈕元素
        hostelButtons.forEach(buttonConfig => {
            const button = document.createElement('button');
            button.textContent = buttonConfig.text;
            button.style.cssText = `
                width: 100%;
                padding: 10px;
                margin: 5px 0;
                background: #4a4a4a;
                color: white;
                border: 1px solid #666;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            `;
            button.onclick = buttonConfig.onclick;
            sidebar.appendChild(button);
        });

        console.log("客棧 sidebar 更新完成");
    }



    // 切換客棧選單
    toggleHostelMenu() {
        console.log("切換客棧選單");

        if (this.isMenuOpen) {
            this.closeHostelMenu();
        } else {
            this.openHostelMenu();
        }
    }

    // 開啟客棧選單
    openHostelMenu() {
        console.log("開啟客棧選單");

        // 暫停鍵盤控制
        this.keyboardPaused = true;

        // 創建選單 UI
        this.createHostelMenuUI();

        this.isMenuOpen = true;
    }

    // 關閉客棧選單
    closeHostelMenu() {
        console.log("關閉客棧選單");

        // 恢復鍵盤控制
        this.keyboardPaused = false;

        // 移除選單 UI
        if (this.hostelMenu) {
            this.hostelMenu.remove();
            this.hostelMenu = null;
        }

        this.isMenuOpen = false;
    }

    // 創建客棧選單 UI
    createHostelMenuUI() {
        console.log("創建客棧選單 UI");

        // 移除現有選單
        if (this.hostelMenu) {
            this.hostelMenu.remove();
        }

        // 創建選單容器
        this.hostelMenu = document.createElement('div');
        this.hostelMenu.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 3px solid #FFD700;
            border-radius: 15px;
            padding: 30px;
            z-index: 10;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            min-width: 350px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        // 選單標題
        const title = document.createElement('h2');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 25px 0;
            text-align: center;
            color: #FFD700;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        `;
        this.hostelMenu.appendChild(title);

        // 選單選項
        const menuOptions = [
            { text: '📊 角色狀態', action: () => this.showCharacterStatus() },
            { text: '⬆️ 加點數', action: () => this.showLevelUpMenu() },
            { text: '💾 存檔', action: () => this.showSaveMenu() }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({ text: '🚀 前往下一關', action: () => this.goToNextLevel() });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({ text: '🏕️ 返回營地', action: () => this.returnToCamp() });
        }

        menuOptions.push({ text: '❌ 關閉選單', action: () => this.closeHostelMenu() });

        // 創建選項按鈕
        menuOptions.forEach((option) => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 15px;
                margin: 8px 0;
                background: linear-gradient(145deg, #5a5a5a, #3a3a3a);
                color: white;
                border: 2px solid #666;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            `;

            button.onmouseover = () => {
                button.style.background = 'linear-gradient(145deg, #FFD700, #FFA500)';
                button.style.color = '#000';
                button.style.transform = 'translateY(-2px)';
                button.style.boxShadow = '0 6px 12px rgba(255,215,0,0.4)';
            };

            button.onmouseout = () => {
                button.style.background = 'linear-gradient(145deg, #5a5a5a, #3a3a3a)';
                button.style.color = 'white';
                button.style.transform = 'translateY(0)';
                button.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
            };

            button.onclick = () => {
                this.closeHostelMenu();
                option.action();
            };
            this.hostelMenu.appendChild(button);
        });

        this.hostelContainer.appendChild(this.hostelMenu);
        console.log("客棧選單 UI 創建完成");
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示存檔選單");

        // 調用現有的存檔系統
        if (typeof showSaveInterface === 'function') {
            showSaveInterface();
        } else if (typeof saveGameFunc === 'function') {
            saveGameFunc();
        } else {
            alert("存檔功能暫未實現");
        }
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof goToNextLevel === 'function') {
            goToNextLevel();
        } else if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 關閉選單
        if (this.isMenuOpen) {
            this.closeHostelMenu();
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        console.log("客棧場景清理完成");
    }
}