/**
 * 客棧場景類 - 簡化版本，使用 DOM 元素和全屏背景
 */
class HostelScene {
    constructor(hostelIndex = 0, playerData = {}, previousScene = null) {
        this.hostelIndex = hostelIndex;
        this.playerData = playerData;
        this.previousScene = previousScene; // 記錄來源場景

        // DOM 元素設置
        this.hostelContainer = null;
        this.backgroundElement = null;

        // 載入狀態
        this.isLoaded = false;
        this.isRunning = false;

        // 客棧特殊功能
        this.hostelMenu = null;
        this.isMenuOpen = false;

        // 鍵盤控制
        this.keydownHandler = null;
        this.keyupHandler = null;
        this.keyboardPaused = false;

        console.log("HostelScene 初始化", {
            hostelIndex,
            previousScene: this.previousScene
        });
    }

    // 初始化客棧場景
    async init() {
        console.log("初始化客棧場景");

        try {
            // 創建客棧 DOM 容器
            this.createHostelContainer();

            // 設置全屏背景
            this.setupBackground();

            // 設置客棧專用控制
            this.setupHostelControls();

            // 創建客棧專用選單面板
            this.createHostelMenuPanel();

            // 設置音樂
            this.setupMusic();

            // 顯示歡迎訊息
            this.showWelcomeMessage();

            // 標記為已載入
            this.isLoaded = true;
            this.isRunning = true;

            console.log("客棧場景初始化完成");

        } catch (error) {
            console.error("客棧場景初始化失敗:", error);
            throw error;
        }
    }

    // 創建客棧 DOM 容器
    createHostelContainer() {
        console.log("創建客棧 DOM 容器");

        // 隱藏 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 創建客棧主容器
        this.hostelContainer = document.createElement('div');
        this.hostelContainer.id = 'hostelContainer';
        this.hostelContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            overflow: hidden;
            background: rgb(0, 0, 0);
        `;

        document.body.appendChild(this.hostelContainer);
        console.log("客棧 DOM 容器創建完成");
    }

    // 設置全屏背景
    setupBackground() {
        console.log("設置客棧全屏背景");

        // 創建背景元素
        this.backgroundElement = document.createElement('div');
        this.backgroundElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url(./Public/Hostel/${this.hostelIndex}/bg.gif);
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        `;

        this.hostelContainer.appendChild(this.backgroundElement);
        console.log("客棧背景設置完成");
    }

    // 顯示歡迎訊息
    showWelcomeMessage() {
        console.log("顯示客棧歡迎訊息");

        // 創建歡迎訊息元素
        const welcomeMessage = document.createElement('div');
        welcomeMessage.style.cssText = `
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(165, 90, 24);
            border-radius: 15px;
            padding: 30px;
            z-index: 5;
            color: white;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        `;

        const welcomeText = this.previousScene === 'camp'
            ? '🏕️ 歡迎從營地來到客棧！'
            : '🏨 歡迎來到客棧！';

        const instructionText = this.previousScene === 'camp'
            ? '您可以在這裡休息、整理裝備，完成後可返回營地。'
            : '您可以在這裡休息、整理裝備，完成後可前往下一關。';

        welcomeMessage.innerHTML = `
            <h2 style="margin: 0 0 20px 0; color: rgb(168, 105, 38);; font-size: 24px;">
                ${welcomeText}
            </h2>
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;color: rgb(168, 105, 38);">
                ${instructionText}
            </p>
        `;

        this.hostelContainer.appendChild(welcomeMessage);

        // 3秒後自動隱藏歡迎訊息
        setTimeout(() => {
            if (welcomeMessage.parentNode) {
                welcomeMessage.style.transition = 'opacity 1s ease-out';
                welcomeMessage.style.opacity = '0';
                setTimeout(() => {
                    if (welcomeMessage.parentNode) {
                        welcomeMessage.remove();
                    }
                }, 1000);
            }
        }, 3000);

        console.log("客棧歡迎訊息顯示完成");
    }

    // 設置音樂
    setupMusic() {
        console.log("設置客棧場景音樂");

        // 播放客棧背景音樂（可以使用商店音樂或專用音樂）
        try {
            if (typeof operates !== 'undefined' && operates.playBGM) {
                operates.playBGM("./Music/shop.mp3"); // 暫時使用商店音樂
                console.log("客棧背景音樂播放成功");
            }
        } catch (error) {
            console.error("客棧背景音樂播放失敗:", error);
        }
    }

    // 設置客棧專用控制
    setupHostelControls() {
        console.log("設置客棧控制系統");

        // 移除任何現有的事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        // 設置新的事件監聽器（只處理基本按鍵）
        this.keydownHandler = (event) => {
            if (this.keyboardPaused) return;

            switch (event.key) {
                case 'Escape':
                    // ESC 鍵返回營地或上一個場景
                    if (this.previousScene === 'camp') {
                        this.returnToCamp();
                    } else {
                        console.log("按下 ESC，但無明確的返回目標");
                    }
                    break;
                // 禁用所有移動按鍵
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                case 'w':
                case 'a':
                case 's':
                case 'd':
                    event.preventDefault();
                    console.log("客棧中無法移動");
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        console.log("客棧控制系統設置完成");
    }

    // 創建客棧專用選單面板
    createHostelMenuPanel() {
        console.log("創建客棧專用選單面板");

        // 創建選單面板容器
        this.menuPanel = document.createElement('div');
        this.menuPanel.id = 'hostelMenuPanel';
        this.menuPanel.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            color: rgb(168, 105, 38);
            background: rgb(247, 231, 173);
            border: 4px solid rgb(168, 105, 38);
            border-radius: 10px;
            padding: 20px;
            z-index: 5;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        `;

        // 創建選單標題
        const title = document.createElement('h3');
        title.textContent = '🏨 客棧選單';
        title.style.cssText = `
            margin: 0 0 20px 0;
            text-align: center;
            color: rgb(168, 105, 38);
            font-size: 20px;
            border-bottom: 2px solid rgb(168, 105, 38);
            padding-bottom: 10px;
        `;
        this.menuPanel.appendChild(title);

        // 創建選單選項
        const menuOptions = [
            {
                text: '📊 角色狀態',
                action: () => this.showCharacterStatus()
            },
            {
                text: '⬆️ 加點數',
                action: () => this.showLevelUpMenu()
            },
            {
                text: '💾 存檔',
                action: () => this.showSaveMenu()
            }
        ];

        // 根據來源場景添加額外選項
        if (this.previousScene !== 'camp') {
            menuOptions.push({
                text: '🚀 前往下一關',
                action: () => this.goToNextLevel()
            });
        }

        if (this.previousScene === 'camp') {
            menuOptions.push({
                text: '🏕️ 返回營地',
                action: () => this.returnToCamp()
            });
        }

        // 創建選項按鈕
        menuOptions.forEach(option => {
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                width: 100%;
                padding: 12px;
                margin: 8px 0;
                color: rgb(168, 105, 38);
                background: rgb(247, 231, 173);
                border: 2px solid rgb(168, 105, 38);
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                transition: all 0.3s ease;
                text-align: left;
            `;

            // 按鈕懸停效果
            button.onmouseover = () => {
                button.style.backgroundColor = 'rgb(185, 110, 44)';
                button.style.borderColor = 'rgb(188, 125, 58)';
                button.style.transform = 'translateX(5px)';
            };

            button.onmouseout = () => {
                button.style.backgroundColor = 'rgb(165, 90, 24)';
                button.style.borderColor = 'rgb(168, 105, 38)';
                button.style.transform = 'translateX(0)';
            };

            button.onclick = option.action;
            this.menuPanel.appendChild(button);
        });

        this.hostelContainer.appendChild(this.menuPanel);
        console.log("客棧選單面板創建完成");
    }









    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 顯示角色狀態
    showCharacterStatus() {
        console.log("顯示角色狀態");

        // 調用現有的角色狀態系統
        if (typeof showPlayerStatus === 'function') {
            showPlayerStatus();
        } else {
            alert("角色狀態功能暫未實現");
        }
    }

    // 顯示加點數選單
    showLevelUpMenu() {
        console.log("顯示加點數選單");

        // 調用現有的加點數系統
        if (typeof showLevelUpInterface === 'function') {
            showLevelUpInterface();
        } else {
            alert("加點數功能暫未實現");
        }
    }

    // 顯示存檔選單
    showSaveMenu() {
        console.log("顯示存檔選單");

        // 調用現有的存檔系統
        if (typeof showSaveInterface === 'function') {
            showSaveInterface();
        } else if (typeof saveGameFunc === 'function') {
            saveGameFunc();
        } else {
            alert("存檔功能暫未實現");
        }
    }

    // 前往下一關
    goToNextLevel() {
        console.log("從客棧前往下一關");

        if (typeof goToNextLevel === 'function') {
            goToNextLevel();
        } else if (typeof sceneManager !== 'undefined') {
            const nextLevel = (typeof currentLevel !== 'undefined' ? currentLevel : 0) + 1;
            sceneManager.switchToLevel(nextLevel);
        } else {
            alert("無法前往下一關");
        }
    }

    // 返回營地
    returnToCamp() {
        console.log("從客棧返回營地");

        if (typeof sceneManager !== 'undefined') {
            const campIndex = typeof currentLevel !== 'undefined' ? currentLevel : 0;
            sceneManager.switchToCamp(campIndex);
        } else {
            alert("無法返回營地");
        }
    }

    // 處理選單選擇（保留用於鍵盤導航）
    handleMenuSelection() {
        console.log("處理客棧選單選擇");
        // 這個方法可以用於鍵盤導航選單，現在選單是固定展開的
    }

    // 清理場景
    cleanup() {
        console.log("清理客棧場景");

        // 停止運行
        this.isRunning = false;

        // 移除事件監聽器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
            this.keydownHandler = null;
        }

        // 移除選單面板
        if (this.menuPanel && this.menuPanel.parentNode) {
            this.menuPanel.parentNode.removeChild(this.menuPanel);
            this.menuPanel = null;
        }

        // 移除 DOM 容器
        if (this.hostelContainer && this.hostelContainer.parentNode) {
            this.hostelContainer.parentNode.removeChild(this.hostelContainer);
            this.hostelContainer = null;
        }

        // 顯示 Canvas
        const canvas = document.getElementById('GameScreen');
        if (canvas) {
            canvas.style.display = 'block';
        }

        console.log("客棧場景清理完成");
    }
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}

// 確保 HostelScene 類別在全局範圍內可用
if (typeof window !== 'undefined') {
    window.HostelScene = HostelScene;
    console.log("HostelScene 類別已註冊到全局範圍");

    // 觸發自定義事件通知其他模組 HostelScene 已準備就緒
    const hostelSceneReadyEvent = new CustomEvent('hostelSceneReady', {
        detail: { HostelScene: HostelScene }
    });
    document.dispatchEvent(hostelSceneReadyEvent);
}

// 如果在 Node.js 環境中，也要導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HostelScene;
}