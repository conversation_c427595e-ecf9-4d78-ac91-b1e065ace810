#GameMap {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #000;
    overflow: scroll;
    scroll-behavior: smooth;
    /* scrollbar-width: thin !important; */
}

#GameBoard {
    background-image: url('../Public/Map/0.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: grid;
}

/*
.obstacle{
    background: rgba(255, 0, 0, 0.5);
}
*/
.cell {
    /* background-color: rgba(0, 0, 0, 0.5); */
    /*border: 1px solid #000;*/
    color: wheat;
    background-repeat: no-repeat;
    /*background-size: contain;*/
    background-position: bottom;
    display: flex;
    justify-content: center;
    align-items: end;
    overflow: visible;
    position: relative;
}

.treasure {
    background-image: url(../Public/treasure.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    animation: treasureani 3s alternate infinite;

}

@keyframes treasureani {
    0% {
        filter: brightness(1);
    }

    50% {
        filter: brightness(1.5);
    }

    100% {
        filter: brightness(1);
    }
}




/*玩家攻擊與移動操作的顯示範圍(注意：這裡每增加新的角色資料都要在這裡添加樣式)*/
.player {
    cursor: pointer;
    box-sizing: border-box;
}


.playercanmove0 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark0 1.5s infinite alternate;
}

.playercanmove1 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playercanmove2 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playercanatk0 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playercanatk1 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playercanatk2 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}


.playermagicrange0 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playermagicrange殷劍平 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playermagicrange封寒月 {
    cursor: pointer;
    background-color: rgba(255, 0, 255, 0.5);
    animation: lightanddark1 1.5s infinite alternate;
}

.playermagicrangework殷劍平 {
    cursor: pointer;
    background-color: rgba(255, 153, 0, 0.881);
    animation: none;
}

.playermagicrangework封寒月 {
    cursor: pointer;
    background-color: rgba(255, 153, 0, 0.881);
    animation: none;
}

/*敵人*/
.enemy {
    cursor: pointer;
}

.enemycanmove {
    cursor: pointer;
    background-color: rgba(255, 0, 0, 0.5);
}



@keyframes lightanddark0 {
    from {
        background-color: rgba(255, 0, 255, 0.2);
    }

    to {
        background-color: rgba(255, 0, 255, 0.5);
    }
}

@keyframes lightanddark1 {
    from {
        background-color: rgba(255, 0, 255, 0.2);
    }

    to {
        background-color: rgba(255, 0, 255, 0.5);
    }
}

@keyframes lightanddark9 {
    from {
        background-color: rgba(255, 153, 0, 0.26);
    }

    to {
        background-color: rgba(255, 153, 0, 0.881);
    }
}

/* ========================================================================== */


#options {
    position: absolute;
    top: -30px;
    width: 300px;
    height: 40px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: row !important;
    animation: appear 1s forwards;
}

.menu {
    border: 3px solid black;
    box-shadow: 0 0 10px 0px rgba(0, 0, 0, .5);
    background: rgb(247, 231, 173);
    width: 55px;
    height: 40px;
    border-radius: 15px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.menu:hover {
    transform: scale(1.1);
    transform-origin: center;
    animation: largeansmall 0.5s infinite alternate;
}

#atkbtn {
    background-image: url('../icons/atk.png');
}

#magicbtn {
    background-image: url('../icons/magic.png');
}

#bagbtn {
    background-image: url('../icons/bag.png');
}

#awaitbtn {
    background-image: url('../icons/await.png');
}

#strengthbtn {
    background-image: url('../icons/strength.png');
}

@keyframes appear {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }

}

@keyframes largeansmall {
    from {
        transform: scale(1);
    }

    to {
        transform: scale(1.1);
    }
}

/*==========================================================================*/

#InfoDialog {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

#ifnodialog_inner {
    width: 75%;
    height: 90%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
    outline: none;
    border: none;
    background: none;
}

#ifnodialog_inner::backdrop {
    backdrop-filter: blur(10px);
}

#playerinfo {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    background: rgb(247, 231, 173);
    border: 6px solid rgb(165, 90, 24);
    box-shadow: 0 0 10px 0px rgba(0, 0, 0, .5);
    color: rgb(168, 105, 38);
    font-family: '微軟正黑體';
}

#closeinfobtn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 30px;
    height: 30px;
    background-image: url('../icons/cancel.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.3s;
    transform-origin: center;
}

#closeinfobtn:hover {
    transform: scale(1.02);
}

#playerinfo_inner {
    width: 95%;
    height: 99%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    gap: 10px;
}

#playerinfo_left {
    width: 42%;
    height: 99%;
    display: flex;
    justify-content: start;
    align-items: center;
    flex-direction: column;
    padding: 0 5px;
    box-shadow: inset 0 0 10px 0px rgba(0, 0, 0, .5);
    border-radius: 10px;
}

#playerinfo_right {
    width: 58%;
    height: 99%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    box-shadow: inset 0 0 10px 0px rgba(0, 0, 0, .5);
    border-radius: 10px;
}

#playerinfo_left_up_right {
    width: 40%;
    height: 60%;
    display: flex;
    justify-content: end;
    align-items: end;
    flex-direction: column;
    gap: 10px;
}

#playerinfo_left_up_right img {
    height: 180px;
    object-fit: contain;
}

#playerinfo_left_up {
    width: 100%;
    height: 40%;
    display: flex;
    justify-content: center;
    align-items: end;
    flex-direction: row;
    gap: 10px;
}

#playerinfo_left_up_left {
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: column;
    user-select: none;
    gap: 10px;
}

#playerinfo_left_up_left1 {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    align-items: center;
    flex-direction: row;
    gap: 10px;
}

#playerinfo_name {
    font-size: 30px;
    color: rgb(207, 40, 40);
    font-weight: bold;
}

#playerinfo_level {
    font-size: 20px;
    font-weight: 600;
}

#playerinfo_atk {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    flex-direction: row;
    white-space: nowrap;
    gap: 5px;
    font-size: 22px;
}

#playerinfo_def {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    flex-direction: row;
    white-space: nowrap;
    gap: 5px;
    font-size: 22px;
}

#playerinfo_move {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    flex-direction: row;
    white-space: nowrap;
    gap: 5px;
    font-size: 22px;
}

#playerinfo_exp {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    flex-direction: row;
    white-space: nowrap;
    gap: 5px;
    font-size: 22px;
}

#playerinfo_left_down {
    width: 100%;
    height: 40%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 35px;
}

#playerinfo_hp {
    width: 100%;
    height: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

}

#playerinfo_hp_up {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

#playerhpbar {
    width: 350px;
    height: 15px;
    background-color: rgb(156, 16, 0);
    border-left: 5px solid rgb(255, 156, 8);
    border-bottom: 4px solid rgb(255, 156, 8);
    border-right: 5px solid rgb(57, 0, 0);
    border-top: 4px solid rgb(57, 0, 0);
}


#playerhpbar_inner {
    width: 100%;
    height: 100%;
    background: rgb(255, 255, 90);
    border-right: 2px solid rgb(156, 16, 0);
    box-shadow: 0 0 10px 0px rgba(0, 0, 0, .5);
    transition: all 0.3s ease;
}

#playerinfo_mp {
    width: 100%;
    height: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
}

#playerinfo_mp_up {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

#playermpbar {
    width: 350px;
    height: 15px;
    background-color: rgb(66, 66, 155);
    border-left: 5px solid rgb(253, 252, 230);
    border-bottom: 4px solid rgb(253, 252, 230);
    border-right: 5px solid rgb(24, 24, 41);
    border-top: 4px solid rgb(24, 24, 41);
}

#playermpbar_inner {
    width: 100%;
    height: 100%;
    background: rgb(165, 181, 222);
    border-right: 2px solid rgb(66, 66, 155);
    box-shadow: 0 0 10px 0px rgba(255, 255, 255, .5);
    transition: all 0.3s ease;
}

/*==========================================================================*/